(()=>{var e={};e.id=220;e.ids=[220];e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3660:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>o});var s=t(2907);var n=t.n(s);const o=(0,s.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(auth)\\\\sign-up\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},4793:(e,r,t)=>{"use strict";t.r(r);t.d(r,{GlobalError:()=>a.a,__next_app__:()=>_,pages:()=>P,routeModule:()=>A,tree:()=>w});var s=t(5239);var n=t.n(s);var o=t(8088);var i=t(8170);var a=t.n(i);var l=t(893);var d=t.n(l);var u={};for(const e in l)if(["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)<0)u[e]=()=>l[e];t.d(r,u);const c=()=>Promise.resolve().then(t.bind(t,8014));const p=()=>Promise.resolve().then(t.t.bind(t,7398,23));const m=()=>Promise.resolve().then(t.t.bind(t,9999,23));const h=()=>Promise.resolve().then(t.t.bind(t,5284,23));const v=()=>Promise.resolve().then(t.bind(t,7470));const x=()=>Promise.resolve().then(t.t.bind(t,7398,23));const g=()=>Promise.resolve().then(t.t.bind(t,9999,23));const f=()=>Promise.resolve().then(t.t.bind(t,5284,23));const b=()=>Promise.resolve().then(t.bind(t,3660));const w={children:["",{children:["(auth)",{children:["sign-up",{children:["__PAGE__",{},{page:[b,"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx"]}]},{}]},{"layout":[v,"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx"],"not-found":[x,"next/dist/client/components/not-found-error"],"forbidden":[g,"next/dist/client/components/forbidden-error"],"unauthorized":[f,"next/dist/client/components/unauthorized-error"]}]},{"layout":[c,"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[p,"next/dist/client/components/not-found-error"],"forbidden":[m,"next/dist/client/components/forbidden-error"],"unauthorized":[h,"next/dist/client/components/unauthorized-error"]}]}.children;const P=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx"];const j=t;const y=()=>Promise.resolve();const _={require:j,loadChunk:y};const A=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(auth)/sign-up/page",pathname:"/sign-up",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:w}})},5414:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>c});var s=t(687);var n=t.n(s);var o=t(3210);var i=t.n(o);var a=t(474);var l=t(2977);var d=t(9360);const u=()=>{const e={email:"Enter your email address",name:"Enter your full name",password:"Enter your password",confirmPassword:"Confirm your password"};return(0,s.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)(a["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,s.jsxs)("div",{className:"lg:hidden",children:[(0,s.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,s.jsx)(a["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,s.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,s.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,s.jsx)(l.A,{formType:"SIGN_UP",schema:d.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:true,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})]}),(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,s.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsx)(l.A,{formType:"SIGN_UP",schema:d.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:true,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})})})]})};const c=u},5511:e=>{"use strict";e.exports=require("crypto")},5607:(e,r,t)=>{Promise.resolve().then(t.bind(t,5414))},8655:(e,r,t)=>{Promise.resolve().then(t.bind(t,3660))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e);var s=r.X(0,[97,423,598,23,937,814,338],()=>t(4793));module.exports=s})();