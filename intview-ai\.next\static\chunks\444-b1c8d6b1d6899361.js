(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[444],{646:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(9946);const i=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]];const o=(0,n.A)("circle-check-big",i)},736:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e["default"]=void 0;var n=a(r(5501));var i=r(4783);var o=r(9070);function a(t){return t&&t.__esModule?t:{"default":t}}function s(t,e,r,n,i,o,a){try{var s=t[o](a);var c=s.value}catch(t){r(t);return}if(s.done){e(c)}else{Promise.resolve(c).then(n,i)}}function c(t){return function(){var e=this,r=arguments;return new Promise(function(n,i){var o=t.apply(e,r);function a(t){s(o,n,i,a,c,"next",t)}function c(t){s(o,n,i,a,c,"throw",t)}a(undefined)})}}function u(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(t,n.key,n)}}function f(t,e,r){if(e)l(t.prototype,e);if(r)l(t,r);return t}var h=function(){function t(e){u(this,t);this.recognition=null;this.pauseAfterDisconnect=false;this.interimTranscript="";this.finalTranscript="";this.listening=false;this.isMicrophoneAvailable=true;this.subscribers={};this.onStopListening=function(){};this.previousResultWasFinalOnly=false;this.resetTranscript=this.resetTranscript.bind(this);this.startListening=this.startListening.bind(this);this.stopListening=this.stopListening.bind(this);this.abortListening=this.abortListening.bind(this);this.setSpeechRecognition=this.setSpeechRecognition.bind(this);this.disableRecognition=this.disableRecognition.bind(this);this.setSpeechRecognition(e);if((0,n["default"])()){this.updateFinalTranscript=(0,i.debounce)(this.updateFinalTranscript,250,true)}}f(t,[{key:"setSpeechRecognition",value:function t(t){var e=!!t&&((0,o.isNative)(t)||(0,i.browserSupportsPolyfills)());if(e){this.disableRecognition();this.recognition=new t;this.recognition.continuous=false;this.recognition.interimResults=true;this.recognition.onresult=this.updateTranscript.bind(this);this.recognition.onend=this.onRecognitionDisconnect.bind(this);this.recognition.onerror=this.onError.bind(this)}this.emitBrowserSupportsSpeechRecognitionChange(e)}},{key:"subscribe",value:function t(t,e){this.subscribers[t]=e}},{key:"unsubscribe",value:function t(t){delete this.subscribers[t]}},{key:"emitListeningChange",value:function t(t){var e=this;this.listening=t;Object.keys(this.subscribers).forEach(function(r){var n=e.subscribers[r].onListeningChange;n(t)})}},{key:"emitMicrophoneAvailabilityChange",value:function t(t){var e=this;this.isMicrophoneAvailable=t;Object.keys(this.subscribers).forEach(function(r){var n=e.subscribers[r].onMicrophoneAvailabilityChange;n(t)})}},{key:"emitTranscriptChange",value:function t(t,e){var r=this;Object.keys(this.subscribers).forEach(function(n){var i=r.subscribers[n].onTranscriptChange;i(t,e)})}},{key:"emitClearTranscript",value:function t(){var t=this;Object.keys(this.subscribers).forEach(function(e){var r=t.subscribers[e].onClearTranscript;r()})}},{key:"emitBrowserSupportsSpeechRecognitionChange",value:function t(t){var e=this;Object.keys(this.subscribers).forEach(function(r){var n=e.subscribers[r],i=n.onBrowserSupportsSpeechRecognitionChange,o=n.onBrowserSupportsContinuousListeningChange;i(t);o(t)})}},{key:"disconnect",value:function t(t){if(this.recognition&&this.listening){switch(t){case"ABORT":this.pauseAfterDisconnect=true;this.abort();break;case"RESET":this.pauseAfterDisconnect=false;this.abort();break;case"STOP":default:this.pauseAfterDisconnect=true;this.stop()}}}},{key:"disableRecognition",value:function t(){if(this.recognition){this.recognition.onresult=function(){};this.recognition.onend=function(){};this.recognition.onerror=function(){};if(this.listening){this.stopListening()}}}},{key:"onError",value:function t(t){if(t&&t.error&&t.error==="not-allowed"){this.emitMicrophoneAvailabilityChange(false);this.disableRecognition()}}},{key:"onRecognitionDisconnect",value:function t(){this.onStopListening();this.listening=false;if(this.pauseAfterDisconnect){this.emitListeningChange(false)}else if(this.recognition){if(this.recognition.continuous){this.startListening({continuous:this.recognition.continuous})}else{this.emitListeningChange(false)}}this.pauseAfterDisconnect=false}},{key:"updateTranscript",value:function t(t){var e=t.results,r=t.resultIndex;var o=r===undefined?e.length-1:r;this.interimTranscript="";this.finalTranscript="";for(var a=o;a<e.length;++a){if(e[a].isFinal&&(!(0,n["default"])()||e[a][0].confidence>0)){this.updateFinalTranscript(e[a][0].transcript)}else{this.interimTranscript=(0,i.concatTranscripts)(this.interimTranscript,e[a][0].transcript)}}var s=false;if(this.interimTranscript===""&&this.finalTranscript!==""){if(this.previousResultWasFinalOnly){s=true}this.previousResultWasFinalOnly=true}else{this.previousResultWasFinalOnly=false}if(!s){this.emitTranscriptChange(this.interimTranscript,this.finalTranscript)}}},{key:"updateFinalTranscript",value:function t(t){this.finalTranscript=(0,i.concatTranscripts)(this.finalTranscript,t)}},{key:"resetTranscript",value:function t(){this.disconnect("RESET")}},{key:"startListening",value:function(){var t=c(regeneratorRuntime.mark(function t(){var e,r,n,i,o,a,s=arguments;return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:e=s.length>0&&s[0]!==undefined?s[0]:{},r=e.continuous,n=r===void 0?false:r,i=e.language;if(this.recognition){t.next=3;break}return t.abrupt("return");case 3:o=n!==this.recognition.continuous;a=i&&i!==this.recognition.lang;if(!(o||a)){t.next=11;break}if(!this.listening){t.next=9;break}t.next=9;return this.stopListening();case 9:this.recognition.continuous=o?n:this.recognition.continuous;this.recognition.lang=a?i:this.recognition.lang;case 11:if(this.listening){t.next=22;break}if(!this.recognition.continuous){this.resetTranscript();this.emitClearTranscript()}t.prev=13;t.next=16;return this.start();case 16:this.emitListeningChange(true);t.next=22;break;case 19:t.prev=19;t.t0=t["catch"](13);if(!(t.t0 instanceof DOMException)){this.emitMicrophoneAvailabilityChange(false)}case 22:case"end":return t.stop()}}},t,this,[[13,19]])}));function e(){return t.apply(this,arguments)}return e}()},{key:"abortListening",value:function(){var t=c(regeneratorRuntime.mark(function t(){var e=this;return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:this.disconnect("ABORT");this.emitListeningChange(false);t.next=4;return new Promise(function(t){e.onStopListening=t});case 4:case"end":return t.stop()}}},t,this)}));function e(){return t.apply(this,arguments)}return e}()},{key:"stopListening",value:function(){var t=c(regeneratorRuntime.mark(function t(){var e=this;return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:this.disconnect("STOP");this.emitListeningChange(false);t.next=4;return new Promise(function(t){e.onStopListening=t});case 4:case"end":return t.stop()}}},t,this)}));function e(){return t.apply(this,arguments)}return e}()},{key:"getRecognition",value:function t(){return this.recognition}},{key:"start",value:function(){var t=c(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:if(!(this.recognition&&!this.listening)){t.next=4;break}t.next=3;return this.recognition.start();case 3:this.listening=true;case 4:case"end":return t.stop()}}},t,this)}));function e(){return t.apply(this,arguments)}return e}()},{key:"stop",value:function t(){if(this.recognition&&this.listening){this.recognition.stop();this.listening=false}}},{key:"abort",value:function t(){if(this.recognition&&this.listening){this.recognition.abort();this.listening=false}}}]);return t}();e["default"]=h},839:()=>{},2138:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(9946);const i=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]];const o=(0,n.A)("arrow-right",i)},2551:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.transcriptReducer=void 0;var n=r(6199);var i=r(4783);var o=function t(t,e){switch(e.type){case n.CLEAR_TRANSCRIPT:return{interimTranscript:"",finalTranscript:""};case n.APPEND_TRANSCRIPT:return{interimTranscript:e.payload.interimTranscript,finalTranscript:(0,i.concatTranscripts)(t.finalTranscript,e.payload.finalTranscript)};default:throw new Error}};e.transcriptReducer=o},3393:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.appendTranscript=e.clearTranscript=void 0;var n=r(6199);var i=function t(){return{type:n.CLEAR_TRANSCRIPT}};e.clearTranscript=i;var o=function t(t,e){return{type:n.APPEND_TRANSCRIPT,payload:{interimTranscript:t,finalTranscript:e}}};e.appendTranscript=o},4516:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(9946);const i=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]];const o=(0,n.A)("map-pin",i)},4783:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.browserSupportsPolyfills=e.compareTwoStringsUsingDiceCoefficient=e.commandToRegExp=e.concatTranscripts=e.debounce=void 0;var r=function t(t,e,r){var n;return function(){var i=this;var o=arguments;var a=function e(){n=null;if(!r)t.apply(i,o)};var s=r&&!n;clearTimeout(n);n=setTimeout(a,e);if(s)t.apply(i,o)}};e.debounce=r;var n=function t(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++){e[r]=arguments[r]}return e.map(function(t){return t.trim()}).join(" ").trim()};e.concatTranscripts=n;var i=/\s*\((.*?)\)\s*/g;var o=/(\(\?:[^)]+\))\?/g;var a=/(\(\?)?:\w+/g;var s=/\*/g;var c=/[-{}[\]+?.,\\^$|#]/g;var u=function t(t){if(t instanceof RegExp){return new RegExp(t.source,"i")}t=t.replace(c,"\\$&").replace(i,"(?:$1)?").replace(a,function(t,e){return e?t:"([^\\s]+)"}).replace(s,"(.*?)").replace(o,"\\s*$1?\\s*");return new RegExp("^"+t+"$","i")};e.commandToRegExp=u;var l=function t(t,e){t=t.replace(/\s+/g,"").toLowerCase();e=e.replace(/\s+/g,"").toLowerCase();if(!t.length&&!e.length)return 1;if(!t.length||!e.length)return 0;if(t===e)return 1;if(t.length===1&&e.length===1)return 0;if(t.length<2||e.length<2)return 0;var r=new Map;for(var n=0;n<t.length-1;n++){var i=t.substring(n,n+2);var o=r.has(i)?r.get(i)+1:1;r.set(i,o)}var a=0;for(var s=0;s<e.length-1;s++){var c=e.substring(s,s+2);var u=r.has(c)?r.get(c):0;if(u>0){r.set(c,u-1);a++}}return 2*a/(t.length+e.length-2)};e.compareTwoStringsUsingDiceCoefficient=l;var f=function t(){return typeof window!=="undefined"&&window.navigator!==undefined&&window.navigator.mediaDevices!==undefined&&window.navigator.mediaDevices.getUserMedia!==undefined&&(window.AudioContext!==undefined||window.webkitAudioContext!==undefined)};e.browserSupportsPolyfills=f},5501:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e["default"]=void 0;var r=function t(){return/(android)/i.test(typeof navigator!=="undefined"?navigator.userAgent:"")};e["default"]=r},5925:t=>{var e=function(t){"use strict";var e=Object.prototype;var r=e.hasOwnProperty;var n=Object.defineProperty||function(t,e,r){t[e]=r.value};var i;var o=typeof Symbol==="function"?Symbol:{};var a=o.iterator||"@@iterator";var s=o.asyncIterator||"@@asyncIterator";var c=o.toStringTag||"@@toStringTag";function u(t,e,r){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true});return t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function l(t,e,r,i){var o=e&&e.prototype instanceof y?e:y;var a=Object.create(o.prototype);var s=new E(i||[]);n(a,"_invoke",{value:C(t,r,s)});return a}t.wrap=l;function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}var h="suspendedStart";var p="suspendedYield";var v="executing";var d="completed";var g={};function y(){}function m(){}function b(){}var w={};u(w,a,function(){return this});var k=Object.getPrototypeOf;var R=k&&k(k(O([])));if(R&&R!==e&&r.call(R,a)){w=R}var T=b.prototype=y.prototype=Object.create(w);m.prototype=b;n(T,"constructor",{value:b,configurable:true});n(b,"constructor",{value:m,configurable:true});m.displayName=u(b,c,"GeneratorFunction");function S(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}t.isGeneratorFunction=function(t){var e=typeof t==="function"&&t.constructor;return e?e===m||(e.displayName||e.name)==="GeneratorFunction":false};t.mark=function(t){if(Object.setPrototypeOf){Object.setPrototypeOf(t,b)}else{t.__proto__=b;u(t,c,"GeneratorFunction")}t.prototype=Object.create(T);return t};t.awrap=function(t){return{__await:t}};function x(t,e){function i(n,o,a,s){var c=f(t[n],t,o);if(c.type==="throw"){s(c.arg)}else{var u=c.arg;var l=u.value;if(l&&typeof l==="object"&&r.call(l,"__await")){return e.resolve(l.__await).then(function(t){i("next",t,a,s)},function(t){i("throw",t,a,s)})}return e.resolve(l).then(function(t){u.value=t;a(u)},function(t){return i("throw",t,a,s)})}}var o;function a(t,r){function n(){return new e(function(e,n){i(t,r,e,n)})}return o=o?o.then(n,n):n()}n(this,"_invoke",{value:a})}S(x.prototype);u(x.prototype,s,function(){return this});t.AsyncIterator=x;t.async=function(e,r,n,i,o){if(o===void 0)o=Promise;var a=new x(l(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})};function C(t,e,r){var n=h;return function i(i,o){if(n===v){throw new Error("Generator is already running")}if(n===d){if(i==="throw"){throw o}return _()}r.method=i;r.arg=o;while(true){var a=r.delegate;if(a){var s=P(a,r);if(s){if(s===g)continue;return s}}if(r.method==="next"){r.sent=r._sent=r.arg}else if(r.method==="throw"){if(n===h){n=d;throw r.arg}r.dispatchException(r.arg)}else if(r.method==="return"){r.abrupt("return",r.arg)}n=v;var c=f(t,e,r);if(c.type==="normal"){n=r.done?d:p;if(c.arg===g){continue}return{value:c.arg,done:r.done}}else if(c.type==="throw"){n=d;r.method="throw";r.arg=c.arg}}}}function P(t,e){var r=e.method;var n=t.iterator[r];if(n===i){e.delegate=null;if(r==="throw"&&t.iterator["return"]){e.method="return";e.arg=i;P(t,e);if(e.method==="throw"){return g}}if(r!=="return"){e.method="throw";e.arg=new TypeError("The iterator does not provide a '"+r+"' method")}return g}var o=f(n,t.iterator,e.arg);if(o.type==="throw"){e.method="throw";e.arg=o.arg;e.delegate=null;return g}var a=o.arg;if(!a){e.method="throw";e.arg=new TypeError("iterator result is not an object");e.delegate=null;return g}if(a.done){e[t.resultName]=a.value;e.next=t.nextLoc;if(e.method!=="return"){e.method="next";e.arg=i}}else{return a}e.delegate=null;return g}S(T);u(T,c,"Generator");u(T,a,function(){return this});u(T,"toString",function(){return"[object Generator]"});function L(t){var e={tryLoc:t[0]};if(1 in t){e.catchLoc=t[1]}if(2 in t){e.finallyLoc=t[2];e.afterLoc=t[3]}this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal";delete e.arg;t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}];t.forEach(L,this);this.reset(true)}t.keys=function(t){var e=Object(t);var r=[];for(var n in e){r.push(n)}r.reverse();return function t(){while(r.length){var n=r.pop();if(n in e){t.value=n;t.done=false;return t}}t.done=true;return t}};function O(t){if(t!=null){var e=t[a];if(e){return e.call(t)}if(typeof t.next==="function"){return t}if(!isNaN(t.length)){var n=-1,o=function e(){while(++n<t.length){if(r.call(t,n)){e.value=t[n];e.done=false;return e}}e.value=i;e.done=true;return e};return o.next=o}}throw new TypeError(typeof t+" is not iterable")}t.values=O;function _(){return{value:i,done:true}}E.prototype={constructor:E,reset:function(t){this.prev=0;this.next=0;this.sent=this._sent=i;this.done=false;this.delegate=null;this.method="next";this.arg=i;this.tryEntries.forEach(A);if(!t){for(var e in this){if(e.charAt(0)==="t"&&r.call(this,e)&&!isNaN(+e.slice(1))){this[e]=i}}}},stop:function(){this.done=true;var t=this.tryEntries[0];var e=t.completion;if(e.type==="throw"){throw e.arg}return this.rval},dispatchException:function(t){if(this.done){throw t}var e=this;function n(r,n){s.type="throw";s.arg=t;e.next=r;if(n){e.method="next";e.arg=i}return!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];var s=a.completion;if(a.tryLoc==="root"){return n("end")}if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc");var u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc){return n(a.catchLoc,true)}else if(this.prev<a.finallyLoc){return n(a.finallyLoc)}}else if(c){if(this.prev<a.catchLoc){return n(a.catchLoc,true)}}else if(u){if(this.prev<a.finallyLoc){return n(a.finallyLoc)}}else{throw new Error("try statement without catch or finally")}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}if(o&&(t==="break"||t==="continue")&&o.tryLoc<=e&&e<=o.finallyLoc){o=null}var a=o?o.completion:{};a.type=t;a.arg=e;if(o){this.method="next";this.next=o.finallyLoc;return g}return this.complete(a)},complete:function(t,e){if(t.type==="throw"){throw t.arg}if(t.type==="break"||t.type==="continue"){this.next=t.arg}else if(t.type==="return"){this.rval=this.arg=t.arg;this.method="return";this.next="end"}else if(t.type==="normal"&&e){this.next=e}return g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t){this.complete(r.completion,r.afterLoc);A(r);return g}}},"catch":function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if(n.type==="throw"){var i=n.arg;A(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){this.delegate={iterator:O(t),resultName:e,nextLoc:r};if(this.method==="next"){this.arg=i}return g}};return t}(true?t.exports:0);try{regeneratorRuntime=e}catch(t){if(typeof globalThis==="object"){globalThis.regeneratorRuntime=e}else{Function("r","regeneratorRuntime = r")(e)}}},6199:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.APPEND_TRANSCRIPT=e.CLEAR_TRANSCRIPT=void 0;var r="CLEAR_TRANSCRIPT";e.CLEAR_TRANSCRIPT=r;var n="APPEND_TRANSCRIPT";e.APPEND_TRANSCRIPT=n},6325:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(9946);const i=[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]];const o=(0,n.A)("briefcase-business",i)},6636:(t,e,r)=>{"use strict";r.d(e,{Hf:()=>m,QF:()=>g});var n=r(2115);var i=function(t,e){i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)if(e.hasOwnProperty(r))t[r]=e[r]};return i(t,e)};function o(t,e){i(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var a=function(){a=Object.assign||function t(t){for(var e,r=1,n=arguments.length;r<n;r++){e=arguments[r];for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i))t[i]=e[i]}return t};return a.apply(this,arguments)};function s(t,e){var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0)r[n]=t[n];if(t!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)if(e.indexOf(n[i])<0)r[n[i]]=t[n[i]]}return r}var c=100;var u=100;var l=50;var f=50;var h=50;function p(t){var e=t.className,r=t.counterClockwise,i=t.dashRatio,o=t.pathRadius,a=t.strokeWidth,s=t.style;return(0,n.createElement)("path",{className:e,style:Object.assign({},s,d({pathRadius:o,dashRatio:i,counterClockwise:r})),d:v({pathRadius:o,counterClockwise:r}),strokeWidth:a,fillOpacity:0})}function v(t){var e=t.pathRadius,r=t.counterClockwise;var n=e;var i=r?1:0;return"\n      M "+f+","+h+"\n      m 0,-"+n+"\n      a "+n+","+n+" "+i+" 1 1 0,"+2*n+"\n      a "+n+","+n+" "+i+" 1 1 0,-"+2*n+"\n    "}function d(t){var e=t.counterClockwise,r=t.dashRatio,n=t.pathRadius;var i=Math.PI*2*n;var o=(1-r)*i;return{strokeDasharray:i+"px "+i+"px",strokeDashoffset:(e?-o:o)+"px"}}var g=function(t){o(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}e.prototype.getBackgroundPadding=function(){if(!this.props.background){return 0}return this.props.backgroundPadding};e.prototype.getPathRadius=function(){return l-this.props.strokeWidth/2-this.getBackgroundPadding()};e.prototype.getPathRatio=function(){var t=this.props,e=t.value,r=t.minValue,n=t.maxValue;var i=Math.min(Math.max(e,r),n);return(i-r)/(n-r)};e.prototype.render=function(){var t=this.props,e=t.circleRatio,r=t.className,i=t.classes,o=t.counterClockwise,a=t.styles,s=t.strokeWidth,v=t.text;var d=this.getPathRadius();var g=this.getPathRatio();return(0,n.createElement)("svg",{className:i.root+" "+r,style:a.root,viewBox:"0 0 "+c+" "+u,"data-test-id":"CircularProgressbar"},this.props.background?(0,n.createElement)("circle",{className:i.background,style:a.background,cx:f,cy:h,r:l}):null,(0,n.createElement)(p,{className:i.trail,counterClockwise:o,dashRatio:e,pathRadius:d,strokeWidth:s,style:a.trail}),(0,n.createElement)(p,{className:i.path,counterClockwise:o,dashRatio:g*e,pathRadius:d,strokeWidth:s,style:a.path}),v?(0,n.createElement)("text",{className:i.text,style:a.text,x:f,y:h},v):null)};e.defaultProps={background:false,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:false,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""};return e}(n.Component);function y(t){var e=t.children,r=s(t,["children"]);return createElement("div",{"data-test-id":"CircularProgressbarWithChildren"},createElement("div",{style:{position:"relative",width:"100%",height:"100%"}},createElement(g,a({},r)),t.children?createElement("div",{"data-test-id":"CircularProgressbarWithChildren__children",style:{position:"absolute",width:"100%",height:"100%",marginTop:"-100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}},t.children):null))}function m(t){var e=t.rotation,r=t.strokeLinecap,n=t.textColor,i=t.textSize,o=t.pathColor,a=t.pathTransition,s=t.pathTransitionDuration,c=t.trailColor,u=t.backgroundColor;var l=e==null?undefined:"rotate("+e+"turn)";var f=e==null?undefined:"center center";return{root:{},path:b({stroke:o,strokeLinecap:r,transform:l,transformOrigin:f,transition:a,transitionDuration:s==null?undefined:s+"s"}),trail:b({stroke:c,strokeLinecap:r,transform:l,transformOrigin:f}),text:b({fill:n,fontSize:i}),background:b({fill:u})}}function b(t){Object.keys(t).forEach(function(e){if(t[e]==null){delete t[e]}});return t}},6866:(t,e,r)=>{"use strict";function n(t){"@babel/helpers - typeof";if(typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"){n=function t(t){return typeof t}}else{n=function t(t){return t&&typeof Symbol==="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t}}return n(t)}Object.defineProperty(e,"__esModule",{value:true});Object.defineProperty(e,"useSpeechRecognition",{enumerable:true,get:function t(){return i.useSpeechRecognition}});e["default"]=void 0;var i=a(r(8127));function o(){if(typeof WeakMap!=="function")return null;var t=new WeakMap;o=function e(){return t};return t}function a(t){if(t&&t.__esModule){return t}if(t===null||n(t)!=="object"&&typeof t!=="function"){return{"default":t}}var e=o();if(e&&e.has(t)){return e.get(t)}var r={};var i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t){if(Object.prototype.hasOwnProperty.call(t,a)){var s=i?Object.getOwnPropertyDescriptor(t,a):null;if(s&&(s.get||s.set)){Object.defineProperty(r,a,s)}else{r[a]=t[a]}}}r["default"]=t;if(e){e.set(t,r)}return r}var s=i["default"];e["default"]=s},8127:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e["default"]=e.useSpeechRecognition=void 0;var n=r(2115);var i=r(4783);var o=r(3393);var a=r(2551);var s=l(r(736));var c=l(r(5501));var u=l(r(9070));function l(t){return t&&t.__esModule?t:{"default":t}}function f(t,e,r,n,i,o,a){try{var s=t[o](a);var c=s.value}catch(t){r(t);return}if(s.done){e(c)}else{Promise.resolve(c).then(n,i)}}function h(t){return function(){var e=this,r=arguments;return new Promise(function(n,i){var o=t.apply(e,r);function a(t){f(o,n,i,a,s,"next",t)}function s(t){f(o,n,i,a,s,"throw",t)}a(undefined)})}}function p(t){return g(t)||d(t)||w(t)||v()}function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t){if(typeof Symbol!=="undefined"&&Symbol.iterator in Object(t))return Array.from(t)}function g(t){if(Array.isArray(t))return k(t)}function y(t){"@babel/helpers - typeof";if(typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"){y=function t(t){return typeof t}}else{y=function t(t){return t&&typeof Symbol==="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t}}return y(t)}function m(t,e){return T(t)||R(t,e)||w(t,e)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(t,e){if(!t)return;if(typeof t==="string")return k(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return k(t,e)}function k(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++){n[r]=t[r]}return n}function R(t,e){if(typeof Symbol==="undefined"||!(Symbol.iterator in Object(t)))return;var r=[];var n=true;var i=false;var o=undefined;try{for(var a=t[Symbol.iterator](),s;!(n=(s=a.next()).done);n=true){r.push(s.value);if(e&&r.length===e)break}}catch(t){i=true;o=t}finally{try{if(!n&&a["return"]!=null)a["return"]()}finally{if(i)throw o}}return r}function T(t){if(Array.isArray(t))return t}var S=!!u["default"];var x=S&&!(0,c["default"])();var C;var P=function t(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},e=t.transcribing,r=e===void 0?true:e,s=t.clearTranscriptOnListen,c=s===void 0?true:s,u=t.commands,l=u===void 0?[]:u;var f=(0,n.useState)(L.getRecognitionManager()),h=m(f,1),v=h[0];var d=(0,n.useState)(S),g=m(d,2),b=g[0],w=g[1];var k=(0,n.useState)(x),R=m(k,2),T=R[0],C=R[1];var P=(0,n.useReducer)(a.transcriptReducer,{interimTranscript:v.interimTranscript,finalTranscript:""}),A=m(P,2),E=A[0],O=E.interimTranscript,_=E.finalTranscript,j=A[1];var M=(0,n.useState)(v.listening),N=m(M,2),D=N[0],I=N[1];var W=(0,n.useState)(v.isMicrophoneAvailable),F=m(W,2),z=F[0],B=F[1];var $=(0,n.useRef)(l);$.current=l;var G=function t(){j((0,o.clearTranscript)())};var U=(0,n.useCallback)(function(){v.resetTranscript();G()},[v]);var V=function t(t,e,r){var n=y(t)==="object"?t.toString():t;var o=n.replace(/[&/\\#,+()!$~%.'":*?<>{}]/g,"").replace(/  +/g," ").trim();var a=(0,i.compareTwoStringsUsingDiceCoefficient)(o,e);if(a>=r){return{command:t,commandWithoutSpecials:o,howSimilar:a,isFuzzyMatch:true}}return null};var q=function t(t,e){var r=(0,i.commandToRegExp)(t);var n=r.exec(e);if(n){return{command:t,parameters:n.slice(1)}}return null};var Y=(0,n.useCallback)(function(t,e){$.current.forEach(function(r){var n=r.command,i=r.callback,o=r.matchInterim,a=o===void 0?false:o,s=r.isFuzzyMatch,c=s===void 0?false:s,u=r.fuzzyMatchingThreshold,l=u===void 0?.8:u,f=r.bestMatchOnly,h=f===void 0?false:f;var v=!e&&a?t.trim():e.trim();var d=Array.isArray(n)?n:[n];var g=d.map(function(t){if(c){return V(t,v,l)}return q(t,v)}).filter(function(t){return t});if(c&&h&&g.length>=2){g.sort(function(t,e){return e.howSimilar-t.howSimilar});var y=g[0],m=y.command,b=y.commandWithoutSpecials,w=y.howSimilar;i(b,v,w,{command:m,resetTranscript:U})}else{g.forEach(function(t){if(t.isFuzzyMatch){var e=t.command,r=t.commandWithoutSpecials,n=t.howSimilar;i(r,v,n,{command:e,resetTranscript:U})}else{var o=t.command,a=t.parameters;i.apply(void 0,p(a).concat([{command:o,resetTranscript:U}]))}})}})},[U]);var Z=(0,n.useCallback)(function(t,e){if(r){j((0,o.appendTranscript)(t,e))}Y(t,e)},[Y,r]);var H=(0,n.useCallback)(function(){if(c){G()}},[c]);(0,n.useEffect)(function(){var t=L.counter;L.counter+=1;var e={onListeningChange:I,onMicrophoneAvailabilityChange:B,onTranscriptChange:Z,onClearTranscript:H,onBrowserSupportsSpeechRecognitionChange:w,onBrowserSupportsContinuousListeningChange:C};v.subscribe(t,e);return function(){v.unsubscribe(t)}},[r,c,v,Z,H]);var Q=(0,i.concatTranscripts)(_,O);return{transcript:Q,interimTranscript:O,finalTranscript:_,listening:D,isMicrophoneAvailable:z,resetTranscript:U,browserSupportsSpeechRecognition:b,browserSupportsContinuousListening:T}};e.useSpeechRecognition=P;var L={counter:0,applyPolyfill:function t(t){if(C){C.setSpeechRecognition(t)}else{C=new s["default"](t)}var e=!!t&&(0,i.browserSupportsPolyfills)();S=e;x=e},removePolyfill:function t(){if(C){C.setSpeechRecognition(u["default"])}else{C=new s["default"](u["default"])}S=!!u["default"];x=S&&!(0,c["default"])()},getRecognitionManager:function t(){if(!C){C=new s["default"](u["default"])}return C},getRecognition:function t(){var t=L.getRecognitionManager();return t.getRecognition()},startListening:function(){var t=h(regeneratorRuntime.mark(function t(){var e,r,n,i,o=arguments;return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:e=o.length>0&&o[0]!==undefined?o[0]:{},r=e.continuous,n=e.language;i=L.getRecognitionManager();t.next=4;return i.startListening({continuous:r,language:n});case 4:case"end":return t.stop()}}},t)}));function e(){return t.apply(this,arguments)}return e}(),stopListening:function(){var t=h(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:e=L.getRecognitionManager();t.next=3;return e.stopListening();case 3:case"end":return t.stop()}}},t)}));function e(){return t.apply(this,arguments)}return e}(),abortListening:function(){var t=h(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function t(t){while(1){switch(t.prev=t.next){case 0:e=L.getRecognitionManager();t.next=3;return e.abortListening();case 3:case"end":return t.stop()}}},t)}));function e(){return t.apply(this,arguments)}return e}(),browserSupportsSpeechRecognition:function t(){return S},browserSupportsContinuousListening:function t(){return x}};var A=L;e["default"]=A},8979:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(9946);const i=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]];const o=(0,n.A)("square",i)},9070:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e["default"]=e.isNative=void 0;var r=typeof window!=="undefined"&&(window.SpeechRecognition||window.webkitSpeechRecognition||window.mozSpeechRecognition||window.msSpeechRecognition||window.oSpeechRecognition);var n=function t(t){return t===r};e.isNative=n;var i=r;e["default"]=i},9588:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(9946);const i=[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]];const o=(0,n.A)("mic",i)},9946:(t,e,r)=>{"use strict";r.d(e,{A:()=>f});var n=r(2115);const i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();const o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase());const a=t=>{const e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)};const s=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++){e[r]=arguments[r]}return e.filter((t,e,r)=>{return Boolean(t)&&t.trim()!==""&&r.indexOf(t)===e}).join(" ").trim()};const c=t=>{for(const e in t){if(e.startsWith("aria-")||e==="role"||e==="title"){return true}}};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const l=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:l="",children:f,iconNode:h,...p}=t;return(0,n.createElement)("svg",{ref:e,...u,width:i,height:i,stroke:r,strokeWidth:a?Number(o)*24/Number(i):o,className:s("lucide",l),...!f&&!c(p)&&{"aria-hidden":"true"},...p},[...h.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(f)?f:[f]])});const f=(t,e)=>{const r=(0,n.forwardRef)((r,o)=>{let{className:c,...u}=r;return(0,n.createElement)(l,{ref:o,iconNode:e,className:s("lucide-".concat(i(a(t))),"lucide-".concat(t),c),...u})});r.displayName=a(t);return r}}}]);