(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[801],{471:(l,e,s)=>{Promise.resolve().then(s.bind(s,5834))},5834:(l,e,s)=>{"use strict";s.r(e);s.d(e,{"default":()=>d});var a=s(5155);var i=s(3582);var t=s(5318);var r=s(6766);const c=()=>{const l=async l=>{console.log("Submitted Data:",l);return{success:true}};return(0,a.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)(r["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,a.jsx)(r["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,a.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,a.jsx)(t.A,{mode:"forgot",schema:i.Ih,defaultValues:{email:""},onSubmit:l})})})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,a.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsx)(t.A,{mode:"forgot",schema:i.Ih,defaultValues:{email:""},onSubmit:l})})})})})]})};const d=c}},l=>{var e=e=>l(l.s=e);l.O(0,[766,831,903,905,441,684,358],()=>e(471));var s=l.O();_N_E=s}]);