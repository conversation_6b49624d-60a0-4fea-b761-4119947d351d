(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[575],{2807:(e,s,t)=>{Promise.resolve().then(t.bind(t,6458))},5695:(e,s,t)=>{"use strict";var r=t(8999);var a=t.n(r);if(t.o(r,"usePathname"))t.d(s,{usePathname:function(){return r.usePathname}});if(t.o(r,"useRouter"))t.d(s,{useRouter:function(){return r.useRouter}})},6325:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(9946);const a=[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]];const n=(0,r.A)("briefcase-business",a)},6458:(e,s,t)=>{"use strict";t.r(s);t.d(s,{"default":()=>P});var r=t(5155);var a=t(2115);var n=t(9946);const l=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]];const c=(0,n.A)("menu",l);const o=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M13.916 2.314A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.74 7.327A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673 9 9 0 0 1-.585-.665",key:"1tip0g"}],["circle",{cx:"18",cy:"8",r:"3",key:"1g0gzu"}]];const i=(0,n.A)("bell-dot",o);const d=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]];const x=(0,n.A)("globe",d);const m=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]];const h=(0,n.A)("chevron-down",m);const u=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]];const g=(0,n.A)("x",u);const f=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]];const p=(0,n.A)("circle-user",f);const y=e=>{let{onToggleSidebar:s}=e;const[t,n]=(0,a.useState)(false);const l=()=>{n(!t)};return(0,r.jsxs)("header",{className:"border-b bg-white px-4 sm:px-6 py-4 sm:py-5 shrink-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("button",{onClick:s,className:"lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"Toggle sidebar",children:(0,r.jsx)(c,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsx)("div",{className:"text-lg sm:text-xl font-semibold text-gray-900",children:"AI Interview"})]}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center gap-4 xl:gap-6",children:[(0,r.jsx)(i,{className:"h-6 w-6 sm:h-8 sm:w-7 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-2 sm:py-4 px-4 sm:px-6 rounded-full transition-colors",children:[(0,r.jsx)(x,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"font-bold hidden sm:inline",children:"English"}),(0,r.jsx)(h,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-4 sm:px-8 py-2 sm:py-3 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-gray-500 p-1"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xs sm:text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]}),(0,r.jsx)(h,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex lg:hidden items-center gap-3",children:[(0,r.jsx)(i,{className:"h-6 w-6 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 cursor-pointer bg-gray-50 rounded-full px-3 py-2 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs font-medium text-gray-500",children:"H"})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad"}),(0,r.jsx)(h,{className:"h-4 w-4 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsx)("div",{className:"sm:hidden",children:(0,r.jsx)("button",{onClick:l,className:"p-2",children:t?(0,r.jsx)(g,{className:"h-6 w-6"}):(0,r.jsx)(p,{className:"h-6 w-6 text-gray-700"})})})]}),t&&(0,r.jsxs)("div",{className:"mt-4 pb-4 flex flex-col gap-4 sm:hidden border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"H"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(i,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(x,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"English"})]})]})]})};const b=y;var v=t(6766);var j=t(6874);var N=t.n(j);var w=t(5695);const k={"src":"/_next/static/media/logo-light.a0bdc026.svg","height":62,"width":177,"blurWidth":0,"blurHeight":0};const A=[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]];const C=(0,n.A)("layout-dashboard",A);var E=t(6325);const M=[{label:"Dashboard",href:"/",icon:C},{label:"Job Posts",href:"/interview",icon:E.A}];const L=e=>{let{isOpen:s,onClose:t}=e;const n=(0,w.usePathname)();const l=(0,a.useRef)(n);(0,a.useEffect)(()=>{if(l.current!==n&&s&&t){t()}l.current=n},[n,s,t]);(0,a.useEffect)(()=>{const e=e=>{const s=document.getElementById("mobile-sidebar");const r=document.getElementById("sidebar-overlay");if(s&&!s.contains(e.target)&&(r===null||r===void 0?void 0:r.contains(e.target))){if(t)t()}};if(s){document.addEventListener("mousedown",e);document.body.style.overflow="hidden"}else{document.body.style.overflow="unset"}return()=>{document.removeEventListener("mousedown",e);document.body.style.overflow="unset"}},[s,t]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("aside",{className:"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-10",children:(0,r.jsx)(v["default"],{src:k,alt:"Logo"})}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:M.map(e=>{const s=n===e.href;const t=e.icon;return(0,r.jsxs)(N(),{href:e.href,className:"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\n                  ".concat(s?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"),children:[(0,r.jsx)(t,{className:"w-5 h-5 ".concat(s?"text-purple-700":"text-gray-400")}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]}),(0,r.jsx)("div",{id:"sidebar-overlay",className:"fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ".concat(s?"opacity-100":"opacity-0 pointer-events-none"),children:(0,r.jsxs)("aside",{id:"mobile-sidebar",className:"fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out ".concat(s?"translate-x-0":"-translate-x-full"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-10",children:[(0,r.jsx)(v["default"],{src:k,alt:"Logo"}),(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(g,{className:"h-5 w-5 text-gray-500"})})]}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:M.map(e=>{const s=n===e.href;const a=e.icon;return(0,r.jsxs)(N(),{href:e.href,className:"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\n                      ".concat(s?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"),onClick:t,children:[(0,r.jsx)(a,{className:"w-5 h-5 ".concat(s?"text-purple-700":"text-gray-400")}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]})})]})};const _=L;const H=e=>{let{children:s}=e;const[t,n]=(0,a.useState)(false);const l=(0,a.useCallback)(()=>{n(e=>!e)},[]);const c=(0,a.useCallback)(()=>{n(false)},[]);return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(_,{isOpen:t,onClose:c}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden min-w-0",children:[(0,r.jsx)(b,{onToggleSidebar:l}),(0,r.jsx)("main",{className:"flex-1 overflow-auto p-4 sm:p-6",children:s})]})]})};const P=H},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var r=t(2115);const a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();const n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase());const l=e=>{const s=n(e);return s.charAt(0).toUpperCase()+s.slice(1)};const c=function(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++){s[t]=arguments[t]}return s.filter((e,s,t)=>{return Boolean(e)&&e.trim()!==""&&t.indexOf(e)===s}).join(" ").trim()};const o=e=>{for(const s in e){if(s.startsWith("aria-")||s==="role"||s==="title"){return true}}};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const d=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:d="",children:x,iconNode:m,...h}=e;return(0,r.createElement)("svg",{ref:s,...i,width:a,height:a,stroke:t,strokeWidth:l?Number(n)*24/Number(a):n,className:c("lucide",d),...!x&&!o(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(x)?x:[x]])});const x=(e,s)=>{const t=(0,r.forwardRef)((t,n)=>{let{className:o,...i}=t;return(0,r.createElement)(d,{ref:n,iconNode:s,className:c("lucide-".concat(a(l(e))),"lucide-".concat(e),o),...i})});t.displayName=l(e);return t}}},e=>{var s=s=>e(e.s=s);e.O(0,[766,874,441,684,358],()=>s(2807));var t=e.O();_N_E=t}]);