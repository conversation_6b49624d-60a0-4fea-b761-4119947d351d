(()=>{var e={};e.id=14,e.ids=[14],e.modules={78:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>n,POST:()=>o});var a=r(6559),i=r(8088),u=r(7719);let{GET:n,POST:o}=r(6814).Y9,p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"D:\\Softwares\\Ai bot\\intview-ai\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:x}=p;function v(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6559:(e,t,r)=>{"use strict";e.exports=r(4870)},6814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>u,j2:()=>p});var s=r(9859),a=r(3560),i=r(6056);let{handlers:u,signIn:n,signOut:o,auth:p}=(0,s.Ay)({providers:[a.A,i.A],secret:process.env.AUTH_SECRET})},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[97],()=>r(78));module.exports=s})();