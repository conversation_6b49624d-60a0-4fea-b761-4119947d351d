(()=>{var e={};e.id=14;e.ids=[14];e.modules={78:(e,r,t)=>{"use strict";t.r(r);t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var s={};t.r(s);t.d(s,{GET:()=>u,POST:()=>p});var a=t(6559);var i=t(8088);var o=t(7719);var n=t(6814);const{GET:u,POST:p}=n.Y9;const c="";const d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"D:\\Softwares\\Ai bot\\intview-ai\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:c,userland:s});const{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:l}=d;function h(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6559:(e,r,t)=>{"use strict";if(false){}else{if(false){}else{if(false){}else{if(false){}else{e.exports=t(4870)}}}}},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>o,j2:()=>p});var s=t(9859);var a=t(3560);var i=t(6056);const{handlers:o,signIn:n,signOut:u,auth:p}=(0,s.Ay)({providers:[a.A,i.A],secret:process.env.AUTH_SECRET})},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e);var s=r.X(0,[97],()=>t(78));module.exports=s})();