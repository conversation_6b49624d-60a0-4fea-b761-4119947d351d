exports.id=97,exports.ids=[97],exports.modules={23:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return a}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function a(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(1042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},366:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DetachedPromise",{enumerable:!0,get:function(){return r}});class r{constructor(){let e,t;this.promise=new Promise((r,n)=>{e=r,t=n}),this.resolve=e,this.reject=t}}},397:(e,t,r)=>{var n;(()=>{var a={226:function(a,i){!function(o,s){"use strict";var c="function",u="undefined",l="object",d="string",p="major",f="model",h="name",g="type",y="vendor",m="version",b="architecture",w="console",_="mobile",v="tablet",E="smarttv",S="wearable",A="embedded",R="Amazon",P="Apple",O="ASUS",T="BlackBerry",k="Browser",x="Chrome",C="Firefox",N="Google",j="Huawei",I="Microsoft",D="Motorola",U="Opera",M="Samsung",L="Sharp",$="Sony",H="Xiaomi",W="Zebra",B="Facebook",K="Chromium OS",q="Mac OS",G=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},F=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},V=function(e,t){return typeof e===d&&-1!==J(t).indexOf(J(e))},J=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},z=function(e,t){for(var r,n,a,i,o,u,d=0;d<t.length&&!o;){var p=t[d],f=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(a=0;a<f.length;a++)u=o[++n],typeof(i=f[a])===l&&i.length>0?2===i.length?typeof i[1]==c?this[i[0]]=i[1].call(this,u):this[i[0]]=i[1]:3===i.length?typeof i[1]!==c||i[1].exec&&i[1].test?this[i[0]]=u?u.replace(i[1],i[2]):void 0:this[i[0]]=u?i[1].call(this,u,i[2]):void 0:4===i.length&&(this[i[0]]=u?i[3].call(this,u.replace(i[1],i[2])):s):this[i]=u||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===l&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(V(t[r][n],e))return"?"===r?s:r}else if(V(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,m],[/opios[\/ ]+([\w\.]+)/i],[m,[h,U+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[h,U]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[h,"UC"+k]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+k],m],[/\bfocus\/([\w\.]+)/i],[m,[h,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[h,U+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[h,U+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[h,"MIUI "+k]],[/fxios\/([-\w\.]+)/i],[m,[h,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+k]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+k],m],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,B],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[h,x+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,x+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[h,"Android "+k]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[h,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,m],[/(cobalt)\/([\w\.]+)/i],[h,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,J]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[y,M],[g,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[y,M],[g,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[y,P],[g,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[y,P],[g,v]],[/(macintosh);/i],[f,[y,P]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[y,L],[g,_]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[y,j],[g,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[y,j],[g,_]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[y,H],[g,_]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[y,H],[g,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[y,"OPPO"],[g,_]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[y,"Vivo"],[g,_]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[y,"Realme"],[g,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[y,D],[g,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[y,D],[g,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[y,"LG"],[g,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[y,"LG"],[g,_]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[y,"Lenovo"],[g,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[y,"Nokia"],[g,_]],[/(pixel c)\b/i],[f,[y,N],[g,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[y,N],[g,_]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[y,$],[g,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[y,$],[g,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[y,"OnePlus"],[g,_]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[y,R],[g,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[y,R],[g,_]],[/(playbook);[-\w\),; ]+(rim)/i],[f,y,[g,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[y,T],[g,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[y,O],[g,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[y,O],[g,_]],[/(nexus 9)/i],[f,[y,"HTC"],[g,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[f,/_/g," "],[g,_]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[y,"Acer"],[g,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[y,"Meizu"],[g,_]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,f,[g,_]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,f,[g,v]],[/(surface duo)/i],[f,[y,I],[g,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[y,"Fairphone"],[g,_]],[/(u304aa)/i],[f,[y,"AT&T"],[g,_]],[/\bsie-(\w*)/i],[f,[y,"Siemens"],[g,_]],[/\b(rct\w+) b/i],[f,[y,"RCA"],[g,v]],[/\b(venue[\d ]{2,7}) b/i],[f,[y,"Dell"],[g,v]],[/\b(q(?:mv|ta)\w+) b/i],[f,[y,"Verizon"],[g,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[y,"Barnes & Noble"],[g,v]],[/\b(tm\d{3}\w+) b/i],[f,[y,"NuVision"],[g,v]],[/\b(k88) b/i],[f,[y,"ZTE"],[g,v]],[/\b(nx\d{3}j) b/i],[f,[y,"ZTE"],[g,_]],[/\b(gen\d{3}) b.+49h/i],[f,[y,"Swiss"],[g,_]],[/\b(zur\d{3}) b/i],[f,[y,"Swiss"],[g,v]],[/\b((zeki)?tb.*\b) b/i],[f,[y,"Zeki"],[g,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],f,[g,v]],[/\b(ns-?\w{0,9}) b/i],[f,[y,"Insignia"],[g,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[y,"NextBook"],[g,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],f,[g,_]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],f,[g,_]],[/\b(ph-1) /i],[f,[y,"Essential"],[g,_]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[y,"Envizen"],[g,v]],[/\b(trio[-\w\. ]+) b/i],[f,[y,"MachSpeed"],[g,v]],[/\btu_(1491) b/i],[f,[y,"Rotor"],[g,v]],[/(shield[\w ]+) b/i],[f,[y,"Nvidia"],[g,v]],[/(sprint) (\w+)/i],[y,f,[g,_]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[y,I],[g,_]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[y,W],[g,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[y,W],[g,_]],[/smart-tv.+(samsung)/i],[y,[g,E]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[y,M],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[g,E]],[/(apple) ?tv/i],[y,[f,P+" TV"],[g,E]],[/crkey/i],[[f,x+"cast"],[y,N],[g,E]],[/droid.+aft(\w)( bui|\))/i],[f,[y,R],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[y,L],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[f,[y,$],[g,E]],[/(mitv-\w{5}) bui/i],[f,[y,H],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[y,f,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,X],[f,X],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,f,[g,w]],[/droid.+; (shield) bui/i],[f,[y,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[f,[y,$],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[y,I],[g,w]],[/((pebble))app/i],[y,f,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[y,P],[g,S]],[/droid.+; (glass) \d/i],[f,[y,N],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[f,[y,W],[g,S]],[/(quest( 2| pro)?)/i],[f,[y,B],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[g,A]],[/(aeobc)\b/i],[f,[y,R],[g,A]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[g,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[g,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,_]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[m,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[m,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,q],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,m],[/\(bb(10);/i],[m,[h,T]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[h,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[h,x+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,K],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,m],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,m]]},ee=function(e,t){if(typeof e===l&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==u&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),a=r&&r.userAgentData?r.userAgentData:s,i=t?G(Q,t):Q,w=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[m]=s,z.call(t,n,i.browser),t[p]=typeof(e=t[m])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[b]=s,z.call(e,n,i.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[f]=s,e[g]=s,z.call(e,n,i.device),w&&!e[g]&&a&&a.mobile&&(e[g]=_),w&&"Macintosh"==e[f]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[g]=v),e},this.getEngine=function(){var e={};return e[h]=s,e[m]=s,z.call(e,n,i.engine),e},this.getOS=function(){var e={};return e[h]=s,e[m]=s,z.call(e,n,i.os),w&&!e[h]&&a&&"Unknown"!=a.platform&&(e[h]=a.platform.replace(/chrome os/i,K).replace(/macos/i,q)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?X(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=F([h,m,p]),ee.CPU=F([b]),ee.DEVICE=F([f,y,g,w,_,E,v,S,A]),ee.ENGINE=ee.OS=F([h,m]),typeof i!==u?(a.exports&&(i=a.exports=ee),i.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==u&&(o.UAParser=ee);var et=typeof o!==u&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}},n=!0;try{a[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete i[e]}return r.exports}o.ab=__dirname+"/",e.exports=o(226)})()},635:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,i={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function c(e){if(!e)return;let[[t,r],...n]=s(e),{domain:a,expires:i,httponly:o,maxage:c,path:d,samesite:p,secure:f,partitioned:h,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var y,m,b={name:t,value:decodeURIComponent(r),domain:a,...i&&{expires:new Date(i)},...o&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:d,...p&&{sameSite:u.includes(y=(y=p).toLowerCase())?y:void 0},...f&&{secure:!0},...g&&{priority:l.includes(m=(m=g).toLowerCase())?m:void 0},...h&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(i,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,i,o,s)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let c of n(i))a.call(e,c)||c===o||t(e,c,{get:()=>i[c],enumerable:!(s=r(i,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],l=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let a=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(a)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},898:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNodeNextRequest:function(){return a},isNodeNextResponse:function(){return i},isWebNextRequest:function(){return r},isWebNextResponse:function(){return n}});let r=e=>!1,n=e=>!1,a=e=>!0,i=e=>!0},899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},980:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{CachedRouteKind:function(){return r},IncrementalCacheKind:function(){return n}});var r=function(e){return e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.PAGES="PAGES",e.FETCH="FETCH",e.REDIRECT="REDIRECT",e.IMAGE="IMAGE",e}({}),n=function(e){return e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.PAGES="PAGES",e.FETCH="FETCH",e.IMAGE="IMAGE",e}({})},1042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(8388),a=r(2637),i=r(1846),o=r(1162),s=r(4971),c=r(8479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1076:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getClientComponentLoaderMetrics:function(){return o},wrapClientComponentLoader:function(){return i}});let r=0,n=0,a=0;function i(e){return"performance"in globalThis?{require:(...t)=>{let i=performance.now();0===r&&(r=i);try{return a+=1,e.__next_app__.require(...t)}finally{n+=performance.now()-i}},loadChunk:(...t)=>{let r=performance.now(),a=e.__next_app__.loadChunk(...t);return a.finally(()=>{n+=performance.now()-r}),a}}:e.__next_app__}function o(e={}){let t=0===r?void 0:{clientComponentLoadStart:r,clientComponentLoadTimes:n,clientComponentLoadCount:a};return e.reset&&(r=0,n=0,a=0),t}},1120:(e,t,r)=>{"use strict";e.exports=r(5239).vendored["react-rsc"].React},1162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(8704),a=r(9026);function i(e){return(0,a.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1243:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},1289:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BubbledError:function(){return p},SpanKind:function(){return l},SpanStatusCode:function(){return u},getTracer:function(){return v},isBubbledError:function(){return f}});let a=r(4823),i=r(9098);try{n=r(2665)}catch(e){n=r(2665)}let{context:o,propagation:s,trace:c,SpanStatusCode:u,SpanKind:l,ROOT_CONTEXT:d}=n;class p extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}function f(e){return"object"==typeof e&&null!==e&&e instanceof p}let h=(e,t)=>{f(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:u.ERROR,message:null==t?void 0:t.message})),e.end()},g=new Map,y=n.createContextKey("next.rootSpanId"),m=0,b=()=>m++,w={set(e,t,r){e.push({key:t,value:r})}};class _{getTracerInstance(){return c.getTracer("next.js","0.0.1")}getContext(){return o}getTracePropagationData(){let e=o.active(),t=[];return s.inject(e,t,w),t}getActiveScopeSpan(){return c.getSpan(null==o?void 0:o.active())}withPropagatedContext(e,t,r){let n=o.active();if(c.getSpanContext(n))return t();let a=s.extract(n,e,r);return o.with(a,t)}trace(...e){var t;let[r,n,s]=e,{fn:u,options:l}="function"==typeof n?{fn:n,options:{}}:{fn:s,options:{...n}},p=l.spanName??r;if(!a.NextVanillaSpanAllowlist.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||l.hideSpan)return u();let f=this.getSpanContext((null==l?void 0:l.parentSpan)??this.getActiveScopeSpan()),m=!1;f?(null==(t=c.getSpanContext(f))?void 0:t.isRemote)&&(m=!0):(f=(null==o?void 0:o.active())??d,m=!0);let w=b();return l.attributes={"next.span_name":p,"next.span_type":r,...l.attributes},o.with(f.setValue(y,w),()=>this.getTracerInstance().startActiveSpan(p,l,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{g.delete(w),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&a.LogSpanAllowList.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};m&&g.set(w,new Map(Object.entries(l.attributes??{})));try{if(u.length>1)return u(e,t=>h(e,t));let t=u(e);if((0,i.isThenable)(t))return t.then(t=>(e.end(),t)).catch(t=>{throw h(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw h(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return a.NextVanillaSpanAllowlist.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,s=arguments[a];if("function"!=typeof s)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(o.active(),s);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?c.setSpan(o.active(),e):void 0}getRootSpanAttributes(){let e=o.active().getValue(y);return g.get(e)}setRootSpanAttribute(e,t){let r=o.active().getValue(y),n=g.get(r);n&&n.set(e,t)}}let v=(()=>{let e=new _;return()=>e})()},1314:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},1617:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"InvariantError",{enumerable:!0,get:function(){return r}});class r extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},1846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return a}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},1856:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromResponseCacheEntry:function(){return o},routeKindToIncrementalCacheKind:function(){return c},toResponseCacheEntry:function(){return s}});let n=r(980),a=function(e){return e&&e.__esModule?e:{default:e}}(r(7778)),i=r(8088);async function o(e){var t,r;return{...e,value:(null==(t=e.value)?void 0:t.kind)===n.CachedRouteKind.PAGES?{kind:n.CachedRouteKind.PAGES,html:await e.value.html.toUnchunkedString(!0),pageData:e.value.pageData,headers:e.value.headers,status:e.value.status}:(null==(r=e.value)?void 0:r.kind)===n.CachedRouteKind.APP_PAGE?{kind:n.CachedRouteKind.APP_PAGE,html:await e.value.html.toUnchunkedString(!0),postponed:e.value.postponed,rscData:e.value.rscData,headers:e.value.headers,status:e.value.status,segmentData:e.value.segmentData}:e.value}}async function s(e){var t,r;return e?{isMiss:e.isMiss,isStale:e.isStale,cacheControl:e.cacheControl,isFallback:e.isFallback,value:(null==(t=e.value)?void 0:t.kind)===n.CachedRouteKind.PAGES?{kind:n.CachedRouteKind.PAGES,html:a.default.fromStatic(e.value.html),pageData:e.value.pageData,headers:e.value.headers,status:e.value.status}:(null==(r=e.value)?void 0:r.kind)===n.CachedRouteKind.APP_PAGE?{kind:n.CachedRouteKind.APP_PAGE,html:a.default.fromStatic(e.value.html),rscData:e.value.rscData,headers:e.value.headers,status:e.value.status,postponed:e.value.postponed,segmentData:e.value.segmentData}:e.value}:null}function c(e){switch(e){case i.RouteKind.PAGES:return n.IncrementalCacheKind.PAGES;case i.RouteKind.APP_PAGE:return n.IncrementalCacheKind.APP_PAGE;case i.RouteKind.IMAGE:return n.IncrementalCacheKind.IMAGE;case i.RouteKind.APP_ROUTE:return n.IncrementalCacheKind.APP_ROUTE;default:throw Object.defineProperty(Error(`Unexpected route kind ${e}`),"__NEXT_ERROR_CODE",{value:"E64",enumerable:!1,configurable:!0})}}},1959:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let a=r.get(t);a||(a=t.map(e=>e.toLowerCase()),r.set(t,a));let i=e.split("/",2);if(!i[1])return{pathname:e};let o=i[1].toLowerCase(),s=a.indexOf(o);return s<0?{pathname:e}:(n=t[s],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},2079:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rootParams",{enumerable:!0,get:function(){return l}});let n=r(1617),a=r(4971),i=r(9294),o=r(3033),s=r(8388),c=r(2609),u=new WeakMap;async function l(){let e=i.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new n.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=o.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let n=t.fallbackRouteParams;if(n){let p=!1;for(let t in e)if(n.has(t)){p=!0;break}if(p){if("prerender"===r.type){let t=u.get(e);if(t)return t;let n=(0,s.makeHangingPromise)(r.renderSignal,"`unstable_rootParams`");return u.set(e,n),n}var i=e,o=n,l=t,d=r;let p=u.get(i);if(p)return p;let f={...i},h=Promise.resolve(f);return u.set(i,h),Object.keys(i).forEach(e=>{c.wellKnownProperties.has(e)||(o.has(e)?Object.defineProperty(f,e,{get(){let t=(0,c.describeStringPropertyAccess)("unstable_rootParams",e);"prerender-ppr"===d.type?(0,a.postponeWithTracking)(l.route,t,d.dynamicTracking):(0,a.throwToInterruptStaticGeneration)(t,l,d)},enumerable:!0}):h[e]=i[e])}),h}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},2174:(e,t)=>{"use strict";function r(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},2471:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isAbortError:function(){return c},pipeToNodeResponse:function(){return u}});let n=r(9893),a=r(366),i=r(1289),o=r(4823),s=r(1076);function c(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===n.ResponseAbortedName}async function u(e,t,r){try{let{errored:c,destroyed:u}=t;if(c||u)return;let l=(0,n.createAbortController)(t),d=function(e,t){let r=!1,n=new a.DetachedPromise;function c(){n.resolve()}e.on("drain",c),e.once("close",()=>{e.off("drain",c),n.resolve()});let u=new a.DetachedPromise;return e.once("finish",()=>{u.resolve()}),new WritableStream({write:async t=>{if(!r){if(r=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let e=(0,s.getClientComponentLoaderMetrics)();e&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:e.clientComponentLoadStart,end:e.clientComponentLoadStart+e.clientComponentLoadTimes})}e.flushHeaders(),(0,i.getTracer)().trace(o.NextNodeServerSpan.startResponse,{spanName:"start response"},()=>void 0)}try{let r=e.write(t);"flush"in e&&"function"==typeof e.flush&&e.flush(),r||(await n.promise,n=new a.DetachedPromise)}catch(t){throw e.end(),Object.defineProperty(Error("failed to write chunk to response",{cause:t}),"__NEXT_ERROR_CODE",{value:"E321",enumerable:!1,configurable:!0})}},abort:t=>{e.writableFinished||e.destroy(t)},close:async()=>{if(t&&await t,!e.writableFinished)return e.end(),u.promise}})}(t,r);await e.pipeTo(d,{signal:l.signal})}catch(e){if(c(e))return;throw Object.defineProperty(Error("failed to pipe response",{cause:e}),"__NEXT_ERROR_CODE",{value:"E180",enumerable:!1,configurable:!0})}}},2584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return a}});let n=r(3763);class a extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new a}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,a)},set(t,r,a,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,a,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,s??r,a,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},2609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},2637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},2665:e=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),a=r(172),i=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),a=r(912),i=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:i.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let u=(0,o.getGlobal)("diag"),l=(0,a.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:i.DiagLogLevel.INFO,e);if(u&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),l.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",l,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),a=r(172),i=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),a=r(874),i=r(194),o=r(277),s=r(369),c=r(930),u="propagation",l=new a.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,c.DiagAPI.instance())}inject(e,t,r=i.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=i.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||l}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),a=r(846),i=r(139),o=r(607),s=r(930),c="trace";class u{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=i.wrapSpanContext,this.isSpanContextValid=i.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),a=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function i(e){return e.getValue(a)||void 0}t.getBaggage=i,t.getActiveBaggage=function(){return i(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),a=r(993),i=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:i.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class a{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=a},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new r(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class a{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return i("debug",this._namespace,e)}error(...e){return i("error",this._namespace,e)}info(...e){return i("info",this._namespace,e)}warn(...e){return i("warn",this._namespace,e)}verbose(...e){return i("verbose",this._namespace,e)}}function i(e,t,r){let a=(0,n.getGlobal)("diag");if(a)return r.unshift(t),a[e](...r)}t.DiagComponentLogger=a},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let a=t[r];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),a=r(521),i=r(130),o=a.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var i;let o=c[s]=null!=(i=c[s])?i:{version:a.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${a.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,i.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function i(e){let t=new Set([e]),r=new Set,n=e.match(a);if(!n)return()=>!1;let i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(a);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||i.major!==s.major)return o(e);if(0===i.major)return i.minor===s.minor&&i.patch<=s.patch?(t.add(e),!0):o(e);return i.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=i,t.isCompatible=i(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class i extends n{add(e,t){}}t.NoopUpDownCounterMetric=i;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class u extends s{}t.NoopObservableGaugeMetric=u;class l extends s{}t.NoopObservableUpDownCounterMetric=l,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new i,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new l,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class a{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:global},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class a{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=a},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),a=r(607),i=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new i.NonRecordingSpan;let c=r&&(0,a.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new i.NonRecordingSpan(c):new i.NonRecordingSpan}startActiveSpan(e,t,r,n){let i,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(i=t,c=r):(i=t,o=r,c=n);let u=null!=o?o:s.active(),l=this.startSpan(e,i,u),d=(0,a.setSpan)(u,l);return s.with(d,c,void 0,l)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class a{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=a},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class a{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=a},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),a=new(r(124)).NoopTracerProvider;class i{getTracer(e,t,r){var a;return null!=(a=this.getDelegateTracer(e,t,r))?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=i},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),a=r(403),i=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(i.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),a=r.indexOf("=");if(-1!==a){let i=r.slice(0,a),o=r.slice(a+1,t.length);(0,n.validateKey)(i)&&(0,n.validateValue)(o)&&e.set(i,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,i=RegExp(`^(?:${n}|${a})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return i.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),a=r(403),i=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return i.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var i=r[e]={exports:{}},o=!0;try{t[e].call(i.exports,i,i.exports,n),o=!1}finally{o&&delete r[e]}return i.exports}n.ab=__dirname+"/";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=n(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=n(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=n(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=n(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var o=n(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=n(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=n(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var u=n(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var l=n(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return l.ProxyTracerProvider}});var d=n(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var p=n(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var f=n(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return f.SpanStatusCode}});var h=n(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return h.TraceFlags}});var g=n(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var y=n(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return y.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return y.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return y.isValidSpanId}});var m=n(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let b=n(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return b.context}});let w=n(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return w.diag}});let _=n(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return _.metrics}});let v=n(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return v.propagation}});let E=n(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return E.trace}}),a.default={context:b.context,diag:w.diag,metrics:_.metrics,propagation:v.propagation,trace:E.trace}})(),e.exports=a})()},2765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2829:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return a}});let n=r(8631);function a(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2887:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},2944:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"connection",{enumerable:!0,get:function(){return u}});let n=r(9294),a=r(3033),i=r(4971),o=r(23),s=r(8388),c=r(8719);function u(){let e=n.workAsyncStorage.getStore(),t=a.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return(0,s.makeHangingPromise)(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&(0,i.throwToInterruptStaticGeneration)("connection",e,t);(0,i.trackDynamicDataInDynamicRender)(e,t)}return Promise.resolve(void 0)}},3158:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies},stringifyCookie:function(){return n.stringifyCookie}});let n=r(635)},3182:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return a},userAgent:function(){return o},userAgentFromString:function(){return i}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(397));function a(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function i(e){return{...(0,n.default)(e),isBot:void 0!==e&&a(e)}}function o({headers:e}){return i(e.get("user-agent")||void 0)}},3365:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(8737),a=r(4523),i=r(1856);!function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(980),t);class o{constructor(e){this.batcher=n.Batcher.create({cacheKeyFn:({key:e,isOnDemandRevalidate:t})=>`${e}-${t?"1":"0"}`,schedulerFn:a.scheduleOnNextTick}),this.minimalMode=e}async get(e,t,r){if(!e)return t({hasResolved:!1,previousCacheEntry:null});let{incrementalCache:n,isOnDemandRevalidate:a=!1,isFallback:o=!1,isRoutePPREnabled:s=!1}=r,c=await this.batcher.batch({key:e,isOnDemandRevalidate:a},async(c,u)=>{var l;if(this.minimalMode&&(null==(l=this.previousCacheItem)?void 0:l.key)===c&&this.previousCacheItem.expiresAt>Date.now())return this.previousCacheItem.entry;let d=(0,i.routeKindToIncrementalCacheKind)(r.routeKind),p=!1,f=null;try{if((f=this.minimalMode?null:await n.get(e,{kind:d,isRoutePPREnabled:r.isRoutePPREnabled,isFallback:o}))&&!a&&(u(f),p=!0,!f.isStale||r.isPrefetch))return null;let l=await t({hasResolved:p,previousCacheEntry:f,isRevalidating:!0});if(!l)return this.minimalMode&&(this.previousCacheItem=void 0),null;let h=await (0,i.fromResponseCacheEntry)({...l,isMiss:!f});if(!h)return this.minimalMode&&(this.previousCacheItem=void 0),null;return a||p||(u(h),p=!0),h.cacheControl&&(this.minimalMode?this.previousCacheItem={key:c,entry:h,expiresAt:Date.now()+1e3}:await n.set(e,h.value,{cacheControl:h.cacheControl,isRoutePPREnabled:s,isFallback:o})),h}catch(t){if(null==f?void 0:f.cacheControl){let t=Math.min(Math.max(f.cacheControl.revalidate||3,3),30),r=void 0===f.cacheControl.expire?void 0:Math.max(t+3,f.cacheControl.expire);await n.set(e,f.value,{cacheControl:{revalidate:t,expire:r},isRoutePPREnabled:s,isFallback:o})}if(p)return console.error(t),null;throw t}});return(0,i.toResponseCacheEntry)(c)}}},3381:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(7252),t)},3426:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return d}});let n=r(3158),a=r(6608),i=r(7912),o=r(3763),s=r(3158),c=Symbol("internal response"),u=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,a]of e.request.headers)t.set("x-middleware-request-"+n,a),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,u=new Proxy(new s.ResponseCookies(r),{get(e,a,i){switch(a){case"delete":case"set":return(...i)=>{let o=Reflect.apply(e[a],e,i),c=new Headers(r);return o instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,n.stringifyCookie)(e)).join(",")),l(t,c),o};default:return o.ReflectAdapter.get(e,a,i)}}});this[c]={cookies:u,url:t.url?new a.NextURL(t.url,{headers:(0,i.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[c].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!u.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},a=new Headers(null==n?void 0:n.headers);return a.set("Location",(0,i.validateURL)(e)),new d(null,{...n,headers:a,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,i.validateURL)(e)),l(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new d(null,{...e,headers:t})}}},3560:(e,t,r)=>{"use strict";function n(e){let t=e?.enterprise?.baseUrl??"https://github.com",r=e?.enterprise?.baseUrl?`${e?.enterprise?.baseUrl}/api/v3`:"https://api.github.com";return{id:"github",name:"GitHub",type:"oauth",authorization:{url:`${t}/login/oauth/authorize`,params:{scope:"read:user user:email"}},token:`${t}/login/oauth/access_token`,userinfo:{url:`${r}/user`,async request({tokens:e,provider:t}){let n=await fetch(t.userinfo?.url,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}}).then(async e=>await e.json());if(!n.email){let t=await fetch(`${r}/user/emails`,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}});if(t.ok){let e=await t.json();n.email=(e.find(e=>e.primary)??e[0]).email}}return n}},profile:e=>({id:e.id.toString(),name:e.name??e.login,email:e.email,image:e.avatar_url}),style:{bg:"#24292f",text:"#fff"},options:e}}r.d(t,{A:()=>n})},3611:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return n},RemovedUAError:function(){return a}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class a extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},3763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},3828:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let i of(r&&(r=r.toLowerCase()),e)){var n,a;if(t===(null==(n=i.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===i.defaultLocale.toLowerCase()||(null==(a=i.locales)?void 0:a.some(e=>e.toLowerCase()===r)))return i}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},3913:(e,t,r)=>{"use strict";let n=r(3033),a=r(9294),i=r(4971),o=r(6926),s=r(23),c=r(8479);function u(){let e=a.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return l(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return l(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return p(null);default:return t}}function l(e,t){let r,n=d.get(u);return n||(r=p(e),d.set(e,r),r)}let d=new WeakMap;function p(e){let t=new f(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class f{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){g("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){g("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function g(e){let t=a.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},4069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return p},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return l},responseCookiesToRequestCookies:function(){return y},wrapWithMutableAccessCheck:function(){return f}});let n=r(3158),a=r(3763),i=r(9294),o=r(3033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function l(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=l(t);if(0===r.length)return!1;let a=new n.ResponseCookies(e),i=a.getAll();for(let e of r)a.set(e);for(let e of i)a.set(e);return!0}class p{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},l=new Proxy(r,{get(e,t,r){switch(t){case u:return o;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),l}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),l}finally{c()}};default:return a.ReflectAdapter.get(e,t,r)}}});return l}}function f(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return g("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return g("cookies().set"),e.set(...r),t};default:return a.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function g(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new s}function y(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},4113:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"MISSING_ROOT_TAGS_ERROR",{enumerable:!0,get:function(){return r}});let r="NEXT_MISSING_ROOT_TAGS";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4436:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupeFetch",{enumerable:!0,get:function(){return s}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(1120)),a=r(9169),i=r(1617);function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}function s(e){let t=n.cache(e=>[]);return function(r,n){let o,s;if(n&&n.signal)return e(r,n);if("string"!=typeof r||n){let t="string"==typeof r||r instanceof URL?new Request(r,n):r;if("GET"!==t.method&&"HEAD"!==t.method||t.keepalive)return e(r,n);s=JSON.stringify([t.method,Array.from(t.headers.entries()),t.mode,t.redirect,t.credentials,t.referrer,t.referrerPolicy,t.integrity]),o=t.url}else s='["GET",[],null,"follow",null,null,null,null]',o=r;let c=t(o);for(let e=0,t=c.length;e<t;e+=1){let[t,r]=c[e];if(t===s)return r.then(()=>{let t=c[e][2];if(!t)throw Object.defineProperty(new i.InvariantError("No cached response"),"__NEXT_ERROR_CODE",{value:"E579",enumerable:!1,configurable:!0});let[r,n]=(0,a.cloneResponse)(t);return c[e][2]=n,r})}let u=e(r,n),l=[s,u,null];return c.push(l),u.then(e=>{let[t,r]=(0,a.cloneResponse)(e);return l[2]=r,t})}}},4523:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{atLeastOneTask:function(){return a},scheduleImmediate:function(){return n},scheduleOnNextTick:function(){return r},waitAtLeastOneReactRenderTask:function(){return i}});let r=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},n=e=>{setImmediate(e)};function a(){return new Promise(e=>n(e))}function i(){return new Promise(e=>setImmediate(e))}},4525:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return n.ImageResponse},NextRequest:function(){return a.NextRequest},NextResponse:function(){return i.NextResponse},URLPattern:function(){return s.URLPattern},after:function(){return c.after},connection:function(){return u.connection},unstable_rootParams:function(){return l.unstable_rootParams},userAgent:function(){return o.userAgent},userAgentFromString:function(){return o.userAgentFromString}});let n=r(2174),a=r(6268),i=r(3426),o=r(3182),s=r(1243),c=r(3381),u=r(2944),l=r(2079)},4823:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRenderSpan:function(){return c},AppRouteRouteHandlersSpan:function(){return d},BaseServerSpan:function(){return r},LoadComponentsSpan:function(){return n},LogSpanAllowList:function(){return g},MiddlewareSpan:function(){return f},NextNodeServerSpan:function(){return i},NextServerSpan:function(){return a},NextVanillaSpanAllowlist:function(){return h},NodeSpan:function(){return l},RenderSpan:function(){return s},ResolveMetadataSpan:function(){return p},RouterSpan:function(){return u},StartServerSpan:function(){return o}});var r=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(r||{}),n=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(n||{}),a=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(a||{}),i=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(i||{}),o=function(e){return e.startServer="startServer.startServer",e}(o||{}),s=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(s||{}),c=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(c||{}),u=function(e){return e.executeRoute="Router.executeRoute",e}(u||{}),l=function(e){return e.runHandler="Node.runHandler",e}(l||{}),d=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(d||{}),p=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(p||{}),f=function(e){return e.execute="Middleware.execute",e}(f||{});let h=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],g=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},4971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return A},abortAndThrowOnSynchronousRequestDataAccess:function(){return E},abortOnSynchronousPlatformIOAccess:function(){return _},accessedDynamicData:function(){return N},annotateDynamicAccess:function(){return L},consumeDynamicAccess:function(){return j},createDynamicTrackingState:function(){return p},createDynamicValidationState:function(){return f},createHangingInputAbortSignal:function(){return M},createPostponedAbortSignal:function(){return U},formatDynamicAPIAccesses:function(){return I},getFirstDynamicReason:function(){return h},isDynamicPostpone:function(){return O},isPrerenderInterruptedError:function(){return C},markCurrentScopeAsDynamic:function(){return g},postponeWithTracking:function(){return R},throwIfDisallowedDynamic:function(){return G},throwToInterruptStaticGeneration:function(){return m},trackAllowedDynamicAccess:function(){return q},trackDynamicDataInDynamicRender:function(){return b},trackFallbackParamAccessed:function(){return y},trackSynchronousPlatformIOAccessInDev:function(){return v},trackSynchronousRequestDataAccessInDev:function(){return S},useDynamicRouteParams:function(){return $}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(1120)),a=r(8479),i=r(23),o=r(3033),s=r(9294),c=r(8388),u=r(7625),l=r(4523),d="function"==typeof n.default.unstable_postpone;function p(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function f(){return{hasSuspendedDynamic:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasSyncDynamicErrors:!1,dynamicErrors:[]}}function h(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function g(e,t,r){if((!t||"cache"!==t.type&&"unstable-cache"!==t.type)&&!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender-ppr"===t.type)R(e.route,r,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new a.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}}function y(e,t){let r=o.workUnitAsyncStorage.getStore();r&&"prerender-ppr"===r.type&&R(e.route,t,r.dynamicTracking)}function m(e,t,r){let n=Object.defineProperty(new a.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function b(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function w(e,t,r){let n=x(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let a=r.dynamicTracking;a&&a.dynamicAccesses.push({stack:a.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}function _(e,t,r,n){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=r),w(e,t,n)}function v(e){e.prerenderPhase=!1}function E(e,t,r,n){if(!1===n.controller.signal.aborted){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=r,!0===n.validating&&(a.syncDynamicLogged=!0)),w(e,t,n)}throw x(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}let S=v;function A({reason:e,route:t}){let r=o.workUnitAsyncStorage.getStore();R(t,e,r&&"prerender-ppr"===r.type?r.dynamicTracking:null)}function R(e,t,r){D(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.default.unstable_postpone(P(e,t))}function P(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function O(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&T(e.message)}function T(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===T(P("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let k="NEXT_PRERENDER_INTERRUPTED";function x(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest=k,t}function C(e){return"object"==typeof e&&null!==e&&e.digest===k&&"name"in e&&"message"in e&&e instanceof Error}function N(e){return e.length>0}function j(e,t){return e.dynamicAccesses.push(...t.dynamicAccesses),e.dynamicAccesses}function I(e){return e.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function D(){if(!d)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})}function U(e){D();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}function M(e){let t=new AbortController;return e.cacheSignal?e.cacheSignal.inputReady().then(()=>{t.abort()}):(0,l.scheduleOnNextTick)(()=>t.abort()),t.signal}function L(e,t){let r=t.dynamicTracking;r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:e})}function $(e){let t=s.workAsyncStorage.getStore();if(t&&t.isStaticGeneration&&t.fallbackRouteParams&&t.fallbackRouteParams.size>0){let r=o.workUnitAsyncStorage.getStore();r&&("prerender"===r.type?n.default.use((0,c.makeHangingPromise)(r.renderSignal,e)):"prerender-ppr"===r.type?R(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&m(e,t,r))}}let H=/\n\s+at Suspense \(<anonymous>\)/,W=RegExp(`\\n\\s+at ${u.METADATA_BOUNDARY_NAME}[\\n\\s]`),B=RegExp(`\\n\\s+at ${u.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),K=RegExp(`\\n\\s+at ${u.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function q(e,t,r,n,a){if(!K.test(t)){if(W.test(t)){r.hasDynamicMetadata=!0;return}if(B.test(t)){r.hasDynamicViewport=!0;return}if(H.test(t)){r.hasSuspendedDynamic=!0;return}else if(n.syncDynamicErrorWithStack||a.syncDynamicErrorWithStack){r.hasSyncDynamicErrors=!0;return}else{let n=function(e,t){let r=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.stack="Error: "+e+t,r}(`Route "${e}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,t);r.dynamicErrors.push(n);return}}}function G(e,t,r,n){let a,o,s;if(r.syncDynamicErrorWithStack?(a=r.syncDynamicErrorWithStack,o=r.syncDynamicExpression,s=!0===r.syncDynamicLogged):n.syncDynamicErrorWithStack?(a=n.syncDynamicErrorWithStack,o=n.syncDynamicExpression,s=!0===n.syncDynamicLogged):(a=null,o=void 0,s=!1),t.hasSyncDynamicErrors&&a)throw s||console.error(a),new i.StaticGenBailoutError;let c=t.dynamicErrors;if(c.length){for(let e=0;e<c.length;e++)console.error(c[e]);throw new i.StaticGenBailoutError}if(!t.hasSuspendedDynamic){if(t.hasDynamicMetadata){if(a)throw console.error(a),Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that could not finish rendering before ${o} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:!1,configurable:!0});throw Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:!1,configurable:!0})}else if(t.hasDynamicViewport){if(a)throw console.error(a),Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that could not finish rendering before ${o} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:!1,configurable:!0});throw Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:!1,configurable:!0})}}}},5239:(e,t,r)=>{"use strict";e.exports=r(846)},6056:(e,t,r)=>{"use strict";function n(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}}r.d(t,{A:()=>n})},6143:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return C},CACHE_ONE_YEAR:function(){return S},DOT_NEXT_ALIAS:function(){return k},ESLINT_DEFAULT_DIRS:function(){return z},GSP_NO_RETURNED_VALUE:function(){return q},GSSP_COMPONENT_MEMBER_ERROR:function(){return V},GSSP_NO_RETURNED_VALUE:function(){return G},INFINITE_CACHE:function(){return A},INSTRUMENTATION_HOOK_FILENAME:function(){return O},MATCHED_PATH_HEADER:function(){return a},MIDDLEWARE_FILENAME:function(){return R},MIDDLEWARE_LOCATION_REGEXP:function(){return P},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return E},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return y},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return m},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return v},NEXT_CACHE_TAGS_HEADER:function(){return g},NEXT_CACHE_TAG_MAX_ITEMS:function(){return w},NEXT_CACHE_TAG_MAX_LENGTH:function(){return _},NEXT_DATA_SUFFIX:function(){return p},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return f},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return b},NON_STANDARD_NODE_ENV:function(){return J},PAGES_DIR_ALIAS:function(){return T},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return L},ROOT_DIR_ALIAS:function(){return x},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return M},RSC_ACTION_ENCRYPTION_ALIAS:function(){return U},RSC_ACTION_PROXY_ALIAS:function(){return I},RSC_ACTION_VALIDATE_ALIAS:function(){return j},RSC_CACHE_WRAPPER_ALIAS:function(){return D},RSC_MOD_REF_PROXY_ALIAS:function(){return N},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return c},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return l},SERVER_PROPS_EXPORT_ERROR:function(){return K},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return H},SERVER_PROPS_SSG_CONFLICT:function(){return W},SERVER_RUNTIME:function(){return Y},SSG_FALLBACK_EXPORT_ERROR:function(){return X},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return $},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return B},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return F},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",a="x-matched-path",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=".prefetch.rsc",c=".segments",u=".segment.rsc",l=".rsc",d=".action",p=".json",f=".meta",h=".body",g="x-next-cache-tags",y="x-next-revalidated-tags",m="x-next-revalidate-tag-token",b="next-resume",w=128,_=256,v=1024,E="_N_T_",S=31536e3,A=0xfffffffe,R="middleware",P=`(?:src/)?${R}`,O="instrumentation",T="private-next-pages",k="private-dot-next",x="private-next-root-dir",C="private-next-app-dir",N="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",j="private-next-rsc-action-validate",I="private-next-rsc-server-reference",D="private-next-rsc-cache-wrapper",U="private-next-rsc-action-encryption",M="private-next-rsc-action-client-wrapper",L="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",$="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",H="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",W="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",B="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",K="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",q="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",G="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",F="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",V="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",J='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',X="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",z=["app","pages","components","lib","src"],Y={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...Z,GROUP:{builtinReact:[Z.reactServerComponents,Z.actionBrowser],serverOnly:[Z.reactServerComponents,Z.actionBrowser,Z.instrument,Z.middleware],neutralTarget:[Z.apiNode,Z.apiEdge],clientOnly:[Z.serverSideRendering,Z.appPagesBrowser],bundled:[Z.reactServerComponents,Z.actionBrowser,Z.serverSideRendering,Z.appPagesBrowser,Z.shared,Z.instrument,Z.middleware],appPages:[Z.reactServerComponents,Z.serverSideRendering,Z.appPagesBrowser,Z.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},6191:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_REQUEST_META:function(){return r},addRequestMeta:function(){return i},getRequestMeta:function(){return n},removeRequestMeta:function(){return o},setRequestMeta:function(){return a}});let r=Symbol.for("NextInternalRequestMeta");function n(e,t){let n=e[r]||{};return"string"==typeof t?n[t]:n}function a(e,t){return e[r]=t,t}function i(e,t,r){let i=n(e);return i[t]=r,a(e,i)}function o(e,t){let r=n(e);return delete r[t],a(e,r)}},6268:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return s},NextRequest:function(){return c}});let n=r(6608),a=r(7912),i=r(3611),o=r(3158),s=Symbol("internal request");class c extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,a.validateURL)(r),t.body&&"half"!==t.duplex&&(t.duplex="half"),e instanceof Request?super(e,t):super(r,t);let i=new n.NextURL(r,{headers:(0,a.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new o.RequestCookies(this.headers),nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get nextUrl(){return this[s].nextUrl}get page(){throw new i.RemovedPageError}get ua(){throw new i.RemovedUAError}get url(){return this[s].url}}},6280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(2584),a=r(9294),i=r(3033),o=r(4971),s=r(23),c=r(8388),u=r(6926),l=(r(4523),r(8719));function d(){let e=a.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return f(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,u=t;let n=p.get(u);if(n)return n;let a=(0,c.makeHangingPromise)(u.renderSignal,"`headers()`");return p.set(u,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},keys:{value:function(){let e="`headers().keys()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},values:{value:function(){let e="`headers().values()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},entries:{value:function(){let e="`headers().entries()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}}}),a}else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return f((0,i.getExpectedRequestStore)("headers").headers)}let p=new WeakMap;function f(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let g=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(y);function y(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},6608:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return l}});let n=r(3828),a=r(7853),i=r(1314),o=r(9938),s=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(s,"localhost"),t&&String(t).replace(s,"localhost"))}let u=Symbol("NextURLInternal");class l{constructor(e,t,r){let n,a;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,a=r||{}):a=r||t||{},this[u]={url:c(e,n??a.base),options:a,basePath:""},this.analyze()}analyze(){var e,t,r,a,s;let c=(0,o.getNextPathnameInfo)(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),l=(0,i.getHostname)(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(l):(0,n.detectDomainLocale)(null==(t=this[u].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,l);let d=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[u].options.nextConfig)||null==(a=s.i18n)?void 0:a.defaultLocale);this[u].url.pathname=c.pathname,this[u].defaultLocale=d,this[u].basePath=c.basePath??"",this[u].buildId=c.buildId,this[u].locale=c.locale??d,this[u].trailingSlash=c.trailingSlash}formatPathname(){return(0,a.formatNextPathnameInfo)({basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash})}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=c(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new l(String(this),this[u].options)}}},6897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return l},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(2836),a=r(9026),i=r(9121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function l(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(1120));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let i={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}o(e=>{try{s(i.current)}finally{i.current=null}})},7017:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return a}});let n=r(8631);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:i}=(0,n.parsePath)(e);return""+r+t+a+i}},7252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"after",{enumerable:!0,get:function(){return a}});let n=r(9294);function a(e){let t=n.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}},7348:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return a}});let n=r(8631);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:i}=(0,n.parsePath)(e);return""+t+r+a+i}},7576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(6897),a=r(9026),i=r(2765),o=r(8976),s=r(899),c=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class l extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7625:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{METADATA_BOUNDARY_NAME:function(){return r},OUTLET_BOUNDARY_NAME:function(){return a},VIEWPORT_BOUNDARY_NAME:function(){return n}});let r="__next_metadata_boundary__",n="__next_viewport_boundary__",a="__next_outlet_boundary__"},7719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_PATCH_SYMBOL:function(){return p},createPatchedFetcher:function(){return y},patchFetch:function(){return m},validateRevalidate:function(){return f},validateTags:function(){return h}});let n=r(4823),a=r(1289),i=r(6143),o=r(4971),s=r(8388),c=r(4436),u=r(3365),l=r(4523),d=r(9169),p=Symbol.for("next-patch");function f(e,t){try{let r;if(!1===e)r=i.INFINITE_CACHE;else if("number"==typeof e&&!isNaN(e)&&e>-1)r=e;else if(void 0!==e)throw Object.defineProperty(Error(`Invalid revalidate value "${e}" on "${t}", must be a non-negative number or false`),"__NEXT_ERROR_CODE",{value:"E179",enumerable:!1,configurable:!0});return r}catch(e){if(e instanceof Error&&e.message.includes("Invalid revalidate"))throw e;return}}function h(e,t){let r=[],n=[];for(let a=0;a<e.length;a++){let o=e[a];if("string"!=typeof o?n.push({tag:o,reason:"invalid type, must be a string"}):o.length>i.NEXT_CACHE_TAG_MAX_LENGTH?n.push({tag:o,reason:`exceeded max length of ${i.NEXT_CACHE_TAG_MAX_LENGTH}`}):r.push(o),r.length>i.NEXT_CACHE_TAG_MAX_ITEMS){console.warn(`Warning: exceeded max tag count for ${t}, dropped tags:`,e.slice(a).join(", "));break}}if(n.length>0)for(let{tag:e,reason:r}of(console.warn(`Warning: invalid tags passed to ${t}: `),n))console.log(`tag: "${e}" ${r}`);return r}function g(e,t){var r;if(e&&(null==(r=e.requestEndedState)?!void 0:!r.ended))((process.env.NEXT_DEBUG_BUILD||"1"===process.env.NEXT_SSG_FETCH_METRICS)&&e.isStaticGeneration||0)&&(e.fetchMetrics??=[],e.fetchMetrics.push({...t,end:performance.timeOrigin+performance.now(),idx:e.nextFetchId||0}))}function y(e,{workAsyncStorage:t,workUnitAsyncStorage:r}){let c=async(c,p)=>{var y,m;let b;try{(b=new URL(c instanceof Request?c.url:c)).username="",b.password=""}catch{b=void 0}let w=(null==b?void 0:b.href)??"",_=(null==p||null==(y=p.method)?void 0:y.toUpperCase())||"GET",v=(null==p||null==(m=p.next)?void 0:m.internal)===!0,E="1"===process.env.NEXT_OTEL_FETCH_DISABLED,S=v?void 0:performance.timeOrigin+performance.now(),A=t.getStore(),R=r.getStore(),P=R&&"prerender"===R.type?R.cacheSignal:null;P&&P.beginRead();let O=(0,a.getTracer)().trace(v?n.NextNodeServerSpan.internalFetch:n.AppRenderSpan.fetch,{hideSpan:E,kind:a.SpanKind.CLIENT,spanName:["fetch",_,w].filter(Boolean).join(" "),attributes:{"http.url":w,"http.method":_,"net.peer.name":null==b?void 0:b.hostname,"net.peer.port":(null==b?void 0:b.port)||void 0}},async()=>{var t;let r,n,a,y;if(v||!A||A.isDraftMode)return e(c,p);let m=c&&"object"==typeof c&&"string"==typeof c.method,b=e=>(null==p?void 0:p[e])||(m?c[e]:null),_=e=>{var t,r,n;return void 0!==(null==p||null==(t=p.next)?void 0:t[e])?null==p||null==(r=p.next)?void 0:r[e]:m?null==(n=c.next)?void 0:n[e]:void 0},E=_("revalidate"),O=h(_("tags")||[],`fetch ${c.toString()}`),T=R&&("cache"===R.type||"prerender"===R.type||"prerender-ppr"===R.type||"prerender-legacy"===R.type)?R:void 0;if(T&&Array.isArray(O)){let e=T.tags??(T.tags=[]);for(let t of O)e.includes(t)||e.push(t)}let k=null==R?void 0:R.implicitTags,x=R&&"unstable-cache"===R.type?"force-no-store":A.fetchCache,C=!!A.isUnstableNoStore,N=b("cache"),j="";"string"==typeof N&&void 0!==E&&("force-cache"===N&&0===E||"no-store"===N&&(E>0||!1===E))&&(r=`Specified "cache: ${N}" and "revalidate: ${E}", only one should be specified.`,N=void 0,E=void 0);let I="no-cache"===N||"no-store"===N||"force-no-store"===x||"only-no-store"===x,D=!x&&!N&&!E&&A.forceDynamic;"force-cache"===N&&void 0===E?E=!1:(null==R?void 0:R.type)!=="cache"&&(I||D)&&(E=0),("no-cache"===N||"no-store"===N)&&(j=`cache: ${N}`),y=f(E,A.route);let U=b("headers"),M="function"==typeof(null==U?void 0:U.get)?U:new Headers(U||{}),L=M.get("authorization")||M.get("cookie"),$=!["get","head"].includes((null==(t=b("method"))?void 0:t.toLowerCase())||"get"),H=void 0==x&&(void 0==N||"default"===N)&&void 0==E,W=H&&!A.isPrerendering||(L||$)&&T&&0===T.revalidate;if(H&&void 0!==R&&"prerender"===R.type)return P&&(P.endRead(),P=null),(0,s.makeHangingPromise)(R.renderSignal,"fetch()");switch(x){case"force-no-store":j="fetchCache = force-no-store";break;case"only-no-store":if("force-cache"===N||void 0!==y&&y>0)throw Object.defineProperty(Error(`cache: 'force-cache' used on fetch for ${w} with 'export const fetchCache = 'only-no-store'`),"__NEXT_ERROR_CODE",{value:"E448",enumerable:!1,configurable:!0});j="fetchCache = only-no-store";break;case"only-cache":if("no-store"===N)throw Object.defineProperty(Error(`cache: 'no-store' used on fetch for ${w} with 'export const fetchCache = 'only-cache'`),"__NEXT_ERROR_CODE",{value:"E521",enumerable:!1,configurable:!0});break;case"force-cache":(void 0===E||0===E)&&(j="fetchCache = force-cache",y=i.INFINITE_CACHE)}if(void 0===y?"default-cache"!==x||C?"default-no-store"===x?(y=0,j="fetchCache = default-no-store"):C?(y=0,j="noStore call"):W?(y=0,j="auto no cache"):(j="auto cache",y=T?T.revalidate:i.INFINITE_CACHE):(y=i.INFINITE_CACHE,j="fetchCache = default-cache"):j||(j=`revalidate: ${y}`),!(A.forceStatic&&0===y)&&!W&&T&&y<T.revalidate){if(0===y)if(R&&"prerender"===R.type)return P&&(P.endRead(),P=null),(0,s.makeHangingPromise)(R.renderSignal,"fetch()");else(0,o.markCurrentScopeAsDynamic)(A,R,`revalidate: 0 fetch ${c} ${A.route}`);T&&E===y&&(T.revalidate=y)}let B="number"==typeof y&&y>0,{incrementalCache:K}=A,q=(null==R?void 0:R.type)==="request"||(null==R?void 0:R.type)==="cache"?R:void 0;if(K&&(B||(null==q?void 0:q.serverComponentsHmrCache)))try{n=await K.generateCacheKey(w,m?c:p)}catch(e){console.error("Failed to generate cache key for",c)}let G=A.nextFetchId??1;A.nextFetchId=G+1;let F=()=>Promise.resolve(),V=async(t,a)=>{let o=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...t?[]:["signal"]];if(m){let e=c,t={body:e._ogBody||e.body};for(let r of o)t[r]=e[r];c=new Request(e.url,t)}else if(p){let{_ogBody:e,body:r,signal:n,...a}=p;p={...a,body:e||r,signal:t?void 0:n}}let s={...p,next:{...null==p?void 0:p.next,fetchType:"origin",fetchIdx:G}};return e(c,s).then(async e=>{if(!t&&S&&g(A,{start:S,url:w,cacheReason:a||j,cacheStatus:0===y||a?"skip":"miss",cacheWarning:r,status:e.status,method:s.method||"GET"}),200===e.status&&K&&n&&(B||(null==q?void 0:q.serverComponentsHmrCache))){let t=y>=i.INFINITE_CACHE?i.CACHE_ONE_YEAR:y;if(R&&"prerender"===R.type){let r=await e.arrayBuffer(),a={headers:Object.fromEntries(e.headers.entries()),body:Buffer.from(r).toString("base64"),status:e.status,url:e.url};return await K.set(n,{kind:u.CachedRouteKind.FETCH,data:a,revalidate:t},{fetchCache:!0,fetchUrl:w,fetchIdx:G,tags:O}),await F(),new Response(r,{headers:e.headers,status:e.status,statusText:e.statusText})}{let[r,a]=(0,d.cloneResponse)(e);return r.arrayBuffer().then(async e=>{var a;let i=Buffer.from(e),o={headers:Object.fromEntries(r.headers.entries()),body:i.toString("base64"),status:r.status,url:r.url};null==q||null==(a=q.serverComponentsHmrCache)||a.set(n,o),B&&await K.set(n,{kind:u.CachedRouteKind.FETCH,data:o,revalidate:t},{fetchCache:!0,fetchUrl:w,fetchIdx:G,tags:O})}).catch(e=>console.warn("Failed to set fetch cache",c,e)).finally(F),a}}return await F(),e}).catch(e=>{throw F(),e})},J=!1,X=!1;if(n&&K){let e;if((null==q?void 0:q.isHmrRefresh)&&q.serverComponentsHmrCache&&(e=q.serverComponentsHmrCache.get(n),X=!0),B&&!e){F=await K.lock(n);let t=A.isOnDemandRevalidate?null:await K.get(n,{kind:u.IncrementalCacheKind.FETCH,revalidate:y,fetchUrl:w,fetchIdx:G,tags:O,softTags:null==k?void 0:k.tags});if(H&&R&&"prerender"===R.type&&await (0,l.waitAtLeastOneReactRenderTask)(),t?await F():a="cache-control: no-cache (hard refresh)",(null==t?void 0:t.value)&&t.value.kind===u.CachedRouteKind.FETCH)if(A.isRevalidate&&t.isStale)J=!0;else{if(t.isStale&&(A.pendingRevalidates??={},!A.pendingRevalidates[n])){let e=V(!0).then(async e=>({body:await e.arrayBuffer(),headers:e.headers,status:e.status,statusText:e.statusText})).finally(()=>{A.pendingRevalidates??={},delete A.pendingRevalidates[n||""]});e.catch(console.error),A.pendingRevalidates[n]=e}e=t.value.data}}if(e){S&&g(A,{start:S,url:w,cacheReason:j,cacheStatus:X?"hmr":"hit",cacheWarning:r,status:e.status||200,method:(null==p?void 0:p.method)||"GET"});let t=new Response(Buffer.from(e.body,"base64"),{headers:e.headers,status:e.status});return Object.defineProperty(t,"url",{value:e.url}),t}}if(A.isStaticGeneration&&p&&"object"==typeof p){let{cache:e}=p;if("no-store"===e)if(R&&"prerender"===R.type)return P&&(P.endRead(),P=null),(0,s.makeHangingPromise)(R.renderSignal,"fetch()");else(0,o.markCurrentScopeAsDynamic)(A,R,`no-store fetch ${c} ${A.route}`);let t="next"in p,{next:r={}}=p;if("number"==typeof r.revalidate&&T&&r.revalidate<T.revalidate){if(0===r.revalidate)if(R&&"prerender"===R.type)return(0,s.makeHangingPromise)(R.renderSignal,"fetch()");else(0,o.markCurrentScopeAsDynamic)(A,R,`revalidate: 0 fetch ${c} ${A.route}`);A.forceStatic&&0===r.revalidate||(T.revalidate=r.revalidate)}t&&delete p.next}if(!n||!J)return V(!1,a);{let e=n;A.pendingRevalidates??={};let t=A.pendingRevalidates[e];if(t){let e=await t;return new Response(e.body,{headers:e.headers,status:e.status,statusText:e.statusText})}let r=V(!0,a).then(d.cloneResponse);return(t=r.then(async e=>{let t=e[0];return{body:await t.arrayBuffer(),headers:t.headers,status:t.status,statusText:t.statusText}}).finally(()=>{var t;(null==(t=A.pendingRevalidates)?void 0:t[e])&&delete A.pendingRevalidates[e]})).catch(()=>{}),A.pendingRevalidates[e]=t,r.then(e=>e[1])}});if(P)try{return await O}finally{P&&P.endRead()}return O};return c.__nextPatched=!0,c.__nextGetStaticStore=()=>t,c._nextOriginalFetch=e,globalThis[p]=!0,c}function m(e){if(!0===globalThis[p])return;let t=(0,c.createDedupeFetch)(globalThis.fetch);globalThis.fetch=y(t,e)}},7778:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(7855),a=r(2471);class i{static fromStatic(e){return new i(e,{metadata:{}})}constructor(e,{contentType:t,waitUntil:r,metadata:n}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}assignMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedBuffer(e=!1){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:!1,configurable:!0});if("string"!=typeof this.response){if(!e)throw Object.defineProperty(Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:!1,configurable:!0});return(0,n.streamToBuffer)(this.readable)}return Buffer.from(this.response)}toUnchunkedString(e=!1){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:!1,configurable:!0});if("string"!=typeof this.response){if(!e)throw Object.defineProperty(Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:!1,configurable:!0});return(0,n.streamToString)(this.readable)}return this.response}get readable(){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E14",enumerable:!1,configurable:!0});if("string"==typeof this.response)throw Object.defineProperty(Error("Invariant: static responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E151",enumerable:!1,configurable:!0});return Buffer.isBuffer(this.response)?(0,n.streamFromBuffer)(this.response):Array.isArray(this.response)?(0,n.chainStreams)(...this.response):this.response}chain(e){let t;if(null===this.response)throw Object.defineProperty(Error("Invariant: response is null. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E258",enumerable:!1,configurable:!0});(t="string"==typeof this.response?[(0,n.streamFromString)(this.response)]:Array.isArray(this.response)?this.response:Buffer.isBuffer(this.response)?[(0,n.streamFromBuffer)(this.response)]:[this.response]).push(e),this.response=t}async pipeTo(e){try{await this.readable.pipeTo(e,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await e.close()}catch(t){if((0,a.isAbortError)(t))return void await e.abort(t);throw t}}async pipeToNodeResponse(e){await (0,a.pipeToNodeResponse)(this.readable,e,this.waitUntil)}}},7853:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(2887),a=r(7348),i=r(7017),o=r(9034);function s(e){let t=(0,o.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,i.addPathSuffix)((0,a.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,a.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,i.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},7855:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{chainStreams:function(){return p},continueDynamicHTMLResume:function(){return O},continueDynamicPrerender:function(){return R},continueFizzStream:function(){return A},continueStaticPrerender:function(){return P},createBufferedTransformStream:function(){return m},createDocumentClosingStream:function(){return T},createRootLayoutValidatorStream:function(){return S},renderToInitialFizzStream:function(){return b},streamFromBuffer:function(){return h},streamFromString:function(){return f},streamToBuffer:function(){return g},streamToString:function(){return y}});let n=r(1289),a=r(4823),i=r(366),o=r(4523),s=r(7866),c=r(8684),u=r(4113);function l(){}let d=new TextEncoder;function p(...e){if(0===e.length)throw Object.defineProperty(Error("Invariant: chainStreams requires at least one stream"),"__NEXT_ERROR_CODE",{value:"E437",enumerable:!1,configurable:!0});if(1===e.length)return e[0];let{readable:t,writable:r}=new TransformStream,n=e[0].pipeTo(r,{preventClose:!0}),a=1;for(;a<e.length-1;a++){let t=e[a];n=n.then(()=>t.pipeTo(r,{preventClose:!0}))}let i=e[a];return(n=n.then(()=>i.pipeTo(r))).catch(l),t}function f(e){return new ReadableStream({start(t){t.enqueue(d.encode(e)),t.close()}})}function h(e){return new ReadableStream({start(t){t.enqueue(e),t.close()}})}async function g(e){let t=e.getReader(),r=[];for(;;){let{done:e,value:n}=await t.read();if(e)break;r.push(n)}return Buffer.concat(r)}async function y(e,t){let r=new TextDecoder("utf-8",{fatal:!0}),n="";for await(let a of e){if(null==t?void 0:t.aborted)return n;n+=r.decode(a,{stream:!0})}return n+r.decode()}function m(){let e,t=[],r=0,n=n=>{if(e)return;let a=new i.DetachedPromise;e=a,(0,o.scheduleImmediate)(()=>{try{let e=new Uint8Array(r),a=0;for(let r=0;r<t.length;r++){let n=t[r];e.set(n,a),a+=n.byteLength}t.length=0,r=0,n.enqueue(e)}catch{}finally{e=void 0,a.resolve()}})};return new TransformStream({transform(e,a){t.push(e),r+=e.byteLength,n(a)},flush(){if(e)return e.promise}})}function b({ReactDOMServer:e,element:t,streamOptions:r}){return(0,n.getTracer)().trace(a.AppRenderSpan.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}function w(e){let t=!1,r=!1;return new TransformStream({async transform(n,a){r=!0;let i=await e();if(t){if(i){let e=d.encode(i);a.enqueue(e)}a.enqueue(n)}else{let e=(0,c.indexOfUint8Array)(n,s.ENCODED_TAGS.CLOSED.HEAD);if(-1!==e){if(i){let t=d.encode(i),r=new Uint8Array(n.length+t.length);r.set(n.slice(0,e)),r.set(t,e),r.set(n.slice(e),e+t.length),a.enqueue(r)}else a.enqueue(n);t=!0}else i&&a.enqueue(d.encode(i)),a.enqueue(n),t=!0}},async flush(t){if(r){let r=await e();r&&t.enqueue(d.encode(r))}}})}function _(e){let t=null,r=!1;async function n(n){if(t)return;let a=e.getReader();await (0,o.atLeastOneTask)();try{for(;;){let{done:e,value:t}=await a.read();if(e){r=!0;return}n.enqueue(t)}}catch(e){n.error(e)}}return new TransformStream({transform(e,r){r.enqueue(e),t||(t=n(r))},flush(e){if(!r)return t||n(e)}})}let v="</body></html>";function E(){let e=!1;return new TransformStream({transform(t,r){if(e)return r.enqueue(t);let n=(0,c.indexOfUint8Array)(t,s.ENCODED_TAGS.CLOSED.BODY_AND_HTML);if(n>-1){if(e=!0,t.length===s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length)return;let a=t.slice(0,n);if(r.enqueue(a),t.length>s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length+n){let e=t.slice(n+s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length);r.enqueue(e)}}else r.enqueue(t)},flush(e){e.enqueue(s.ENCODED_TAGS.CLOSED.BODY_AND_HTML)}})}function S(){let e=!1,t=!1;return new TransformStream({async transform(r,n){!e&&(0,c.indexOfUint8Array)(r,s.ENCODED_TAGS.OPENING.HTML)>-1&&(e=!0),!t&&(0,c.indexOfUint8Array)(r,s.ENCODED_TAGS.OPENING.BODY)>-1&&(t=!0),n.enqueue(r)},flush(r){let n=[];e||n.push("html"),t||n.push("body"),n.length&&r.enqueue(d.encode(`<html id="__next_error__">
            <template
              data-next-error-message="Missing ${n.map(e=>`<${e}>`).join(n.length>1?" and ":"")} tags in the root layout.
Read more at https://nextjs.org/docs/messages/missing-root-layout-tags""
              data-next-error-digest="${u.MISSING_ROOT_TAGS_ERROR}"
              data-next-error-stack=""
            ></template>
          `))}})}async function A(e,{suffix:t,inlinedDataStream:r,isStaticGeneration:n,getServerInsertedHTML:a,getServerInsertedMetadata:s,validateRootLayout:c}){let u=t?t.split(v,1)[0]:null;n&&"allReady"in e&&await e.allReady;var l=[m(),w(s),null!=u&&u.length>0?function(e){let t,r=!1,n=r=>{let n=new i.DetachedPromise;t=n,(0,o.scheduleImmediate)(()=>{try{r.enqueue(d.encode(e))}catch{}finally{t=void 0,n.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),r||(r=!0,n(t))},flush(n){if(t)return t.promise;r||n.enqueue(d.encode(e))}})}(u):null,r?_(r):null,c?S():null,E(),w(a)];let p=e;for(let e of l)e&&(p=p.pipeThrough(e));return p}async function R(e,{getServerInsertedHTML:t,getServerInsertedMetadata:r}){return e.pipeThrough(m()).pipeThrough(new TransformStream({transform(e,t){(0,c.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.BODY_AND_HTML)||(0,c.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.BODY)||(0,c.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.HTML)||(e=(0,c.removeFromUint8Array)(e,s.ENCODED_TAGS.CLOSED.BODY),e=(0,c.removeFromUint8Array)(e,s.ENCODED_TAGS.CLOSED.HTML),t.enqueue(e))}})).pipeThrough(w(t)).pipeThrough(w(r))}async function P(e,{inlinedDataStream:t,getServerInsertedHTML:r,getServerInsertedMetadata:n}){return e.pipeThrough(m()).pipeThrough(w(r)).pipeThrough(w(n)).pipeThrough(_(t)).pipeThrough(E())}async function O(e,{inlinedDataStream:t,getServerInsertedHTML:r,getServerInsertedMetadata:n}){return e.pipeThrough(m()).pipeThrough(w(r)).pipeThrough(w(n)).pipeThrough(_(t)).pipeThrough(E())}function T(){return f(v)}},7866:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ENCODED_TAGS",{enumerable:!0,get:function(){return r}});let r={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}}},7912:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return a},normalizeNextQueryParam:function(){return c},splitCookiesString:function(){return i},toNodeOutgoingHttpHeaders:function(){return o},validateURL:function(){return s}});let n=r(6143);function a(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function i(e){var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function o(e){let t={},r=[];if(e)for(let[n,a]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...i(a)),t[n]=1===r.length?r[0]:r):t[n]=a;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function c(e){for(let t of[n.NEXT_QUERY_PARAM_PREFIX,n.NEXT_INTERCEPTION_MARKER_PREFIX])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},8088:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouteKind",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},8388:(e,t)=>{"use strict";function r(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isHangingPromiseRejectionError:function(){return r},makeHangingPromise:function(){return o}});let n="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=n}}let i=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new a(t));{let r=new Promise((r,n)=>{let o=n.bind(null,new a(t)),s=i.get(e);if(s)s.push(o);else{let t=[o];i.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(s),r}}function s(){}},8479:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return a}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8631:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},8684:(e,t)=>{"use strict";function r(e,t){if(0===t.length)return 0;if(0===e.length||t.length>e.length)return -1;for(let r=0;r<=e.length-t.length;r++){let n=!0;for(let a=0;a<t.length;a++)if(e[r+a]!==t[a]){n=!1;break}if(n)return r}return -1}function n(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function a(e,t){let n=r(e,t);if(0===n)return e.subarray(t.length);if(!(n>-1))return e;{let r=new Uint8Array(e.length-t.length);return r.set(e.slice(0,n)),r.set(e.slice(n+t.length),n),r}}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{indexOfUint8Array:function(){return r},isEquivalentUint8Arrays:function(){return n},removeFromUint8Array:function(){return a}})},8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return a},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return i}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),a="NEXT_HTTP_ERROR_FALLBACK";function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===a&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return i},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let n=r(23),a=r(3295);function i(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},8737:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Batcher",{enumerable:!0,get:function(){return a}});let n=r(366);class a{constructor(e,t=e=>e()){this.cacheKeyFn=e,this.schedulerFn=t,this.pending=new Map}static create(e){return new a(null==e?void 0:e.cacheKeyFn,null==e?void 0:e.schedulerFn)}async batch(e,t){let r=this.cacheKeyFn?await this.cacheKeyFn(e):e;if(null===r)return t(r,Promise.resolve);let a=this.pending.get(r);if(a)return a;let{promise:i,resolve:o,reject:s}=new n.DetachedPromise;return this.pending.set(r,i),this.schedulerFn(async()=>{try{let e=await t(r,o);o(e)}catch(e){s(e)}finally{this.pending.delete(r)}}),i}}},8976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return a},RedirectType:function(){return i},isRedirectError:function(){return o}});let n=r(2836),a="NEXT_REDIRECT";var i=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===a&&("replace"===i||"push"===i)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return i}});let n=r(7348),a=r(2829);function i(e,t,r,i){if(!t||t===r)return e;let o=e.toLowerCase();return!i&&((0,a.pathHasPrefix)(o,"/api")||(0,a.pathHasPrefix)(o,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},9098:(e,t)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return r}})},9169:(e,t)=>{"use strict";function r(e){if(!e.body)return[e,e];let[t,r]=e.body.tee(),n=new Response(t,{status:e.status,statusText:e.statusText,headers:e.headers});Object.defineProperty(n,"url",{value:e.url});let a=new Response(r,{status:e.status,statusText:e.statusText,headers:e.headers});return Object.defineProperty(a,"url",{value:e.url}),[n,a]}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"cloneResponse",{enumerable:!0,get:function(){return r}})},9229:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return a}});let n=r(2829);function a(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},9859:(e,t,r)=>{"use strict";let n,a,i,o,s,c,u;r.d(t,{Ay:()=>of});var l={};r.r(l),r.d(l,{q:()=>tF,l:()=>tX});var d=function(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r},p=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function f(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class h{constructor(e,t,r){if(r_.add(this),rv.set(this,{}),rE.set(this,void 0),rS.set(this,void 0),d(this,rS,r,"f"),d(this,rE,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(p(this,rv,"f")[e]=r)}get value(){return Object.keys(p(this,rv,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>p(this,rv,"f")[e]).join("")}chunk(e,t){let r=p(this,r_,"m",rR).call(this);for(let n of p(this,r_,"m",rA).call(this,{name:p(this,rE,"f").name,value:e,options:{...p(this,rE,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(p(this,r_,"m",rR).call(this))}}rv=new WeakMap,rE=new WeakMap,rS=new WeakMap,r_=new WeakSet,rA=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return p(this,rv,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,a=e.value.substr(3936*n,3936);r.push({...e,name:t,value:a}),p(this,rv,"f")[t]=a}return p(this,rS,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rR=function(){let e={};for(let t in p(this,rv,"f"))delete p(this,rv,"f")?.[t],e[t]={name:t,value:"",options:{...p(this,rE,"f").options,maxAge:0}};return e};class g extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class y extends g{}y.kind="signIn";class m extends g{}m.type="AdapterError";class b extends g{}b.type="AccessDenied";class w extends g{}w.type="CallbackRouteError";class _ extends g{}_.type="ErrorPageLoop";class v extends g{}v.type="EventError";class E extends g{}E.type="InvalidCallbackUrl";class S extends y{constructor(){super(...arguments),this.code="credentials"}}S.type="CredentialsSignin";class A extends g{}A.type="InvalidEndpoints";class R extends g{}R.type="InvalidCheck";class P extends g{}P.type="JWTSessionError";class O extends g{}O.type="MissingAdapter";class T extends g{}T.type="MissingAdapterMethods";class k extends g{}k.type="MissingAuthorize";class x extends g{}x.type="MissingSecret";class C extends y{}C.type="OAuthAccountNotLinked";class N extends y{}N.type="OAuthCallbackError";class j extends g{}j.type="OAuthProfileParseError";class I extends g{}I.type="SessionTokenError";class D extends y{}D.type="OAuthSignInError";class U extends y{}U.type="EmailSignInError";class M extends g{}M.type="SignOutError";class L extends g{}L.type="UnknownAction";class $ extends g{}$.type="UnsupportedStrategy";class H extends g{}H.type="InvalidProvider";class W extends g{}W.type="UntrustedHost";class B extends g{}B.type="Verification";class K extends y{}K.type="MissingCSRF";let q=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class G extends g{}G.type="DuplicateConditionalUI";class F extends g{}F.type="MissingWebAuthnAutocomplete";class V extends g{}V.type="WebAuthnVerificationError";class J extends y{}J.type="AccountNotLinked";class X extends g{}X.type="ExperimentalFeatureNotEnabled";let z=!1;function Y(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let Z=!1,Q=!1,ee=!1,et=["createVerificationToken","useVerificationToken","getUserByEmail"],er=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],en=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var ea=r(5511);let ei=(e,t,r,n,a)=>{let i=parseInt(e.substr(3),10)>>3||20,o=(0,ea.createHmac)(e,r.byteLength?r:new Uint8Array(i)).update(t).digest(),s=Math.ceil(a/i),c=new Uint8Array(i*s+n.byteLength+1),u=0,l=0;for(let t=1;t<=s;t++)c.set(n,l),c[l+n.byteLength]=t,c.set((0,ea.createHmac)(e,o).update(c.subarray(u,l+n.byteLength+1)).digest(),l),u=l,l+=i;return c.slice(0,a)};"function"!=typeof ea.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{ea.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let eo=async(e,t,r,a,i)=>(n||ei)(e,t,r,a,i);function es(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function ec(e,t,r,n,a){return eo(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=es(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),es(r,"salt"),function(e){let t=es(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(a,e))}let eu=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},el=new TextEncoder,ed=new TextDecoder;function ep(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function ef(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function eh(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return ef(r,t,0),ef(r,e%0x100000000,4),r}function eg(e){let t=new Uint8Array(4);return ef(t,e),t}function ey(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:ed.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=ed.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function em(e){let t=e;return("string"==typeof t&&(t=el.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class eb extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ew extends eb{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class e_ extends eb{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ev extends eb{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class eE extends eb{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class eS extends eb{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class eA extends eb{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eR extends eb{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eP extends eb{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eO extends eb{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eT(e){if(!ek(e))throw Error("CryptoKey instance expected")}function ek(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function ex(e){return e?.[Symbol.toStringTag]==="KeyObject"}let eC=e=>ek(e)||ex(e),eN=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function ej(e){return eN(e)&&"string"==typeof e.kty}function eI(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eD=(e,...t)=>eI("Key must be ",e,...t);function eU(e,t,...r){return eI(`Key for the ${e} algorithm must be `,t,...r)}async function eM(e){if(ex(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:em(e)};if(!ek(e))throw TypeError(eD(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...i}=await crypto.subtle.exportKey("jwk",e);return i}async function eL(e){return eM(e)}let e$=(e,t)=>{if("string"!=typeof e||!e)throw new eP(`${t} missing or invalid`)};async function eH(e,t){let r,n;if(ej(e))r=e;else if(eC(e))r=await eL(e);else throw TypeError(eD(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":e$(r.crv,'"crv" (Curve) Parameter'),e$(r.x,'"x" (X Coordinate) Parameter'),e$(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":e$(r.crv,'"crv" (Subtype of Key Pair) Parameter'),e$(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":e$(r.e,'"e" (Exponent) Parameter'),e$(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":e$(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new eE('"kty" (Key Type) Parameter missing or unsupported')}let a=el.encode(JSON.stringify(n));return em(await eu(t,a))}let eW=Symbol();function eB(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new eE(`Unsupported JWE Algorithm: ${e}`)}}let eK=e=>crypto.getRandomValues(new Uint8Array(eB(e)>>3)),eq=(e,t)=>{if(t.length<<3!==eB(e))throw new eA("Invalid Initialization Vector length")},eG=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new eA(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eF(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eV(e,t){return e.name===t}function eJ(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eV(e.algorithm,"AES-GCM"))throw eF("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eF(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eV(e.algorithm,"AES-KW"))throw eF("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eF(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eF("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eV(e.algorithm,"PBKDF2"))throw eF("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eV(e.algorithm,"RSA-OAEP"))throw eF("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eF(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,a=r;if(a&&!n.usages.includes(a))throw TypeError(`CryptoKey does not support this operation, its usages must include ${a}.`)}async function eX(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(eD(r,"Uint8Array"));let i=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(i>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),u=ep(a,n,c,eh(a.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,u)).slice(0,i>>3)),iv:n}}async function ez(e,t,r,n,a){let i;r instanceof Uint8Array?i=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eJ(r,e,"encrypt"),i=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},i,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let eY=async(e,t,r,n,a)=>{if(!ek(r)&&!(r instanceof Uint8Array))throw TypeError(eD(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?eq(e,n):n=eK(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eG(r,parseInt(e.slice(-3),10)),eX(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eG(r,parseInt(e.slice(1,4),10)),ez(e,t,r,n,a);default:throw new eE("Unsupported JWE Content Encryption Algorithm")}};function eZ(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function eQ(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(eJ(e,t,r),e)}async function e0(e,t,r){let n=await eQ(t,e,"wrapKey");eZ(n,e);let a=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",a,n,"AES-KW"))}async function e1(e,t,r){let n=await eQ(t,e,"unwrapKey");eZ(n,e);let a=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",a))}function e2(e){return ep(eg(e.length),e)}async function e3(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(eg(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await eu("sha256",n),32*t)}return a.slice(0,t>>3)}async function e8(e,t,r,n,a=new Uint8Array(0),i=new Uint8Array(0)){let o;eJ(e,"ECDH"),eJ(t,"ECDH","deriveBits");let s=ep(e2(el.encode(r)),e2(a),e2(i),eg(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e3(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e6(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e5=(e,t)=>ep(el.encode(e),new Uint8Array([0]),t);async function e4(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new eA("PBES2 Salt Input must be 8 or more octets");let a=e5(t,e),i=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(eJ(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,i))}async function e9(e,t,r,n=2048,a=crypto.getRandomValues(new Uint8Array(16))){let i=await e4(a,e,n,t);return{encryptedKey:await e0(e.slice(-6),i,r),p2c:n,p2s:em(a)}}async function e7(e,t,r,n,a){let i=await e4(a,e,n,t);return e1(e.slice(-6),i,r)}let te=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},tt=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new eE(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function tr(e,t,r){return eJ(t,e,"encrypt"),te(e,t),new Uint8Array(await crypto.subtle.encrypt(tt(e),t,r))}async function tn(e,t,r){return eJ(t,e,"decrypt"),te(e,t),new Uint8Array(await crypto.subtle.decrypt(tt(e),t,r))}let ta=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new eE('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eE('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eE('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new eE('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},ti=async(e,t,r,n=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let o=await ta({...t,alg:r});return n&&Object.freeze(e),i?i[r]=o:a.set(e,{[r]:o}),o},to=(e,t)=>{let r,n=(a||=new WeakMap).get(e);if(n?.[t])return n[t];let i="public"===e.type,o=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:a.set(e,{[t]:r}),r},ts=async(e,t)=>{if(e instanceof Uint8Array||ek(e))return e;if(ex(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return to(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return ti(e,r,t)}if(ej(e))return e.k?ey(e.k):ti(e,e,t,!0);throw Error("unreachable")};function tc(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new eE(`Unsupported JWE Algorithm: ${e}`)}}let tu=e=>crypto.getRandomValues(new Uint8Array(tc(e)>>3));async function tl(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),a=new Uint8Array(await crypto.subtle.sign(r,n,e)),i=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=a[s]^i[s];return 0===o}async function td(e,t,r,n,a,i){let o,s;if(!(t instanceof Uint8Array))throw TypeError(eD(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),u=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),l=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=ep(i,n,r,eh(i.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",l,d)).slice(0,c>>3));try{o=await tl(a,p)}catch{}if(!o)throw new eS;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},u,r))}catch{}if(!s)throw new eS;return s}async function tp(e,t,r,n,a,i){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eJ(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},o,ep(r,a)))}catch{throw new eS}}let tf=async(e,t,r,n,a,i)=>{if(!ek(t)&&!(t instanceof Uint8Array))throw TypeError(eD(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new eA("JWE Initialization Vector missing");if(!a)throw new eA("JWE Authentication Tag missing");switch(eq(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eG(t,parseInt(e.slice(-3),10)),td(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eG(t,parseInt(e.slice(1,4),10)),tp(e,t,r,n,a,i);default:throw new eE("Unsupported JWE Content Encryption Algorithm")}};async function th(e,t,r,n){let a=e.slice(0,7),i=await eY(a,r,t,n,new Uint8Array(0));return{encryptedKey:i.ciphertext,iv:em(i.iv),tag:em(i.tag)}}async function tg(e,t,r,n,a){return tf(e.slice(0,7),t,r,n,a,new Uint8Array(0))}let ty=async(e,t,r,n,a={})=>{let i,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(eT(r),!e6(r))throw new eE("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:u,apv:l}=a;c=a.epk?await ts(a.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:f,kty:h}=await eL(c),g=await e8(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?tc(t):parseInt(e.slice(-5,-2),10),u,l);if(o={epk:{x:d,crv:f,kty:h}},"EC"===h&&(o.epk.y=p),u&&(o.apu=em(u)),l&&(o.apv=em(l)),"ECDH-ES"===e){s=g;break}s=n||tu(t);let y=e.slice(-6);i=await e0(y,g,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||tu(t),eT(r),i=await tr(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||tu(t);let{p2c:c,p2s:u}=a;({encryptedKey:i,...o}=await e9(e,r,s,c,u));break}case"A128KW":case"A192KW":case"A256KW":s=n||tu(t),i=await e0(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||tu(t);let{iv:c}=a;({encryptedKey:i,...o}=await th(e,r,s,c));break}default:throw new eE('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}},tm=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tb=(e,t,r,n,a)=>{let i;if(void 0!==a.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new eE(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tw=e=>e?.[Symbol.toStringTag],t_=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},tv=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(ej(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&t_(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eC(t))throw TypeError(eU(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tw(t)} instances for symmetric algorithms must be of type "secret"`)}},tE=(e,t,r)=>{if(ej(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&t_(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&t_(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eC(t))throw TypeError(eU(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tw(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tw(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tw(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tw(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tw(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tS=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tv(e,t,r):tE(e,t,r)};class tA{#e;#t;#r;#n;#a;#i;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#a=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,a,i,o;if(!this.#t&&!this.#n&&!this.#r)throw new eA("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tm(this.#t,this.#n,this.#r))throw new eA("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(tb(eA,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new eE('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:u}=s;if("string"!=typeof c||!c)throw new eA('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof u||!u)throw new eA('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#i&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);tS("dir"===c?u:c,e,"encrypt");{let a,i=await ts(e,c);({cek:n,encryptedKey:r,parameters:a}=await ty(c,u,i,this.#i,this.#s)),a&&(t&&eW in t?this.#n?this.#n={...this.#n,...a}:this.setUnprotectedHeader(a):this.#t?this.#t={...this.#t,...a}:this.setProtectedHeader(a))}i=this.#t?el.encode(em(JSON.stringify(this.#t))):el.encode(""),this.#a?(o=em(this.#a),a=ep(i,el.encode("."),el.encode(o))):a=i;let{ciphertext:l,tag:d,iv:p}=await eY(u,this.#e,n,this.#o,a),f={ciphertext:em(l)};return p&&(f.iv=em(p)),d&&(f.tag=em(d)),r&&(f.encrypted_key=em(r)),o&&(f.aad=o),this.#t&&(f.protected=ed.decode(i)),this.#r&&(f.unprotected=this.#r),this.#n&&(f.header=this.#n),f}}class tR{#c;constructor(e){this.#c=new tA(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tP=e=>Math.floor(e.getTime()/1e3),tO=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tT=e=>{let t,r=tO.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tk(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tx=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tC=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tN{#u;constructor(e){if(!eN(e))throw TypeError("JWT Claims Set MUST be an object");this.#u=structuredClone(e)}data(){return el.encode(JSON.stringify(this.#u))}get iss(){return this.#u.iss}set iss(e){this.#u.iss=e}get sub(){return this.#u.sub}set sub(e){this.#u.sub=e}get aud(){return this.#u.aud}set aud(e){this.#u.aud=e}set jti(e){this.#u.jti=e}set nbf(e){"number"==typeof e?this.#u.nbf=tk("setNotBefore",e):e instanceof Date?this.#u.nbf=tk("setNotBefore",tP(e)):this.#u.nbf=tP(new Date)+tT(e)}set exp(e){"number"==typeof e?this.#u.exp=tk("setExpirationTime",e):e instanceof Date?this.#u.exp=tk("setExpirationTime",tP(e)):this.#u.exp=tP(new Date)+tT(e)}set iat(e){void 0===e?this.#u.iat=tP(new Date):e instanceof Date?this.#u.iat=tk("setIssuedAt",tP(e)):"string"==typeof e?this.#u.iat=tk("setIssuedAt",tP(new Date)+tT(e)):this.#u.iat=tk("setIssuedAt",e)}}class tj{#i;#o;#s;#t;#l;#d;#p;#f;constructor(e={}){this.#f=new tN(e)}setIssuer(e){return this.#f.iss=e,this}setSubject(e){return this.#f.sub=e,this}setAudience(e){return this.#f.aud=e,this}setJti(e){return this.#f.jti=e,this}setNotBefore(e){return this.#f.nbf=e,this}setExpirationTime(e){return this.#f.exp=e,this}setIssuedAt(e){return this.#f.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#l=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#p=!0,this}async encrypt(e,t){let r=new tR(this.#f.data());return this.#t&&(this.#l||this.#d||this.#p)&&(this.#t={...this.#t,iss:this.#l?this.#f.iss:void 0,sub:this.#d?this.#f.sub:void 0,aud:this.#p?this.#f.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#i&&r.setContentEncryptionKey(this.#i),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tI(e,t,r){let n;if(!eN(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return ey(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new eE('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return ta({...e,alg:t,ext:n});default:throw new eE('Unsupported "kty" (Key Type) Parameter value')}}let tD=async(e,t,r,n,a)=>{switch(e){case"dir":if(void 0!==r)throw new eA("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new eA("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!eN(n.epk))throw new eA('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eT(t),!e6(t))throw new eE("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tI(n.epk,e);if(eT(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new eA('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=ey(n.apu)}catch{throw new eA("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new eA('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=ey(n.apv)}catch{throw new eA("Failed to base64url decode the apv")}}let s=await e8(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?tc(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new eA("JWE Encrypted Key missing");return e1(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new eA("JWE Encrypted Key missing");return eT(t),tn(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new eA("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new eA('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=a?.maxPBES2Count||1e4;if(n.p2c>o)throw new eA('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new eA('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=ey(n.p2s)}catch{throw new eA("Failed to base64url decode the p2s")}return e7(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new eA("JWE Encrypted Key missing");return e1(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new eA("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new eA('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new eA('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=ey(n.iv)}catch{throw new eA("Failed to base64url decode the iv")}try{i=ey(n.tag)}catch{throw new eA("Failed to base64url decode the tag")}return tg(e,t,r,a,i)}default:throw new eE('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tU=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tM(e,t,r){let n,a,i,o,s,c,u;if(!eN(e))throw new eA("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new eA("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new eA("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new eA("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new eA("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eA("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new eA("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new eA("JWE AAD incorrect type");if(void 0!==e.header&&!eN(e.header))throw new eA("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eN(e.unprotected))throw new eA("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=ey(e.protected);n=JSON.parse(ed.decode(t))}catch{throw new eA("JWE Protected Header is invalid")}if(!tm(n,e.header,e.unprotected))throw new eA("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let l={...n,...e.header,...e.unprotected};if(tb(eA,new Map,r?.crit,n,l),void 0!==l.zip)throw new eE('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=l;if("string"!=typeof d||!d)throw new eA("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new eA("missing JWE Encryption Algorithm (enc) in JWE Header");let f=r&&tU("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tU("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(f&&!f.has(d)||!f&&d.startsWith("PBES2"))throw new ev('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(p))throw new ev('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{a=ey(e.encrypted_key)}catch{throw new eA("Failed to base64url decode the encrypted_key")}let g=!1;"function"==typeof t&&(t=await t(n,e),g=!0),tS("dir"===d?p:d,t,"decrypt");let y=await ts(t,d);try{i=await tD(d,y,a,l,r)}catch(e){if(e instanceof TypeError||e instanceof eA||e instanceof eE)throw e;i=tu(p)}if(void 0!==e.iv)try{o=ey(e.iv)}catch{throw new eA("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=ey(e.tag)}catch{throw new eA("Failed to base64url decode the tag")}let m=el.encode(e.protected??"");c=void 0!==e.aad?ep(m,el.encode("."),el.encode(e.aad)):m;try{u=ey(e.ciphertext)}catch{throw new eA("Failed to base64url decode the ciphertext")}let b={plaintext:await tf(p,i,u,o,s,c)};if(void 0!==e.protected&&(b.protectedHeader=n),void 0!==e.aad)try{b.additionalAuthenticatedData=ey(e.aad)}catch{throw new eA("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(b.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(b.unprotectedHeader=e.header),g)?{...b,key:y}:b}async function tL(e,t,r){if(e instanceof Uint8Array&&(e=ed.decode(e)),"string"!=typeof e)throw new eA("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new eA("Invalid Compact JWE");let u=await tM({ciphertext:o,iv:i||void 0,protected:n,tag:s||void 0,encrypted_key:a||void 0},t,r),l={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...l,key:u.key}:l}async function t$(e,t,r){let n=await tL(e,t,r),a=function(e,t,r={}){let n,a;try{n=JSON.parse(ed.decode(t))}catch{}if(!eN(n))throw new eR("JWT Claims Set must be a top-level JSON object");let{typ:i}=r;if(i&&("string"!=typeof e.typ||tx(e.typ)!==tx(i)))throw new ew('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:u,maxTokenAge:l}=r,d=[...o];for(let e of(void 0!==l&&d.push("iat"),void 0!==u&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ew(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ew('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ew('unexpected "sub" claim value',n,"sub","check_failed");if(u&&!tC(n.aud,"string"==typeof u?[u]:u))throw new ew('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=tT(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,f=tP(p||new Date);if((void 0!==n.iat||l)&&"number"!=typeof n.iat)throw new ew('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ew('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>f+a)throw new ew('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ew('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=f-a)throw new e_('"exp" claim timestamp check failed',n,"exp","check_failed")}if(l){let e=f-n.iat;if(e-a>("number"==typeof l?l:tT(l)))throw new e_('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-a)throw new ew('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new ew('replicated "iss" claim header parameter mismatch',a,"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new ew('replicated "sub" claim header parameter mismatch',a,"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new ew('replicated "aud" claim header parameter mismatch',a,"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}let tH=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tW=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tB=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tK=/^[\u0020-\u003A\u003D-\u007E]*$/,tq=Object.prototype.toString,tG=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tF(e,t){let r=new tG,n=e.length;if(n<2)return r;let a=t?.decode||tz,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let o=e.indexOf(";",i),s=-1===o?n:o;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let c=tV(e,i,t),u=tJ(e,t,c),l=e.slice(c,u);if(void 0===r[l]){let n=tV(e,t+1,s),i=tJ(e,s,n),o=a(e.slice(n,i));r[l]=o}i=s+1}while(i<n);return r}function tV(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tJ(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tX(e,t,r){let n=r?.encode||encodeURIComponent;if(!tH.test(e))throw TypeError(`argument name is invalid: ${e}`);let a=n(t);if(!tW.test(a))throw TypeError(`argument val is invalid: ${t}`);let i=e+"="+a;if(!r)return i;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);i+="; Max-Age="+r.maxAge}if(r.domain){if(!tB.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);i+="; Domain="+r.domain}if(r.path){if(!tK.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);i+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tq.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);i+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.partitioned&&(i+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return i}function tz(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tY}=l,tZ=()=>Date.now()/1e3|0,tQ="A256CBC-HS512";async function t0(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a}=e,i=Array.isArray(r)?r:[r],o=await t2(tQ,i[0],a),s=await eH({kty:"oct",k:em(o)},`sha${o.byteLength<<3}`);return await new tj(t).setProtectedHeader({alg:"dir",enc:tQ,kid:s}).setIssuedAt().setExpirationTime(tZ()+n).setJti(crypto.randomUUID()).encrypt(o)}async function t1(e){let{token:t,secret:r,salt:n}=e,a=Array.isArray(r)?r:[r];if(!t)return null;let{payload:i}=await t$(t,async({kid:e,enc:t})=>{for(let r of a){let a=await t2(t,r,n);if(void 0===e||e===await eH({kty:"oct",k:em(a)},`sha${a.byteLength<<3}`))return a}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tQ,"A256GCM"]});return i}async function t2(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await ec("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t3({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:a}=e,i=n.origin;return t?i=await a.redirect({url:t,baseUrl:n.origin}):r&&(i=await a.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}let t8="\x1b[31m",t6="\x1b[0m",t5={error(e){let t=e instanceof g?e.type:e.name;if(console.error(`${t8}[auth][error]${t6} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t8}[auth][cause]${t6}:`,t.stack),r&&console.error(`${t8}[auth][details]${t6}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t6}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t6} ${e}`,JSON.stringify(t,null,2))}};function t4(e){let t={...t5};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t9=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:t7,l:re}=l;async function rt(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function rr(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new L("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:a}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new L(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new L(`Cannot parse action at ${e}`);let[a,i]=n;if(!t9.includes(a)||i&&!["signin","callback","webauthn-options"].includes(a))throw new L(`Cannot parse action at ${e}`);return{action:a,providerId:"undefined"==i?void 0:i}}(r.pathname,t.basePath);return{url:r,action:n,providerId:a,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await rt(e):void 0,cookies:t7(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t4(t);r.error(n),r.debug("request",e)}}function rn(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:a}=e,i=re(r,n,a);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function ra(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function ri(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function ro({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[a,i]=t.split("|");if(i===await ra(`${a}${e.secret}`))return{csrfTokenVerified:r&&a===n,csrfToken:a}}let a=ri(32),i=await ra(`${a}${e.secret}`);return{cookie:`${a}|${i}`,csrfToken:a}}function rs(e,t){if(!t)throw new K(`CSRF token was missing during an action ${e}`)}function rc(e){return null!==e&&"object"==typeof e}function ru(e,...t){if(!t.length)return e;let r=t.shift();if(rc(e)&&rc(r))for(let t in r)rc(r[t])?(rc(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),ru(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return ru(e,...t)}let rl=Symbol("skip-csrf-check"),rd=Symbol("return-type-raw"),rp=Symbol("custom-fetch"),rf=Symbol("conform-internal"),rh=e=>ry({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rg=e=>ry({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function ry(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rm(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rb={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rw({authOptions:e,providerId:t,action:r,url:n,cookies:a,callbackUrl:i,csrfToken:o,csrfDisabled:s,isPost:c}){var u,l;let d=t4(e),{providers:p,provider:h}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),a=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:a,...i}=t,o=a?.id??i.id,s=ru(i,a,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=a?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rm(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rm(e.token,e.issuer),n=rm(e.userinfo,e.issuer),a=e.checks??["pkce"];return e.redirectProxyUrl&&(a.includes("state")||a.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:a,userinfo:n,profile:e.profile??rh,account:e.account??rg}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rp]??(e[rp]=a?.[rp]),e}return s}),i=a.find(({id:e})=>e===t);if(t&&!i){let e=a.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:a,provider:i}}({url:n,providerId:t,config:e}),g=!1;if((h?.type==="oauth"||h?.type==="oidc")&&h.redirectProxyUrl)try{g=new URL(h.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${h.redirectProxyUrl}`)}let y={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:h,cookies:ru(f(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:t0,decode:t1,...e.jwt},events:(u=e.events??{},l=d,Object.keys(u).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=u[t];return await r(...e)}catch(e){l.error(new v(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let a=e[n];return await a(...r)}catch(r){let e=new m(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...rb,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:g,experimental:{...e.experimental}},b=[];if(s)y.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await ro({options:y,cookieValue:a?.[y.cookies.csrfToken.name],isPost:c,bodyValue:o});y.csrfToken=e,y.csrfTokenVerified=r,t&&b.push({name:y.cookies.csrfToken.name,value:t,options:y.cookies.csrfToken.options})}let{callbackUrl:w,callbackUrlCookie:_}=await t3({options:y,cookieValue:a?.[y.cookies.callbackUrl.name],paramValue:i});return y.callbackUrl=w,_&&b.push({name:y.cookies.callbackUrl.name,value:_,options:y.cookies.callbackUrl.options}),{options:y,cookies:b}}var r_,rv,rE,rS,rA,rR,rP,rO,rT,rk,rx,rC,rN,rj,rI,rD,rU={},rM=[],rL=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,r$=Array.isArray;function rH(e,t){for(var r in t)e[r]=t[r];return e}function rW(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rB(e,t,r){var n,a,i,o={};for(i in t)"key"==i?n=t[i]:"ref"==i?a=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?rP.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===o[i]&&(o[i]=e.defaultProps[i]);return rK(e,o,n,a,null)}function rK(e,t,r,n,a){var i={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++rT:a,__i:-1,__u:0};return null==a&&null!=rO.vnode&&rO.vnode(i),i}function rq(e){return e.children}function rG(e,t){this.props=e,this.context=t}function rF(e,t){if(null==t)return e.__?rF(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rF(e):null}function rV(e){(!e.__d&&(e.__d=!0)&&rk.push(e)&&!rJ.__r++||rx!==rO.debounceRendering)&&((rx=rO.debounceRendering)||rC)(rJ)}function rJ(){var e,t,r,n,a,i,o,s;for(rk.sort(rN);e=rk.shift();)e.__d&&(t=rk.length,n=void 0,i=(a=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rH({},a)).__v=a.__v+1,rO.vnode&&rO.vnode(n),rQ(r.__P,n,a,r.__n,r.__P.namespaceURI,32&a.__u?[i]:null,o,null==i?rF(a):i,!!(32&a.__u),s),n.__v=a.__v,n.__.__k[n.__i]=n,r0(o,n,s),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rk.length>t&&rk.sort(rN));rJ.__r=0}function rX(e,t,r,n,a,i,o,s,c,u,l){var d,p,f,h,g,y=n&&n.__k||rM,m=t.length;for(r.__d=c,function(e,t,r){var n,a,i,o,s,c=t.length,u=r.length,l=u,d=0;for(e.__k=[],n=0;n<c;n++)null!=(a=t[n])&&"boolean"!=typeof a&&"function"!=typeof a?(o=n+d,(a=e.__k[n]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?rK(null,a,null,null,null):r$(a)?rK(rq,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?rK(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,i=null,-1!==(s=a.__i=function(e,t,r,n){var a=e.key,i=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&a==c.key&&i===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return s;s++}}return -1}(a,r,o,l))&&(l--,(i=r[s])&&(i.__u|=131072)),null==i||null===i.__v?(-1==s&&d--,"function"!=typeof a.type&&(a.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,a.__u|=65536))):a=e.__k[n]=null;if(l)for(n=0;n<u;n++)null!=(i=r[n])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=rF(i)),function e(t,r,n){var a,i;if(rO.unmount&&rO.unmount(t),(a=t.ref)&&(a.current&&a.current!==t.__e||r1(a,null,r)),null!=(a=t.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(e){rO.__e(e,r)}a.base=a.__P=null}if(a=t.__k)for(i=0;i<a.length;i++)a[i]&&e(a[i],r,n||"function"!=typeof t.type);n||rW(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(i,i))}(r,t,y),c=r.__d,d=0;d<m;d++)null!=(f=r.__k[d])&&(p=-1===f.__i?rU:y[f.__i]||rU,f.__i=d,rQ(e,f,p,a,i,o,s,c,u,l),h=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&r1(p.ref,null,f),l.push(f.ref,f.__c||h,f)),null==g&&null!=h&&(g=h),65536&f.__u||p.__k===f.__k?c=function e(t,r,n){var a,i;if("function"==typeof t.type){for(a=t.__k,i=0;a&&i<a.length;i++)a[i]&&(a[i].__=t,r=e(a[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rF(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(f,c,e):"function"==typeof f.type&&void 0!==f.__d?c=f.__d:h&&(c=h.nextSibling),f.__d=void 0,f.__u&=-196609);r.__d=c,r.__e=g}function rz(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rL.test(t)?r:r+"px"}function rY(e,t,r,n,a){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rz(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rz(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=rj,e.addEventListener(t,i?rD:rI,i)):e.removeEventListener(t,i?rD:rI,i);else{if("http://www.w3.org/2000/svg"==a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rZ(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rj++;else if(t.t<r.u)return;return r(rO.event?rO.event(t):t)}}}function rQ(e,t,r,n,a,i,o,s,c,u){var l,d,p,f,h,g,y,m,b,w,_,v,E,S,A,R,P=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),i=[s=t.__e=r.__e]),(l=rO.__b)&&l(t);e:if("function"==typeof P)try{if(m=t.props,b="prototype"in P&&P.prototype.render,w=(l=P.contextType)&&n[l.__c],_=l?w?w.props.value:l.__:n,r.__c?y=(d=t.__c=r.__c).__=d.__E:(b?t.__c=d=new P(m,_):(t.__c=d=new rG(m,_),d.constructor=P,d.render=r2),w&&w.sub(d),d.props=m,d.state||(d.state={}),d.context=_,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),b&&null==d.__s&&(d.__s=d.state),b&&null!=P.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rH({},d.__s)),rH(d.__s,P.getDerivedStateFromProps(m,d.__s))),f=d.props,h=d.state,d.__v=t,p)b&&null==P.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),b&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(b&&null==P.getDerivedStateFromProps&&m!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(m,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(m,d.__s,_)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=m,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(m,d.__s,_),b&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(f,h,g)})}if(d.context=_,d.props=m,d.__P=e,d.__e=!1,E=rO.__r,S=0,b){for(d.state=d.__s,d.__d=!1,E&&E(t),l=d.render(d.props,d.state,d.context),A=0;A<d._sb.length;A++)d.__h.push(d._sb[A]);d._sb=[]}else do d.__d=!1,E&&E(t),l=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(n=rH(rH({},n),d.getChildContext())),b&&!p&&null!=d.getSnapshotBeforeUpdate&&(g=d.getSnapshotBeforeUpdate(f,h)),rX(e,r$(R=null!=l&&l.type===rq&&null==l.key?l.props.children:l)?R:[R],t,r,n,a,i,o,s,c,u),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),y&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=i){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;i[i.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rO.__e(e,t,r)}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,a,i,o,s,c){var u,l,d,p,f,h,g,y=r.props,m=t.props,b=t.type;if("svg"===b?a="http://www.w3.org/2000/svg":"math"===b?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=i){for(u=0;u<i.length;u++)if((f=i[u])&&"setAttribute"in f==!!b&&(b?f.localName===b:3===f.nodeType)){e=f,i[u]=null;break}}if(null==e){if(null===b)return document.createTextNode(m);e=document.createElementNS(a,b,m.is&&m),s&&(rO.__m&&rO.__m(t,i),s=!1),i=null}if(null===b)y===m||s&&e.data===m||(e.data=m);else{if(i=i&&rP.call(e.childNodes),y=r.props||rU,!s&&null!=i)for(y={},u=0;u<e.attributes.length;u++)y[(f=e.attributes[u]).name]=f.value;for(u in y)if(f=y[u],"children"==u);else if("dangerouslySetInnerHTML"==u)d=f;else if(!(u in m)){if("value"==u&&"defaultValue"in m||"checked"==u&&"defaultChecked"in m)continue;rY(e,u,null,f,a)}for(u in m)f=m[u],"children"==u?p=f:"dangerouslySetInnerHTML"==u?l=f:"value"==u?h=f:"checked"==u?g=f:s&&"function"!=typeof f||y[u]===f||rY(e,u,f,y[u],a);if(l)s||d&&(l.__html===d.__html||l.__html===e.innerHTML)||(e.innerHTML=l.__html),t.__k=[];else if(d&&(e.innerHTML=""),rX(e,r$(p)?p:[p],t,r,n,"foreignObject"===b?"http://www.w3.org/1999/xhtml":a,i,o,i?i[0]:r.__k&&rF(r,0),s,c),null!=i)for(u=i.length;u--;)rW(i[u]);s||(u="value","progress"===b&&null==h?e.removeAttribute("value"):void 0===h||h===e[u]&&("progress"!==b||h)&&("option"!==b||h===y[u])||rY(e,u,h,y[u],a),u="checked",void 0!==g&&g!==e[u]&&rY(e,u,g,y[u],a))}return e}(r.__e,t,r,n,a,i,o,c,u);(l=rO.diffed)&&l(t)}function r0(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)r1(r[n],r[++n],r[++n]);rO.__c&&rO.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rO.__e(e,t.__v)}})}function r1(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rO.__e(e,r)}}function r2(e,t,r){return this.constructor(e,r)}function r3(e,t){var r,n,a,i,o;r=e,rO.__&&rO.__(r,t),a=(n="function"==typeof r3)?null:r3&&r3.__k||t.__k,i=[],o=[],rQ(t,r=(!n&&r3||t).__k=rB(rq,null,[r]),a||rU,rU,t.namespaceURI,!n&&r3?[r3]:a?null:t.firstChild?rP.call(t.childNodes):null,i,!n&&r3?r3:a?a.__e:t.firstChild,n,o),r0(i,r,o)}rP=rM.slice,rO={__e:function(e,t,r,n){for(var a,i,o;t=t.__;)if((a=t.__c)&&!a.__)try{if((i=a.constructor)&&null!=i.getDerivedStateFromError&&(a.setState(i.getDerivedStateFromError(e)),o=a.__d),null!=a.componentDidCatch&&(a.componentDidCatch(e,n||{}),o=a.__d),o)return a.__E=a}catch(t){e=t}throw e}},rT=0,rG.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rH({},this.state),"function"==typeof e&&(e=e(rH({},r),this.props)),e&&rH(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rV(this))},rG.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rV(this))},rG.prototype.render=rq,rk=[],rC="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rN=function(e,t){return e.__v.__b-t.__v.__b},rJ.__r=0,rj=0,rI=rZ(!1),rD=rZ(!0);var r8=/[\s\n\\/='"\0<>]/,r6=/^(xlink|xmlns|xml)([A-Z])/,r5=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r4=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,r9=new Set(["draggable","spellcheck"]),r7=/["&<]/;function ne(e){if(0===e.length||!1===r7.test(e))return e;for(var t=0,r=0,n="",a="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:a="&quot;";break;case 38:a="&amp;";break;case 60:a="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=a,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var nt={},nr=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),nn=/[A-Z]/g;function na(){this.__d=!0}var ni=null,no,ns,nc,nu,nl={},nd=[],np=Array.isArray,nf=Object.assign;function nh(e,t){var r,n=e.type,a=!0;return e.__c?(a=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=nl),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=nf({},r.state,n.getDerivedStateFromProps(r.props,r.state)):a&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!a&&r.componentWillUpdate&&r.componentWillUpdate(),nc&&nc(e),r.render(r.props,r.state,t)}var ng=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),ny=/["&<]/,nm=0;function nb(e,t,r,n,a,i){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var u={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--nm,__i:-1,__u:0,__source:a,__self:i};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rO.vnode&&rO.vnode(u),u}async function nw(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),i().forEach(e=>{n.searchParams.append(e.name,e.value)});let a=await fetch(n);return a.ok?a.json():void console.error("Failed to fetch options",a)}function a(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(a().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=a();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function u(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=a();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),u()}let n_={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},nv=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nE({html:e,title:t,status:r,cookies:n,theme:a,headTags:i}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${nv}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${a?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rO.__s;rO.__s=!0,no=rO.__b,ns=rO.diffed,nc=rO.__r,nu=rO.unmount;var a=rB(rq,null);a.__k=[e];try{var i=function e(t,r,n,a,i,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?ne(t):t+"";if(np(t)){var u,l="";i.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var f,h=e(p,r,n,a,i,o,s);"string"==typeof h?l+=h:(u||(u=[]),l&&u.push(l),l="",np(h)?(f=u).push.apply(f,h):u.push(h))}}return u?(l&&u.push(l),u):l}if(void 0!==t.constructor)return"";t.__=i,no&&no(t);var g=t.type,y=t.props;if("function"==typeof g){var m,b,w,_=r;if(g===rq){if("tpl"in y){for(var v="",E=0;E<y.tpl.length;E++)if(v+=y.tpl[E],y.exprs&&E<y.exprs.length){var S=y.exprs[E];if(null==S)continue;"object"==typeof S&&(void 0===S.constructor||np(S))?v+=e(S,r,n,a,t,o,s):v+=S}return v}if("UNSTABLE_comment"in y)return"\x3c!--"+ne(y.UNSTABLE_comment)+"--\x3e";b=y.children}else{if(null!=(m=g.contextType)){var A=r[m.__c];_=A?A.props.value:m.__}var R=g.prototype&&"function"==typeof g.prototype.render;if(R)b=nh(t,_),w=t.__c;else{t.__c=w={__v:t,context:_,props:t.props,setState:na,forceUpdate:na,__d:!0,__h:[]};for(var P=0;w.__d&&P++<25;)w.__d=!1,nc&&nc(t),b=g.call(w,y,_);w.__d=!0}if(null!=w.getChildContext&&(r=nf({},r,w.getChildContext())),R&&rO.errorBoundaries&&(g.getDerivedStateFromError||w.componentDidCatch)){b=null!=b&&b.type===rq&&null==b.key&&null==b.props.tpl?b.props.children:b;try{return e(b,r,n,a,t,o,s)}catch(i){return g.getDerivedStateFromError&&(w.__s=g.getDerivedStateFromError(i)),w.componentDidCatch&&w.componentDidCatch(i,nl),w.__d?(b=nh(t,r),null!=(w=t.__c).getChildContext&&(r=nf({},r,w.getChildContext())),e(b=null!=b&&b.type===rq&&null==b.key&&null==b.props.tpl?b.props.children:b,r,n,a,t,o,s)):""}finally{ns&&ns(t),t.__=null,nu&&nu(t)}}}b=null!=b&&b.type===rq&&null==b.key&&null==b.props.tpl?b.props.children:b;try{var O=e(b,r,n,a,t,o,s);return ns&&ns(t),t.__=null,rO.unmount&&rO.unmount(t),O}catch(i){if(!o&&s&&s.onError){var T=s.onError(i,t,function(i){return e(i,r,n,a,t,o,s)});if(void 0!==T)return T;var k=rO.__e;return k&&k(i,t),""}if(!o||!i||"function"!=typeof i.then)throw i;return i.then(function i(){try{return e(b,r,n,a,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(b,r,n,a,t,o,s)},i)}})}}var x,C="<"+g,N="";for(var j in y){var I=y[j];if("function"!=typeof I||"class"===j||"className"===j){switch(j){case"children":x=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in y)continue;j="for";break;case"className":if("class"in y)continue;j="class";break;case"defaultChecked":j="checked";break;case"defaultSelected":j="selected";break;case"defaultValue":case"value":switch(j="value",g){case"textarea":x=I;continue;case"select":a=I;continue;case"option":a!=I||"selected"in y||(C+=" selected")}break;case"dangerouslySetInnerHTML":N=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var a="-"==r[0]?r:nt[r]||(nt[r]=r.replace(nn,"-$&").toLowerCase()),i=";";"number"!=typeof n||a.startsWith("--")||nr.has(a)||(i="px;"),t=t+a+":"+n+i}}return t||void 0}(I));break;case"acceptCharset":j="accept-charset";break;case"httpEquiv":j="http-equiv";break;default:if(r6.test(j))j=j.replace(r6,"$1:$2").toLowerCase();else{if(r8.test(j))continue;("-"===j[4]||r9.has(j))&&null!=I?I+="":n?r4.test(j)&&(j="panose1"===j?"panose-1":j.replace(/([A-Z])/g,"-$1").toLowerCase()):r5.test(j)&&(j=j.toLowerCase())}}null!=I&&!1!==I&&(C=!0===I||""===I?C+" "+j:C+" "+j+'="'+("string"==typeof I?ne(I):I+"")+'"')}}if(r8.test(g))throw Error(g+" is not a valid HTML tag name in "+C+">");if(N||("string"==typeof x?N=ne(x):null!=x&&!1!==x&&!0!==x&&(N=e(x,r,"svg"===g||"foreignObject"!==g&&n,a,t,o,s))),ns&&ns(t),t.__=null,nu&&nu(t),!N&&ng.has(g))return C+"/>";var D="</"+g+">",U=C+">";return np(N)?[U].concat(N,[D]):"string"!=typeof N?[U,N,D]:U+N+D}(e,nl,!1,void 0,a,!1,void 0);return np(i)?i.join(""):i}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rO.__c&&rO.__c(e,nd),rO.__s=n,nd.length=0}}(e)}</div></body></html>`}}function nS(e){let{url:t,theme:r,query:n,cookies:a,pages:i,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:a,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:a,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new L("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:a}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),u="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;u=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nE({cookies:a,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:a,email:i,error:o}=e;"undefined"!=typeof document&&a?.brandColor&&document.documentElement.style.setProperty("--brand-color",a.brandColor),"undefined"!=typeof document&&a?.buttonText&&document.documentElement.style.setProperty("--button-text-color",a.buttonText);let s=o&&(n_[o]??n_.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return nb("div",{className:"signin",children:[a?.brandColor&&nb("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${a.brandColor}}`}}),a?.buttonText&&nb("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),nb("div",{className:"card",children:[s&&nb("div",{className:"error",children:nb("p",{children:s})}),a?.logo&&nb("img",{src:a.logo,alt:"Logo",className:"logo"}),r.map((e,a)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let u=s??o??"#fff";return nb("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?nb("form",{action:e.signinUrl,method:"POST",children:[nb("input",{type:"hidden",name:"csrfToken",value:t}),n&&nb("input",{type:"hidden",name:"callbackUrl",value:n}),nb("button",{type:"submit",className:"button",style:{"--provider-brand-color":u},tabIndex:0,children:[nb("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&nb("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a>0&&"email"!==r[a-1].type&&"credentials"!==r[a-1].type&&"webauthn"!==r[a-1].type&&nb("hr",{}),"email"===e.type&&nb("form",{action:e.signinUrl,method:"POST",children:[nb("input",{type:"hidden",name:"csrfToken",value:t}),nb("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),nb("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),nb("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&nb("form",{action:e.callbackUrl,method:"POST",children:[nb("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>nb("div",{children:[nb("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),nb("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),nb("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&nb("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[nb("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>nb("div",{children:[nb("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),nb("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),nb("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a+1<r.length&&nb("hr",{})]},e.id)})]}),c&&nb(rq,{children:nb("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nw})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:u})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:a}:nE({cookies:a,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return nb("div",{className:"signout",children:[n?.brandColor&&nb("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&nb("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),nb("div",{className:"card",children:[n?.logo&&nb("img",{src:n.logo,alt:"Logo",className:"logo"}),nb("h1",{children:"Signout"}),nb("p",{children:"Are you sure you want to sign out?"}),nb("form",{action:t?.toString(),method:"POST",children:[nb("input",{type:"hidden",name:"csrfToken",value:r}),nb("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:`${i.verifyRequest}${t?.search??""}`,cookies:a}:nE({cookies:a,theme:r,html:function(e){let{url:t,theme:r}=e;return nb("div",{className:"verify-request",children:[r.brandColor&&nb("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),nb("div",{className:"card",children:[r.logo&&nb("img",{src:r.logo,alt:"Logo",className:"logo"}),nb("h1",{children:"Check your email"}),nb("p",{children:"A sign in link has been sent to your email address."}),nb("p",{children:nb("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:a}:nE({cookies:a,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,a=`${t}/signin`,i={default:{status:200,heading:"Error",message:nb("p",{children:nb("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:nb("div",{children:[nb("p",{children:"There is a problem with the server configuration."}),nb("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:nb("div",{children:[nb("p",{children:"You do not have permission to sign in."}),nb("p",{children:nb("a",{className:"button",href:a,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:nb("div",{children:[nb("p",{children:"The sign in link is no longer valid."}),nb("p",{children:"It may have been used already or it may have expired."})]}),signin:nb("a",{className:"button",href:a,children:"Sign in"})}},{status:o,heading:s,message:c,signin:u}=i[r]??i.default;return{status:o,html:nb("div",{className:"error",children:[n?.brandColor&&nb("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),nb("div",{className:"card",children:[n?.logo&&nb("img",{src:n?.logo,alt:"Logo",className:"logo"}),nb("h1",{children:s}),nb("div",{className:"message",children:c}),u]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nA(e,t=Date.now()){return new Date(t+1e3*e)}async function nR(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:a,jwt:i,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!a)return{user:t,account:r};let u=r,{createUser:l,updateUser:d,getUser:p,getUserByAccount:f,getUserByEmail:h,linkAccount:g,createSession:y,getSessionAndUser:m,deleteSession:b}=a,w=null,_=null,v=!1,E="jwt"===s;if(e)if(E)try{let t=n.cookies.sessionToken.name;(w=await i.decode({...i,token:e,salt:t}))&&"sub"in w&&w.sub&&(_=await p(w.sub))}catch{}else{let t=await m(e);t&&(w=t.session,_=t.user)}if("email"===u.type){let r=await h(t.email);return r?(_?.id!==r.id&&!E&&e&&await b(e),_=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:_})):(_=await l({...t,emailVerified:new Date}),await o.createUser?.({user:_}),v=!0),{session:w=E?{}:await y({sessionToken:c(),userId:_.id,expires:nA(n.session.maxAge)}),user:_,isNewUser:v}}if("webauthn"===u.type){let e=await f({providerAccountId:u.providerAccountId,provider:u.provider});if(e){if(_){if(e.id===_.id){let e={...u,userId:_.id};return{session:w,user:_,isNewUser:v,account:e}}throw new J("The account is already associated with another user",{provider:u.provider})}w=E?{}:await y({sessionToken:c(),userId:e.id,expires:nA(n.session.maxAge)});let t={...u,userId:e.id};return{session:w,user:e,isNewUser:v,account:t}}{if(_){await g({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t});let e={...u,userId:_.id};return{session:w,user:_,isNewUser:v,account:e}}if(t.email?await h(t.email):null)throw new J("Another account already exists with the same e-mail address",{provider:u.provider});_=await l({...t}),await o.createUser?.({user:_}),await g({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),w=E?{}:await y({sessionToken:c(),userId:_.id,expires:nA(n.session.maxAge)});let e={...u,userId:_.id};return{session:w,user:_,isNewUser:!0,account:e}}}let S=await f({providerAccountId:u.providerAccountId,provider:u.provider});if(S){if(_){if(S.id===_.id)return{session:w,user:_,isNewUser:v};throw new C("The account is already associated with another user",{provider:u.provider})}return{session:w=E?{}:await y({sessionToken:c(),userId:S.id,expires:nA(n.session.maxAge)}),user:S,isNewUser:v}}{let{provider:e}=n,{type:r,provider:a,providerAccountId:i,userId:s,...d}=u;if(u=Object.assign(e.account(d)??{},{providerAccountId:i,provider:a,type:r,userId:s}),_)return await g({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),{session:w,user:_,isNewUser:v};let p=t.email?await h(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)_=p,v=!1;else throw new C("Another account already exists with the same e-mail address",{provider:u.provider})}else _=await l({...t,emailVerified:null}),v=!0;return await o.createUser?.({user:_}),await g({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),{session:w=E?{}:await y({sessionToken:c(),userId:_.id,expires:nA(n.session.maxAge)}),user:_,isNewUser:v}}}function nP(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(i="oauth4webapi/v3.6.0");let nO="ERR_INVALID_ARG_VALUE",nT="ERR_INVALID_ARG_TYPE";function nk(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nx=Symbol(),nC=Symbol(),nN=Symbol(),nj=Symbol(),nI=Symbol(),nD=Symbol(),nU=Symbol(),nM=new TextEncoder,nL=new TextDecoder;function n$(e){return"string"==typeof e?nM.encode(e):nL.decode(e)}function nH(e){return"string"==typeof e?s(e):o(e)}o=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},s=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw nk("The input to be decoded is not correctly encoded.",nO,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nk("The input to be decoded is not correctly encoded.",nO,e)}};class nW extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aY,Error.captureStackTrace?.(this,this.constructor)}}class nB extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nK(e,t,r){return new nB(e,{code:t,cause:r})}function nq(e,t){if(!(e instanceof CryptoKey))throw nk(`${t} must be a CryptoKey`,nT)}function nG(e,t){if(nq(e,t),"private"!==e.type)throw nk(`${t} must be a private CryptoKey`,nO)}function nF(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nV(e){nP(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e??{});if(i&&!t.has("user-agent")&&t.set("user-agent",i),t.has("authorization"))throw nk('"options.headers" must not include the "authorization" header name',nO);return t}function nJ(e,t){if(void 0!==t){if("function"==typeof t&&(t=t(e.href)),!(t instanceof AbortSignal))throw nk('"options.signal" must return or be an instance of AbortSignal',nT);return t}}function nX(e){return e.includes("//")?e.replace("//","/"):e}async function nz(e,t,r,n){if(!(e instanceof URL))throw nk(`"${t}" must be an instance of URL`,nT);ao(e,n?.[nx]!==!0);let a=r(new URL(e.href)),i=nV(n?.headers);return i.set("accept","application/json"),(n?.[nj]||fetch)(a.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:nJ(a,n?.signal)})}async function nY(e,t){return nz(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nX(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(e,t,r=!1){"/"===e.pathname?e.pathname=t:e.pathname=nX(`${t}/${r?e.pathname:e.pathname.replace(/(\/)$/,"")}`)}(e,".well-known/oauth-authorization-server");break;default:throw nk('"options.algorithm" must be "oidc" (default), or "oauth2"',nO)}return e},t)}function nZ(e,t,r,n,a){try{if("number"!=typeof e||!Number.isFinite(e))throw nk(`${r} must be a number`,nT,a);if(e>0)return;if(t){if(0!==e)throw nk(`${r} must be a non-negative number`,nO,a);return}throw nk(`${r} must be a positive number`,nO,a)}catch(e){if(n)throw nK(e.message,n,a);throw e}}function nQ(e,t,r,n){try{if("string"!=typeof e)throw nk(`${t} must be a string`,nT,n);if(0===e.length)throw nk(`${t} must not be empty`,nO,n)}catch(e){if(r)throw nK(e.message,r,n);throw e}}async function n0(e,t){if(!(e instanceof URL)&&e!==iP)throw nk('"expectedIssuerIdentifier" must be an instance of URL',nT);if(!nP(t,Response))throw nk('"response" must be an instance of Response',nT);if(200!==t.status)throw nK('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',a8,t);ii(t);let r=await iR(t);if(nQ(r.issuer,'"response" body "issuer" property',a1,{body:r}),e!==iP&&new URL(r.issuer).href!==e.href)throw nK('"response" body "issuer" property does not match the expected value',a7,{expected:e.href,body:r,attribute:"issuer"});return r}function n1(e){var t=e,r="application/json";if(aP(t)!==r)throw n2(t,r)}function n2(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nK(r,a3,e)}function n3(){return nH(crypto.getRandomValues(new Uint8Array(32)))}async function n8(e){return nQ(e,"codeVerifier"),nH(await crypto.subtle.digest("SHA-256",n$(e)))}function n6(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nW("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nW("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nW("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nW("unsupported CryptoKey algorithm name",{cause:e})}}function n5(e){let t=e?.[nC];return"number"==typeof t&&Number.isFinite(t)?t:0}function n4(e){let t=e?.[nN];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n9(){return Math.floor(Date.now()/1e3)}function n7(e){if("object"!=typeof e||null===e)throw nk('"as" must be an object',nT);nQ(e.issuer,'"as.issuer"')}function ae(e){if("object"!=typeof e||null===e)throw nk('"client" must be an object',nT);nQ(e.client_id,'"client.client_id"')}function at(e,t){let r=n9()+n5(t);return{jti:n3(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function ar(e,t,r){if(!r.usages.includes("sign"))throw nk('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nO);let n=`${nH(n$(JSON.stringify(e)))}.${nH(n$(JSON.stringify(t)))}`,a=nH(await crypto.subtle.sign(il(r),r,n$(n)));return`${n}.${a}`}async function an(e){let{kty:t,e:r,n,x:a,y:i,crv:o}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:a,y:i,crv:o};return c.set(e,s),s}async function aa(e){return(c||=new WeakMap).get(e)||an(e)}let ai=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function ao(e,t){if(t&&"https:"!==e.protocol)throw nK("only requests to HTTPS are allowed",a6,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nK("only HTTP and HTTPS requests are allowed",a5,e)}function as(e,t,r,n){let a;if("string"!=typeof e||!(a=ai(e)))throw nK(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?it:ir,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return ao(a,n),a}function ac(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?as(e.mtls_endpoint_aliases[t],t,r,n):as(e[t],t,r,n)}class au extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=az,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class al extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aZ,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class ad extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aX,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let ap="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",af=RegExp("^[,\\s]*("+ap+")\\s(.*)"),ah=RegExp("^[,\\s]*("+ap+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ag=RegExp("^[,\\s]*"+("("+ap+")\\s*=\\s*(")+ap+")[,\\s]*(.*)"),ay=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function am(e){if(e.status>399&&e.status<500){ii(e),n1(e);try{let t=await e.clone().json();if(nF(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function ab(e,t,r){if(e.status!==t){let t;if(t=await am(e))throw await e.body?.cancel(),new au("server responded with an error in the response body",{cause:t,response:e});throw nK(`"response" is not a conform ${r} response (unexpected HTTP status code)`,a8,e)}}function aw(e){if(!a$.has(e))throw nk('"options.DPoP" is not a valid DPoPHandle',nO)}async function a_(e,t,r,n,a,i){if(nQ(e,'"accessToken"'),!(r instanceof URL))throw nk('"url" must be an instance of URL',nT);ao(r,i?.[nx]!==!0),n=nV(n),i?.DPoP&&(aw(i.DPoP),await i.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (i?.[nj]||fetch)(r.href,{body:a,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:nJ(r,i?.signal)});return i?.DPoP?.cacheNonce(o),o}async function av(e,t,r,n){n7(e),ae(t);let a=ac(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nx]!==!0),i=nV(n?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),a_(r,"GET",a,i,null,{...n,[nC]:n5(t)})}function aE(e,t,r,n){(u||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n9()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function aS(e,t){u?.delete(e),delete t?.jwks,delete t?.uat}async function aA(e,t,r){var n;let a,i,o,{alg:s,kid:c}=r;if(function(e){if(!ic(e.alg))throw new nW('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!u?.has(e)&&!("object"!=typeof(n=t?.[nU])||null===n||!("uat"in n)||"number"!=typeof n.uat||n9()-n.uat>=300)&&"jwks"in n&&nF(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nF)&&aE(e,t?.[nU].jwks,t?.[nU].uat),u?.has(e)){if({jwks:a,age:i}=u.get(e),i>=300)return aS(e,t?.[nU]),aA(e,t,r)}else a=await io(e,t).then(is),i=0,aE(e,a,n9(),t?.[nU]);switch(s.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new nW("unsupported JWS algorithm",{cause:{alg:s}})}let l=a.keys.filter(e=>{if(e.kty!==o||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=l;if(!p){if(i>=60)return aS(e,t?.[nU]),aA(e,t,r);throw nK("error when selecting a JWT verification key, no applicable keys found",ie,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw nK('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',ie,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)});return iS(s,d)}let aR=Symbol();function aP(e){return e.headers.get("content-type")?.split(";")[0]}async function aO(e,t,r,n,a){let i;if(n7(e),ae(t),!nP(n,Response))throw nk('"response" must be an instance of Response',nT);if(aI(n),200!==n.status)throw nK('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',a8,n);if(ii(n),"application/jwt"===aP(n)){let{claims:r,jwt:o}=await ip(await n.text(),ib.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n5(t),n4(t),a?.[nD]).then(aD.bind(void 0,t.client_id)).then(aM.bind(void 0,e));aC.set(n,o),i=r}else{if(t.userinfo_signed_response_alg)throw nK("JWT UserInfo Response expected",aQ,n);i=await iR(n)}if(nQ(i.sub,'"response" body "sub" property',a1,{body:i}),r===aR);else if(nQ(r,'"expectedSubject"'),i.sub!==r)throw nK('unexpected "response" body "sub" property value',a7,{expected:r,body:i,attribute:"sub"});return i}async function aT(e,t,r,n,a,i,o){return await r(e,t,a,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[nj]||fetch)(n.href,{body:a,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:nJ(n,o?.signal)})}async function ak(e,t,r,n,a,i){let o=ac(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[nx]!==!0);a.set("grant_type",n);let s=nV(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(aw(i.DPoP),await i.DPoP.addProof(o,s,"POST"));let c=await aT(e,t,r,o,a,s,i);return i?.DPoP?.cacheNonce(c),c}let ax=new WeakMap,aC=new WeakMap;function aN(e){if(!e.id_token)return;let t=ax.get(e);if(!t)throw nk('"ref" was already garbage collected or did not resolve from the proper sources',nO);return t}async function aj(e,t,r,n,a){if(n7(e),ae(t),!nP(r,Response))throw nk('"response" must be an instance of Response',nT);aI(r),await ab(r,200,"Token Endpoint"),ii(r);let i=await iR(r);if(nQ(i.access_token,'"response" body "access_token" property',a1,{body:i}),nQ(i.token_type,'"response" body "token_type" property',a1,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new nW("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;nZ(e,!0,'"response" body "expires_in" property',a1,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&nQ(i.refresh_token,'"response" body "refresh_token" property',a1,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw nK('"response" body "scope" property must be a string',a1,{body:i});if(void 0!==i.id_token){nQ(i.id_token,'"response" body "id_token" property',a1,{body:i});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nZ(t.default_max_age,!0,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await ip(i.id_token,ib.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n5(t),n4(t),a?.[nD]).then(aK.bind(void 0,o)).then(aL.bind(void 0,e)).then(aU.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nK('ID Token "aud" (audience) claim includes additional untrusted audiences',a9,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nK('unexpected ID Token "azp" (authorized party) claim value',a9,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nZ(s.auth_time,!1,'ID Token "auth_time" (authentication time)',a1,{claims:s}),aC.set(r,c),ax.set(i,s)}return i}function aI(e){let t;if(t=function(e){if(!nP(e,Response))throw nk('"response" must be an instance of Response',nT);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(af),a=t?.["1"].toLowerCase();if(n=t?.["2"],!a)return;let i={};for(;n;){let r,a;if(t=n.match(ah)){if([,r,a,n]=t,a.includes("\\"))try{a=JSON.parse(`"${a}"`)}catch{}i[r.toLowerCase()]=a;continue}if(t=n.match(ag)){[,r,a,n]=t,i[r.toLowerCase()]=a;continue}if(t=n.match(ay)){if(Object.keys(i).length)break;[,e,n]=t;break}return}let o={scheme:a,parameters:i};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new ad("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function aD(e,t){return void 0!==t.claims.aud?aU(e,t):t}function aU(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nK('unexpected JWT "aud" (audience) claim value',a9,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nK('unexpected JWT "aud" (audience) claim value',a9,{expected:e,claims:t.claims,claim:"aud"});return t}function aM(e,t){return void 0!==t.claims.iss?aL(e,t):t}function aL(e,t){let r=e[iO]?.(t)??e.issuer;if(t.claims.iss!==r)throw nK('unexpected JWT "iss" (issuer) claim value',a9,{expected:r,claims:t.claims,claim:"iss"});return t}let a$=new WeakSet,aH=Symbol();async function aW(e,t,r,n,a,i,o){if(n7(e),ae(t),!a$.has(n))throw nk('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nO);nQ(a,'"redirectUri"');let s=iw(n,"code");if(!s)throw nK('no authorization code in "callbackParameters"',a1);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",a),c.set("code",s),i!==aH&&(nQ(i,'"codeVerifier"'),c.set("code_verifier",i)),ak(e,t,r,"authorization_code",c,o)}let aB={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function aK(e,t){for(let r of e)if(void 0===t.claims[r])throw nK(`JWT "${r}" (${aB[r]}) claim missing`,a1,{claims:t.claims});return t}let aq=Symbol(),aG=Symbol();async function aF(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?aV(e,t,r,n.expectedNonce,n.maxAge,{[nD]:n[nD]}):aJ(e,t,r,n)}async function aV(e,t,r,n,a,i){let o=[];switch(n){case void 0:n=aq;break;case aq:break;default:nQ(n,'"expectedNonce" argument'),o.push("nonce")}switch(a??=t.default_max_age){case void 0:a=aG;break;case aG:break;default:nZ(a,!0,'"maxAge" argument'),o.push("auth_time")}let s=await aj(e,t,r,o,i);nQ(s.id_token,'"response" body "id_token" property',a1,{body:s});let c=aN(s);if(a!==aG){let e=n9()+n5(t),r=n4(t);if(c.auth_time+a<e-r)throw nK("too much time has elapsed since the last End-User authentication",a4,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===aq){if(void 0!==c.nonce)throw nK('unexpected ID Token "nonce" claim value',a9,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nK('unexpected ID Token "nonce" claim value',a9,{expected:n,claims:c,claim:"nonce"});return s}async function aJ(e,t,r,n){let a=await aj(e,t,r,void 0,n),i=aN(a);if(i){if(void 0!==t.default_max_age){nZ(t.default_max_age,!0,'"client.default_max_age"');let e=n9()+n5(t),r=n4(t);if(i.auth_time+t.default_max_age<e-r)throw nK("too much time has elapsed since the last End-User authentication",a4,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw nK('unexpected ID Token "nonce" claim value',a9,{expected:void 0,claims:i,claim:"nonce"})}return a}let aX="OAUTH_WWW_AUTHENTICATE_CHALLENGE",az="OAUTH_RESPONSE_BODY_ERROR",aY="OAUTH_UNSUPPORTED_OPERATION",aZ="OAUTH_AUTHORIZATION_RESPONSE_ERROR",aQ="OAUTH_JWT_USERINFO_EXPECTED",a0="OAUTH_PARSE_ERROR",a1="OAUTH_INVALID_RESPONSE",a2="OAUTH_INVALID_REQUEST",a3="OAUTH_RESPONSE_IS_NOT_JSON",a8="OAUTH_RESPONSE_IS_NOT_CONFORM",a6="OAUTH_HTTP_REQUEST_FORBIDDEN",a5="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",a4="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",a9="OAUTH_JWT_CLAIM_COMPARISON_FAILED",a7="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",ie="OAUTH_KEY_SELECTION_FAILED",it="OAUTH_MISSING_SERVER_METADATA",ir="OAUTH_INVALID_SERVER_METADATA";function ia(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nK('unexpected JWT "typ" header parameter value',a1,{header:t.header});return t}function ii(e){if(e.bodyUsed)throw nk('"response" body has been used already',nO)}async function io(e,t){n7(e);let r=ac(e,"jwks_uri",!1,t?.[nx]!==!0),n=nV(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nj]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:nJ(r,t?.signal)})}async function is(e){if(!nP(e,Response))throw nk('"response" must be an instance of Response',nT);if(200!==e.status)throw nK('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',a8,e);ii(e);let t=await iR(e,e=>(function(e,...t){if(!t.includes(aP(e)))throw n2(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nK('"response" body "keys" property must be an array',a1,{body:t});if(!Array.prototype.every.call(t.keys,nF))throw nK('"response" body "keys" property members must be JWK formatted objects',a1,{body:t});return t}function ic(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function iu(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nW(`unsupported ${t.name} modulusLength`,{cause:e})}function il(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nW("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(iu(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nW("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return iu(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nW("unsupported CryptoKey algorithm name",{cause:e})}async function id(e,t,r,n){let a=n$(`${e}.${t}`),i=il(r);if(!await crypto.subtle.verify(i,r,n,a))throw nK("JWT signature verification failed",a1,{key:r,data:a,signature:n,algorithm:i})}async function ip(e,t,r,n,a){let i,o,{0:s,1:c,length:u}=e.split(".");if(5===u)if(void 0!==a)e=await a(e),{0:s,1:c,length:u}=e.split(".");else throw new nW("JWE decryption is not configured",{cause:e});if(3!==u)throw nK("Invalid JWT",a1,e);try{i=JSON.parse(n$(nH(s)))}catch(e){throw nK("failed to parse JWT Header body as base64url encoded JSON",a0,e)}if(!nF(i))throw nK("JWT Header must be a top level object",a1,e);if(t(i),void 0!==i.crit)throw new nW('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{o=JSON.parse(n$(nH(c)))}catch(e){throw nK("failed to parse JWT Payload body as base64url encoded JSON",a0,e)}if(!nF(o))throw nK("JWT Payload must be a top level object",a1,e);let l=n9()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nK('unexpected JWT "exp" (expiration time) claim type',a1,{claims:o});if(o.exp<=l-n)throw nK('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',a4,{claims:o,now:l,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nK('unexpected JWT "iat" (issued at) claim type',a1,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nK('unexpected JWT "iss" (issuer) claim type',a1,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nK('unexpected JWT "nbf" (not before) claim type',a1,{claims:o});if(o.nbf>l+n)throw nK('unexpected JWT "nbf" (not before) claim value',a4,{claims:o,now:l,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nK('unexpected JWT "aud" (audience) claim type',a1,{claims:o});return{header:i,claims:o,jwt:e}}async function ih(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nW(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let a=await crypto.subtle.digest(n,n$(e));return nH(a.slice(0,a.byteLength/2))}async function ig(e,t,r,n){return t===await ih(e,r,n)}async function iy(e){if(e.bodyUsed)throw nk("form_post Request instances must contain a readable body",nO,{cause:e});return e.text()}async function im(e){if("POST"!==e.method)throw nk("form_post responses are expected to use the POST method",nO,{cause:e});if("application/x-www-form-urlencoded"!==aP(e))throw nk("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nO,{cause:e});return iy(e)}function ib(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nK('unexpected JWT "alg" header parameter',a1,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nK('unexpected JWT "alg" header parameter',a1,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nK('unexpected JWT "alg" header parameter',a1,{header:n,expected:r,reason:"default value"});return}throw nK('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function iw(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nK(`"${t}" parameter must be provided only once`,a1);return r}let i_=Symbol(),iv=Symbol();function iE(e,t,r,n){var a;if(n7(e),ae(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nk('"parameters" must be an instance of URLSearchParams, or URL',nT);if(iw(r,"response"))throw nK('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',a1,{parameters:r});let i=iw(r,"iss"),o=iw(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw nK('response parameter "iss" (issuer) missing',a1,{parameters:r});if(i&&i!==e.issuer)throw nK('unexpected "iss" (issuer) response parameter value',a1,{expected:e.issuer,parameters:r});switch(n){case void 0:case iv:if(void 0!==o)throw nK('unexpected "state" response parameter encountered',a1,{expected:void 0,parameters:r});break;case i_:break;default:if(nQ(n,'"expectedState" argument'),o!==n)throw nK(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',a1,{expected:n,parameters:r})}if(iw(r,"error"))throw new al("authorization response from the server is an error",{cause:r});let s=iw(r,"id_token"),c=iw(r,"token");if(void 0!==s||void 0!==c)throw new nW("implicit and hybrid flows are not supported");return a=new URLSearchParams(r),a$.add(a),a}async function iS(e,t){let{ext:r,key_ops:n,use:a,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nW("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function iA(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function iR(e,t=n1){let r;try{r=await e.json()}catch(r){throw t(e),nK('failed to parse "response" body as JSON',a0,r)}if(!nF(r))throw nK('"response" body must be a top level object',a1,{body:r});return r}let iP=Symbol(),iO=Symbol();async function iT(e,t,r){let{cookies:n,logger:a}=r,i=n[e],o=new Date;o.setTime(o.getTime()+9e5),a.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:o});let s=await t0({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:o};return{name:i.name,value:s,options:c}}async function ik(e,t,r){try{let{logger:n,cookies:a,jwt:i}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new R(`${e} cookie was missing`);let o=await t1({...i,token:t,salt:a[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new R(`${e} value could not be parsed`,{cause:t})}}function ix(e,t,r){let{logger:n,cookies:a}=t,i=a[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...a[e].options,maxAge:0}})}function iC(e,t){return async function(r,n,a){let{provider:i,logger:o}=a;if(!i?.checks?.includes(e))return;let s=r?.[a.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await ik(t,s,a);return ix(t,a,n),c}}let iN={async create(e){let t=n3(),r=await n8(t);return{cookie:await iT("pkceCodeVerifier",t,e),value:r}},use:iC("pkce","pkceCodeVerifier")},ij="encodedState",iI={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new R("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n3()},a=await t0({secret:e.jwt.secret,token:n,salt:ij,maxAge:900});return{cookie:await iT("state",a,e),value:a}},use:iC("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await t1({secret:t.jwt.secret,token:e,salt:ij});if(r)return r;throw Error("Invalid state")}catch(e){throw new R("State could not be decoded",{cause:e})}}},iD={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n3();return{cookie:await iT("nonce",t,e),value:t}},use:iC("nonce","nonce")},iU="encodedWebauthnChallenge",iM={create:async(e,t,r)=>({cookie:await iT("webauthnChallenge",await t0({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:iU,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],a=await ik("webauthnChallenge",n,e),i=await t1({secret:e.jwt.secret,token:a,salt:iU});if(ix("webauthnChallenge",e,r),!i)throw new R("WebAuthn challenge was missing");return i}};function iL(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function i$(e,t,r){let n,a,i,{logger:o,provider:s}=r,{token:c,userinfo:u}=s;if(c?.url&&"authjs.dev"!==c.url.host||u?.url&&"authjs.dev"!==u.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:u?.url.toString()};else{let e=new URL(s.issuer),t=await nY(e,{[nx]:!0,[nj]:s[rp]});if(!(n=await n0(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let l={client_id:s.clientId,...s.client};switch(l.token_endpoint_auth_method){case void 0:case"client_secret_basic":a=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=iL(e),n=iL(t),a=btoa(`${r}:${n}`);return`Basic ${a}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nQ(d=s.clientSecret,'"clientSecret"'),a=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":a=function(e,t){let r;nQ(e,'"clientSecret"');let n=void 0;return async(t,a,i,o)=>{r||=await crypto.subtle.importKey("raw",n$(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=at(t,a);n?.(s,c);let u=`${nH(n$(JSON.stringify(s)))}.${nH(n$(JSON.stringify(c)))}`,l=await crypto.subtle.sign(r.algorithm,r,n$(u));i.set("client_id",a.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${u}.${nH(new Uint8Array(l))}`)}}(s.clientSecret);break;case"private_key_jwt":a=function(e,t){var r;let{key:n,kid:a}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&nQ(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nG(n,'"clientPrivateKey.key"'),async(e,r,i,o)=>{let s={alg:n6(n),kid:a},c=at(e,r);t?.[nI]?.(s,c),i.set("client_id",r.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await ar(s,c,n))}}(s.token.clientPrivateKey,{[nI](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":a=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],f=await iI.use(t,p,r);try{i=iE(n,l,new URLSearchParams(e),s.checks.includes("state")?f:i_)}catch(e){if(e instanceof al){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new N("OAuth Provider returned an error",t)}throw e}let h=await iN.use(t,p,r),g=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(g=s.redirectProxyUrl);let y=await aW(n,l,a,i,g,h??"decoy",{[nx]:!0,[nj]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rp]??fetch)(...e))});s.token?.conform&&(y=await s.token.conform(y.clone())??y);let m={},b="oidc"===s.type;if(s[rf])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await y.clone().json();if(e.error){let t={providerId:s.id,...e};throw new N(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eR("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new eR("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new eR("Invalid JWT");if(!n)throw new eR("JWTs must contain a payload");try{t=ey(n)}catch{throw new eR("Failed to base64url decode the payload")}try{r=JSON.parse(ed.decode(t))}catch{throw new eR("Failed to parse the decoded payload as JSON")}if(!eN(r))throw new eR("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),a=await nY(r,{[nj]:s[rp]});n=await n0(r,a)}}}let w=await aF(n,l,y,{expectedNonce:await iD.use(t,p,r),requireIdToken:b});if(b){let t=aN(w);if(m=t,s[rf]&&"apple"===s.id)try{m.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await av(n,l,w.access_token,{[nj]:s[rp],[nx]:!0});m=await aO(n,l,t.sub,e)}}else if(u?.request){let e=await u.request({tokens:w,provider:s});e instanceof Object&&(m=e)}else if(u?.url){let e=await av(n,l,w.access_token,{[nj]:s[rp],[nx]:!0});m=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await iH(m,s,w,o),profile:m,cookies:p}}async function iH(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new j(r,{provider:t.id}))}}async function iW(e,t,r,n){let a=await iF(e,t,r),{cookie:i}=await iM.create(e,a.challenge,r);return{status:200,cookies:[...n??[],i],body:{action:"register",options:a},headers:{"Content-Type":"application/json"}}}async function iB(e,t,r,n){let a=await iG(e,t,r),{cookie:i}=await iM.create(e,a.challenge);return{status:200,cookies:[...n??[],i],body:{action:"authenticate",options:a},headers:{"Content-Type":"application/json"}}}async function iK(e,t,r){let n,{adapter:a,provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new g("Invalid WebAuthn Authentication response");let s=iX(iJ(o.id)),c=await a.getAuthenticator(s);if(!c)throw new g(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:u}=await iM.use(e,t.cookies,r);try{var l;let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:u,response:o,authenticator:{...l=c,credentialDeviceType:l.credentialDeviceType,transports:iz(l.transports),credentialID:iJ(l.credentialID),credentialPublicKey:iJ(l.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new V(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new V("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await a.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new m(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let f=await a.getAccount(c.providerAccountId,i.id);if(!f)throw new g(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await a.getUser(f.userId);if(!h)throw new g(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:h}}async function iq(e,t,r){var n;let a,{provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new g("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await iM.use(e,t.cookies,r);if(!c)throw new g("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);a=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new V(e)}if(!a.verified||!a.registrationInfo)throw new V("WebAuthn registration response could not be verified");let u={providerAccountId:iX(a.registrationInfo.credentialID),provider:e.provider.id,type:i.type},l={providerAccountId:u.providerAccountId,counter:a.registrationInfo.counter,credentialID:iX(a.registrationInfo.credentialID),credentialPublicKey:iX(a.registrationInfo.credentialPublicKey),credentialBackedUp:a.registrationInfo.credentialBackedUp,credentialDeviceType:a.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:u,authenticator:l}}async function iG(e,t,r){let{provider:n,adapter:a}=e,i=r&&r.id?await a.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:i?.map(e=>({id:iJ(e.credentialID),type:"public-key",transports:iz(e.transports)}))})}async function iF(e,t,r){let{provider:n,adapter:a}=e,i=r.id?await a.listAuthenticatorsByUserId(r.id):null,o=ri(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:iJ(e.credentialID),type:"public-key",transports:iz(e.transports)}))})}function iV(e){let{provider:t,adapter:r}=e;if(!r)throw new O("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new H("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function iJ(e){return new Uint8Array(Buffer.from(e,"base64"))}function iX(e){return Buffer.from(e).toString("base64")}function iz(e){return e?e.split(","):void 0}async function iY(e,t,r,n){if(!t.provider)throw new H("Callback route called without provider");let{query:a,body:i,method:o,headers:s}=e,{provider:c,adapter:u,url:l,callbackUrl:d,pages:p,jwt:f,events:h,callbacks:y,session:{strategy:m,maxAge:b},logger:_}=t,v="jwt"===m;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:a;if(t.isOnRedirectProxy&&s?.state){let e=await iI.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return _.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let g=await i$(s,e.cookies,t);g.cookies.length&&n.push(...g.cookies),_.debug("authorization result",g);let{user:m,account:w,profile:E}=g;if(!m||!w||!E)return{redirect:`${l}/signin`,cookies:n};if(u){let{getUserByAccount:e}=u;o=await e({providerAccountId:w.providerAccountId,provider:c.id})}let S=await iZ({user:o??m,account:w,profile:E},t);if(S)return{redirect:S,cookies:n};let{user:A,session:R,isNewUser:P}=await nR(r.value,m,w,t);if(v){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},a=await y.jwt({token:e,user:A,account:w,profile:E,isNewUser:P,trigger:P?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:R.sessionToken,options:{...t.cookies.sessionToken.options,expires:R.expires}});if(await h.signIn?.({user:A,account:w,profile:E,isNewUser:P}),P&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=a?.token,i=a?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await u.useVerificationToken({identifier:i,token:await ra(`${e}${o}`)}),l=!!s,g=l&&s.expires.valueOf()<Date.now();if(!l||g||i&&s.identifier!==i)throw new B({hasInvite:l,expired:g});let{identifier:m}=s,w=await u.getUserByEmail(m)??{id:crypto.randomUUID(),email:m,emailVerified:null},_={providerAccountId:w.email,userId:w.id,type:"email",provider:c.id},E=await iZ({user:w,account:_},t);if(E)return{redirect:E,cookies:n};let{user:S,session:A,isNewUser:R}=await nR(r.value,w,_,t);if(v){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},a=await y.jwt({token:e,user:S,account:_,isNewUser:R,trigger:R?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await h.signIn?.({user:S,account:_,isNewUser:R}),R&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=i??{};Object.entries(a??{}).forEach(([e,t])=>l.searchParams.set(e,t));let u=await c.authorize(e,new Request(l,{headers:s,method:o,body:JSON.stringify(i)}));if(u)u.id=u.id?.toString()??crypto.randomUUID();else throw new S;let p={providerAccountId:u.id,type:"credentials",provider:c.id},g=await iZ({user:u,account:p,credentials:e},t);if(g)return{redirect:g,cookies:n};let m={name:u.name,email:u.email,picture:u.image,sub:u.id},w=await y.jwt({token:m,user:u,account:p,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:w,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*b);let o=r.chunk(a,{expires:i});n.push(...o)}return await h.signIn?.({user:u,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let a,i,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new g("Invalid action parameter");let c=iV(t);switch(s){case"authenticate":{let t=await iK(c,e,n);a=t.user,i=t.account;break}case"register":{let r=await iq(t,e,n);a=r.user,i=r.account,o=r.authenticator}}await iZ({user:a,account:i},t);let{user:u,isNewUser:l,session:m,account:w}=await nR(r.value,a,i,t);if(!w)throw new g("Error creating or finding account");if(o&&u.id&&await c.adapter.createAuthenticator({...o,userId:u.id}),v){let e={name:u.name,email:u.email,picture:u.image,sub:u.id?.toString()},a=await y.jwt({token:e,user:u,account:w,isNewUser:l,trigger:l?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await h.signIn?.({user:u,account:w,isNewUser:l}),l&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new H(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof g)throw t;let e=new w(t,{provider:c.id});throw _.debug("callback route error details",{method:o,query:a,body:i}),e}}async function iZ(e,t){let r,{signIn:n,redirect:a}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof g)throw e;throw new b(e)}if(!r)throw new b("AccessDenied");if("string"==typeof r)return await a({url:r,baseUrl:t.url.origin})}async function iQ(e,t,r,n,a){let{adapter:i,jwt:o,events:s,callbacks:c,logger:u,session:{strategy:l,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},f=t.value;if(!f)return p;if("jwt"===l){try{let r=e.cookies.sessionToken.name,i=await o.decode({...o,token:f,salt:r});if(!i)throw Error("Invalid JWT");let u=await c.jwt({token:i,...n&&{trigger:"update"},session:a}),l=nA(d);if(null!==u){let e={user:{name:u.name,email:u.email,image:u.picture},expires:l.toISOString()},n=await c.session({session:e,token:u});p.body=n;let a=await o.encode({...o,token:u,salt:r}),i=t.chunk(a,{expires:l});p.cookies?.push(...i),await s.session?.({session:n,token:u})}else p.cookies?.push(...t.clean())}catch(e){u.error(new P(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:u}=i,l=await r(f);if(l&&l.session.expires.valueOf()<Date.now()&&(await o(f),l=null),l){let{user:t,session:r}=l,i=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*i,h=nA(d);o<=Date.now()&&await u({sessionToken:f,expires:h});let g=await c.session({session:{...r,user:t},user:t,newSession:a,...n?{trigger:"update"}:{}});p.body=g,p.cookies?.push({name:e.cookies.sessionToken.name,value:f,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:g})}else f&&p.cookies?.push(...t.clean())}catch(e){u.error(new I(e))}return p}async function i0(e,t){let r,n,{logger:a,provider:i}=t,o=i.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(i.issuer),t=await nY(e,{[nj]:i[rp],[nx]:!0}),r=await n0(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,n=i.callbackUrl,a.debug("using redirect proxy",{redirect_uri:c,data:n}));let u=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in u)s.set(e,u[e]);let l=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await iI.create(t,n);if(d&&(s.set("state",d.value),l.push(d.cookie)),i.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await iN.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),l.push(r)}let p=await iD.create(t);return p&&(s.set("nonce",p.value),l.push(p.cookie)),"oidc"!==i.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),a.debug("authorization url is ready",{url:o,cookies:l,provider:i}),{redirect:o.toString(),cookies:l}}async function i1(e,t){let r,{body:n}=e,{provider:a,callbacks:i,adapter:o}=t,s=(a.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},u=await o.getUserByEmail(s)??c,l={providerAccountId:s,userId:u.id,type:"email",provider:a.id};try{r=await i.signIn({user:u,account:l,email:{verificationRequest:!0}})}catch(e){throw new b(e)}if(!r)throw new b("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,f=await a.generateVerificationToken?.()??ri(32),h=new Date(Date.now()+(a.maxAge??86400)*1e3),g=a.secret??t.secret,y=new URL(t.basePath,t.url.origin),m=a.sendVerificationRequest({identifier:s,token:f,expires:h,url:`${y}/callback/${a.id}?${new URLSearchParams({callbackUrl:d,token:f,email:s})}`,provider:a,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),w=o.createVerificationToken?.({identifier:s,token:await ra(`${f}${g}`),expires:h});return await Promise.all([m,w]),{redirect:`${y}/verify-request?${new URLSearchParams({provider:a.id,type:a.type})}`}}async function i2(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:a}=await i0(e.query,r);return a&&t.push(...a),{redirect:n,cookies:t}}case"email":return{...await i1(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function i3(e,t,r){let{jwt:n,events:a,callbackUrl:i,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await a.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await a.signOut?.({session:e})}}catch(e){o.error(new M(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function i8(e,t){let{adapter:r,jwt:n,session:{strategy:a}}=e,i=t.value;if(!i)return null;if("jwt"===a){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function i6(e,t,r,n){let a=iV(t),{provider:i}=a,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await i8(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),u=c?.user;switch(function(e,t,r){let{user:n,exists:a=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===a)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(a)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return iB(a,e,u,n);case"register":if("string"==typeof u?.email)return iW(a,e,u,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function i5(e,t){let{action:r,providerId:n,error:a,method:i}=e,o=t.skipCSRFCheck===rl,{options:s,cookies:c}=await rw({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:o}),u=new h(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=nS({...s,query:e.query,cookies:c});switch(r){case"callback":return await iY(e,s,u,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(a);case"providers":return t.providers(s.providers);case"session":return await iQ(s,u,c);case"signin":return t.signin(n,a);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await i6(e,s,u,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&rs(r,t),await iY(e,s,u,c);case"session":return rs(r,t),await iQ(s,u,c,!0,e.body?.data);case"signin":return rs(r,t),await i2(e,c,s);case"signout":return rs(r,t),await i3(c,u,s)}}throw new L(`Cannot handle action: ${r}`)}function i4(e,t,r,n,a){let i,o=a?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)i=new URL(s),o&&"/"!==o&&"/"!==i.pathname&&(i.pathname!==o&&t4(a).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",a=n.endsWith(":")?n:n+":";i=new URL(`${a}//${e}`)}let c=i.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function i9(e,t){let r=t4(t),n=await rr(e,t);if(!n)return Response.json("Bad request.",{status:400});let a=function(e,t){let{url:r}=e,n=[];if(!z&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new W(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new x("Please define a `secret`");let a=e.query?.callbackUrl;if(a&&!Y(a,r.origin))return new E(`Invalid callback URL. Received: ${a}`);let{callbackUrl:i}=f(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(o&&!Y(o,r.origin))return new E(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:a}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof a||a?.url||(e="userinfo"):e="token":e="authorization",e)return new A(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)Z=!0;else if("email"===t.type)Q=!0;else if("webauthn"===t.type){var c;if(ee=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new g(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new G("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new F(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(Z){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new $("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new k("Must define an authorize() handler to use credentials authentication provider")}let{adapter:u,session:l}=t,d=[];if(Q||l?.strategy==="database"||!l?.strategy&&u)if(Q){if(!u)return new O("Email login requires an adapter");d.push(...et)}else{if(!u)return new O("Database session requires an adapter");d.push(...er)}if(ee){if(!t.experimental?.enableWebAuthn)return new X("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!u)return new O("WebAuthn requires an adapter");d.push(...en)}if(u){let e=d.filter(e=>!(e in u));if(e.length)return new T(`Required adapter methods were missing: ${e.join(", ")}`)}return z||(z=!0),n}(n,t);if(Array.isArray(a))a.forEach(r.warn);else if(a){if(r.error(a),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new _(`The error page ${e?.error} should not require authentication`)),rn(nS({theme:i}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rd;try{let e=await i5(n,t);if(o)return e;let r=rn(e),a=r.headers.get("Location");if(!i||!a)return r;return Response.json({url:a},{headers:r.headers})}catch(d){r.error(d);let a=d instanceof g;if(a&&o&&!i)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof g&&q.has(d.type)?d.type:"Configuration"});d instanceof S&&s.set("code",d.code);let c=a&&d.kind||"error",u=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,l=`${n.url.origin}${u}?${s}`;if(i)return Response.json({url:l});return Response.redirect(l)}}var i7=r(4525);function oe(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:a}=e.nextUrl;return new i7.NextRequest(n.replace(a,r),e)}function ot(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t4(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),a=e[`AUTH_${n}_ID`],i=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:a,clientSecret:i,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=a),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var or=r(9933),on=r(6280);async function oa(e,t){return i9(new Request(i4("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function oi(e){return"function"==typeof e}function oo(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,on.b)(),n=await e(void 0);return t?.(n),oa(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],a=r[1],i=await e(n);return t?.(i),os([n,a],i)}if(oi(r[0])){let n=r[0];return async(...r)=>{let a=await e(r[0]);return t?.(a),os(r,a,n)}}let n="req"in r[0]?r[0].req:r[0],a="res"in r[0]?r[0].res:r[1],i=await e(n);return t?.(i),oa(new Headers(n.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,on.b)()).then(t=>oa(t,e).then(e=>e.json()));if(t[0]instanceof Request)return os([t[0],t[1]],e);if(oi(t[0])){let r=t[0];return async(...t)=>os(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return oa(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function os(e,t,r){let n=oe(e[0]),a=await oa(n.headers,t),i=await a.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:i}));let s=i7.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),a=Object.values(r.pages??{});return(oc.has(n)||a.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=i,s=await r(n,e[1])??i7.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=i7.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of a.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(3913);let oc=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ou=r(7576);async function ol(e,t={},r,n){let a=new Headers(await (0,on.b)()),{redirect:i=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??a.get("Referer")??"/",u=i4("signin",a.get("x-forwarded-proto"),a,process.env,n);if(!e)return u.searchParams.append("callbackUrl",c),i&&(0,ou.redirect)(u.toString()),u.toString();let l=`${u}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,a=r?.id??n.id;if(a===e){d={id:a,type:r?.type??n.type};break}}if(!d.id){let e=`${u}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,ou.redirect)(e),e}"credentials"===d.type&&(l=l.replace("signin","callback")),a.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(l,{method:"POST",headers:a,body:new URLSearchParams({...s,callbackUrl:c})}),f=await i9(p,{...n,raw:rd,skipCSRFCheck:rl}),h=await (0,or.U)();for(let e of f?.cookies??[])h.set(e.name,e.value,e.options);let g=(f instanceof Response?f.headers.get("Location"):f.redirect)??l;return i?(0,ou.redirect)(g):g}async function od(e,t){let r=new Headers(await (0,on.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=i4("signout",r.get("x-forwarded-proto"),r,process.env,t),a=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(n,{method:"POST",headers:r,body:a}),o=await i9(i,{...t,raw:rd,skipCSRFCheck:rl}),s=await (0,or.U)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,ou.redirect)(o.redirect):o}async function op(e,t){let r=new Headers(await (0,on.b)());r.set("Content-Type","application/json");let n=new Request(i4("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),a=await i9(n,{...t,raw:rd,skipCSRFCheck:rl}),i=await (0,or.U)();for(let e of a?.cookies??[])i.set(e.name,e.value,e.options);return a.body}function of(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return ot(r),i9(oe(t),r)};return{handlers:{GET:t,POST:t},auth:oo(e,e=>ot(e)),signIn:async(t,r,n)=>{let a=await e(void 0);return ot(a),ol(t,r,n,a)},signOut:async t=>{let r=await e(void 0);return ot(r),od(t,r)},unstable_update:async t=>{let r=await e(void 0);return ot(r),op(t,r)}}}ot(e);let t=t=>i9(oe(t),e);return{handlers:{GET:t,POST:t},auth:oo(e),signIn:(t,r,n)=>ol(t,r,n,e),signOut:t=>od(t,e),unstable_update:t=>op(t,e)}}},9893:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NextRequestAdapter:function(){return d},ResponseAborted:function(){return c},ResponseAbortedName:function(){return s},createAbortController:function(){return u},signalFromNodeResponse:function(){return l}});let n=r(6191),a=r(7912),i=r(6268),o=r(898),s="ResponseAborted";class c extends Error{constructor(...e){super(...e),this.name=s}}function u(e){let t=new AbortController;return e.once("close",()=>{e.writableFinished||t.abort(new c)}),t}function l(e){let{errored:t,destroyed:r}=e;if(t||r)return AbortSignal.abort(t??new c);let{signal:n}=u(e);return n}class d{static fromBaseNextRequest(e,t){if((0,o.isNodeNextRequest)(e))return d.fromNodeNextRequest(e,t);throw Object.defineProperty(Error("Invariant: Unsupported NextRequest type"),"__NEXT_ERROR_CODE",{value:"E345",enumerable:!1,configurable:!0})}static fromNodeNextRequest(e,t){let r,o=null;if("GET"!==e.method&&"HEAD"!==e.method&&e.body&&(o=e.body),e.url.startsWith("http"))r=new URL(e.url);else{let t=(0,n.getRequestMeta)(e,"initURL");r=t&&t.startsWith("http")?new URL(e.url,t):new URL(e.url,"http://n")}return new i.NextRequest(r,{method:e.method,headers:(0,a.fromNodeOutgoingHttpHeaders)(e.headers),duplex:"half",signal:t,...t.aborted?{}:{body:o}})}static fromWebNextRequest(e){let t=null;return"GET"!==e.method&&"HEAD"!==e.method&&(t=e.body),new i.NextRequest(e.url,{method:e.method,headers:(0,a.fromNodeOutgoingHttpHeaders)(e.headers),duplex:"half",signal:e.request.signal,...e.request.signal.aborted?{}:{body:t}})}}},9933:(e,t,r)=>{"use strict";Object.defineProperty(t,"U",{enumerable:!0,get:function(){return p}});let n=r(4069),a=r(3158),i=r(9294),o=r(3033),s=r(4971),c=r(23),u=r(8388),l=r(6926),d=(r(4523),r(8719));function p(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var l=t.route,p=r;let e=f.get(p);if(e)return e;let n=(0,u.makeHangingPromise)(p.renderSignal,"`cookies()`");return f.set(p,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},size:{get(){let e="`cookies().size`",t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${g(arguments[0])})\``;let t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${g(arguments[0])})\``;let t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${g(arguments[0])})\``;let t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${g(t)}, ...)\``:"`cookies().set(...)`"}let t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${g(arguments[0])})\``:`\`cookies().delete(${g(arguments[0])}, ...)\``;let t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},clear:{value:function(){let e="`cookies().clear()`",t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}},toString:{value:function(){let e="`cookies().toString()`",t=m(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,p)}}}),n}else"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r);(0,s.trackDynamicDataInDynamicRender)(t,r)}let y=(0,o.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(y)?y.userspaceMutableCookies:y.cookies)}let f=new WeakMap;function h(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):b.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):w.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function g(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let y=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function b(){return this.getAll().map(e=>[e.name,e]).values()}function w(e){for(let e of this.getAll())this.delete(e.name);return e}},9938:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return o}});let n=r(1959),a=r(9229),i=r(2829);function o(e,t){var r,o;let{basePath:s,i18n:c,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},l={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};s&&(0,i.pathHasPrefix)(l.pathname,s)&&(l.pathname=(0,a.removePathPrefix)(l.pathname,s),l.basePath=s);let d=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){let e=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");l.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(l.pathname=d)}if(c){let e=t.i18nProvider?t.i18nProvider.analyze(l.pathname):(0,n.normalizeLocalePath)(l.pathname,c.locales);l.locale=e.detectedLocale,l.pathname=null!=(o=e.pathname)?o:l.pathname,!e.detectedLocale&&l.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,c.locales)).detectedLocale&&(l.locale=e.detectedLocale)}return l}}};