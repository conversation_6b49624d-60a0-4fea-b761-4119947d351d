exports.id=97;exports.ids=[97];exports.modules={23:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{StaticGenBailoutError:function(){return c},isStaticGenBailoutError:function(){return u}});const s="NEXT_STATIC_GEN_BAILOUT";class c extends Error{constructor(...o){super(...o),this.code=s}}function u(o){if(typeof o!=="object"||o===null||!("code"in o)){return false}return o.code===s}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},163:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"unstable_rethrow",{enumerable:true,get:function(){return s}});const s=true?a(1042).unstable_rethrow:0;if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},366:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"DetachedPromise",{enumerable:true,get:function(){return a}});class a{constructor(){let o;let i;this.promise=new Promise((a,s)=>{o=a;i=s});this.resolve=o;this.reject=i}}},397:(o,i,a)=>{var s;(()=>{var c={226:function(c,u){(function(l,d){"use strict";var f="1.0.35",p="",h="?",y="function",g="undefined",m="object",b="string",w="major",_="model",v="name",E="type",S="vendor",R="version",A="architecture",P="console",k="mobile",O="tablet",T="smarttv",x="wearable",C="embedded",j=350;var N="Amazon",I="Apple",D="ASUS",U="BlackBerry",M="Browser",L="Chrome",$="Edge",H="Firefox",W="Google",K="Huawei",B="LG",q="Microsoft",G="Motorola",F="Opera",J="Samsung",V="Sharp",z="Sony",X="Viera",Y="Xiaomi",Z="Zebra",Q="Facebook",ee="Chromium OS",et="Mac OS";var en=function(o,i){var a={};for(var s in o){if(i[s]&&i[s].length%2===0){a[s]=i[s].concat(o[s])}else{a[s]=o[s]}}return a},er=function(o){var i={};for(var a=0;a<o.length;a++){i[o[a].toUpperCase()]=o[a]}return i},eo=function(o,i){return typeof o===b?ei(i).indexOf(ei(o))!==-1:false},ei=function(o){return o.toLowerCase()},ea=function(o){return typeof o===b?o.replace(/[^\d\.]/g,p).split(".")[0]:d},es=function(o,i){if(typeof o===b){o=o.replace(/^\s\s*/,p);return typeof i===g?o:o.substring(0,j)}};var ec=function(o,i){var a=0,s,c,u,l,f,p;while(a<i.length&&!f){var h=i[a],g=i[a+1];s=c=0;while(s<h.length&&!f){if(!h[s]){break}f=h[s++].exec(o);if(!!f){for(u=0;u<g.length;u++){p=f[++c];l=g[u];if(typeof l===m&&l.length>0){if(l.length===2){if(typeof l[1]==y){this[l[0]]=l[1].call(this,p)}else{this[l[0]]=l[1]}}else if(l.length===3){if(typeof l[1]===y&&!(l[1].exec&&l[1].test)){this[l[0]]=p?l[1].call(this,p,l[2]):d}else{this[l[0]]=p?p.replace(l[1],l[2]):d}}else if(l.length===4){this[l[0]]=p?l[3].call(this,p.replace(l[1],l[2])):d}}else{this[l]=p?p:d}}}}a+=2}},eu=function(o,i){for(var a in i){if(typeof i[a]===m&&i[a].length>0){for(var s=0;s<i[a].length;s++){if(eo(i[a][s],o)){return a===h?d:a}}}else if(eo(i[a],o)){return a===h?d:a}}return o};var el={"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},ed={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"};var ef={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[R,[v,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[R,[v,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[v,R],[/opios[\/ ]+([\w\.]+)/i],[R,[v,F+" Mini"]],[/\bopr\/([\w\.]+)/i],[R,[v,F]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[v,R],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[R,[v,"UC"+M]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[R,[v,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[R,[v,"WeChat"]],[/konqueror\/([\w\.]+)/i],[R,[v,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[R,[v,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[R,[v,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[v,/(.+)/,"$1 Secure "+M],R],[/\bfocus\/([\w\.]+)/i],[R,[v,H+" Focus"]],[/\bopt\/([\w\.]+)/i],[R,[v,F+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[R,[v,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[R,[v,"Dolphin"]],[/coast\/([\w\.]+)/i],[R,[v,F+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[R,[v,"MIUI "+M]],[/fxios\/([-\w\.]+)/i],[R,[v,H]],[/\bqihu|(qi?ho?o?|360)browser/i],[[v,"360 "+M]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[v,/(.+)/,"$1 "+M],R],[/(comodo_dragon)\/([\w\.]+)/i],[[v,/_/g," "],R],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[v,R],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[v],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[v,Q],R],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[v,R],[/\bgsa\/([\w\.]+) .*safari\//i],[R,[v,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[R,[v,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[R,[v,L+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[v,L+" WebView"],R],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[R,[v,"Android "+M]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[v,R],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[R,[v,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[R,v],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[v,[R,eu,el]],[/(webkit|khtml)\/([\w\.]+)/i],[v,R],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[v,"Netscape"],R],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[R,[v,H+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[v,R],[/(cobalt)\/([\w\.]+)/i],[v,[R,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[A,"amd64"]],[/(ia32(?=;))/i],[[A,ei]],[/((?:i[346]|x)86)[;\)]/i],[[A,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[A,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[A,"armhf"]],[/windows (ce|mobile); ppc;/i],[[A,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[A,/ower/,p,ei]],[/(sun4\w)[;\)]/i],[[A,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[A,ei]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[_,[S,J],[E,O]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[_,[S,J],[E,k]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[_,[S,I],[E,k]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[_,[S,I],[E,O]],[/(macintosh);/i],[_,[S,I]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[_,[S,V],[E,k]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[_,[S,K],[E,O]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[_,[S,K],[E,k]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[_,/_/g," "],[S,Y],[E,k]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[_,/_/g," "],[S,Y],[E,O]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[_,[S,"OPPO"],[E,k]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[_,[S,"Vivo"],[E,k]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[_,[S,"Realme"],[E,k]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[_,[S,G],[E,k]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[_,[S,G],[E,O]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[_,[S,B],[E,O]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[_,[S,B],[E,k]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[_,[S,"Lenovo"],[E,O]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[_,/_/g," "],[S,"Nokia"],[E,k]],[/(pixel c)\b/i],[_,[S,W],[E,O]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[_,[S,W],[E,k]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[_,[S,z],[E,k]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[_,"Xperia Tablet"],[S,z],[E,O]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[_,[S,"OnePlus"],[E,k]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[_,[S,N],[E,O]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[_,/(.+)/g,"Fire Phone $1"],[S,N],[E,k]],[/(playbook);[-\w\),; ]+(rim)/i],[_,S,[E,O]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[_,[S,U],[E,k]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[_,[S,D],[E,O]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[_,[S,D],[E,k]],[/(nexus 9)/i],[_,[S,"HTC"],[E,O]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[S,[_,/_/g," "],[E,k]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[_,[S,"Acer"],[E,O]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[_,[S,"Meizu"],[E,k]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[S,_,[E,k]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[S,_,[E,O]],[/(surface duo)/i],[_,[S,q],[E,O]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[_,[S,"Fairphone"],[E,k]],[/(u304aa)/i],[_,[S,"AT&T"],[E,k]],[/\bsie-(\w*)/i],[_,[S,"Siemens"],[E,k]],[/\b(rct\w+) b/i],[_,[S,"RCA"],[E,O]],[/\b(venue[\d ]{2,7}) b/i],[_,[S,"Dell"],[E,O]],[/\b(q(?:mv|ta)\w+) b/i],[_,[S,"Verizon"],[E,O]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[_,[S,"Barnes & Noble"],[E,O]],[/\b(tm\d{3}\w+) b/i],[_,[S,"NuVision"],[E,O]],[/\b(k88) b/i],[_,[S,"ZTE"],[E,O]],[/\b(nx\d{3}j) b/i],[_,[S,"ZTE"],[E,k]],[/\b(gen\d{3}) b.+49h/i],[_,[S,"Swiss"],[E,k]],[/\b(zur\d{3}) b/i],[_,[S,"Swiss"],[E,O]],[/\b((zeki)?tb.*\b) b/i],[_,[S,"Zeki"],[E,O]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[S,"Dragon Touch"],_,[E,O]],[/\b(ns-?\w{0,9}) b/i],[_,[S,"Insignia"],[E,O]],[/\b((nxa|next)-?\w{0,9}) b/i],[_,[S,"NextBook"],[E,O]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[S,"Voice"],_,[E,k]],[/\b(lvtel\-)?(v1[12]) b/i],[[S,"LvTel"],_,[E,k]],[/\b(ph-1) /i],[_,[S,"Essential"],[E,k]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[_,[S,"Envizen"],[E,O]],[/\b(trio[-\w\. ]+) b/i],[_,[S,"MachSpeed"],[E,O]],[/\btu_(1491) b/i],[_,[S,"Rotor"],[E,O]],[/(shield[\w ]+) b/i],[_,[S,"Nvidia"],[E,O]],[/(sprint) (\w+)/i],[S,_,[E,k]],[/(kin\.[onetw]{3})/i],[[_,/\./g," "],[S,q],[E,k]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[_,[S,Z],[E,O]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[_,[S,Z],[E,k]],[/smart-tv.+(samsung)/i],[S,[E,T]],[/hbbtv.+maple;(\d+)/i],[[_,/^/,"SmartTV"],[S,J],[E,T]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[S,B],[E,T]],[/(apple) ?tv/i],[S,[_,I+" TV"],[E,T]],[/crkey/i],[[_,L+"cast"],[S,W],[E,T]],[/droid.+aft(\w)( bui|\))/i],[_,[S,N],[E,T]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[_,[S,V],[E,T]],[/(bravia[\w ]+)( bui|\))/i],[_,[S,z],[E,T]],[/(mitv-\w{5}) bui/i],[_,[S,Y],[E,T]],[/Hbbtv.*(technisat) (.*);/i],[S,_,[E,T]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[S,es],[_,es],[E,T]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[E,T]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[S,_,[E,P]],[/droid.+; (shield) bui/i],[_,[S,"Nvidia"],[E,P]],[/(playstation [345portablevi]+)/i],[_,[S,z],[E,P]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[_,[S,q],[E,P]],[/((pebble))app/i],[S,_,[E,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[_,[S,I],[E,x]],[/droid.+; (glass) \d/i],[_,[S,W],[E,x]],[/droid.+; (wt63?0{2,3})\)/i],[_,[S,Z],[E,x]],[/(quest( 2| pro)?)/i],[_,[S,Q],[E,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[S,[E,C]],[/(aeobc)\b/i],[_,[S,N],[E,C]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[_,[E,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[_,[E,O]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[E,O]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[E,k]],[/(android[-\w\. ]{0,9});.+buil/i],[_,[S,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[R,[v,$+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[R,[v,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[v,R],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[R,v]],os:[[/microsoft (windows) (vista|xp)/i],[v,R],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[v,[R,eu,ed]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[v,"Windows"],[R,eu,ed]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[R,/_/g,"."],[v,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[v,et],[R,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[R,v],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[v,R],[/\(bb(10);/i],[R,[v,U]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[R,[v,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[R,[v,H+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[R,[v,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[R,[v,"watchOS"]],[/crkey\/([\d\.]+)/i],[R,[v,L+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[v,ee],R],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[v,R],[/(sunos) ?([\w\.\d]*)/i],[[v,"Solaris"],R],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[v,R]]};var ep=function(o,i){if(typeof o===m){i=o;o=d}if(!(this instanceof ep)){return new ep(o,i).getResult()}var a=typeof l!==g&&l.navigator?l.navigator:d;var s=o||(a&&a.userAgent?a.userAgent:p);var c=a&&a.userAgentData?a.userAgentData:d;var u=i?en(ef,i):ef;var f=a&&a.userAgent==s;this.getBrowser=function(){var o={};o[v]=d;o[R]=d;ec.call(o,s,u.browser);o[w]=ea(o[R]);if(f&&a&&a.brave&&typeof a.brave.isBrave==y){o[v]="Brave"}return o};this.getCPU=function(){var o={};o[A]=d;ec.call(o,s,u.cpu);return o};this.getDevice=function(){var o={};o[S]=d;o[_]=d;o[E]=d;ec.call(o,s,u.device);if(f&&!o[E]&&c&&c.mobile){o[E]=k}if(f&&o[_]=="Macintosh"&&a&&typeof a.standalone!==g&&a.maxTouchPoints&&a.maxTouchPoints>2){o[_]="iPad";o[E]=O}return o};this.getEngine=function(){var o={};o[v]=d;o[R]=d;ec.call(o,s,u.engine);return o};this.getOS=function(){var o={};o[v]=d;o[R]=d;ec.call(o,s,u.os);if(f&&!o[v]&&c&&c.platform!="Unknown"){o[v]=c.platform.replace(/chrome os/i,ee).replace(/macos/i,et)}return o};this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}};this.getUA=function(){return s};this.setUA=function(o){s=typeof o===b&&o.length>j?es(o,j):o;return this};this.setUA(s);return this};ep.VERSION=f;ep.BROWSER=er([v,R,w]);ep.CPU=er([A]);ep.DEVICE=er([_,S,E,P,k,T,O,x,C]);ep.ENGINE=ep.OS=er([v,R]);if(typeof u!==g){if("object"!==g&&c.exports){u=c.exports=ep}u.UAParser=ep}else{if("function"===y&&a.amdO){!(s=(function(){return ep}).call(i,a,i,o),s!==undefined&&(o.exports=s))}else if(typeof l!==g){l.UAParser=ep}}var eh=typeof l!==g&&(l.jQuery||l.Zepto);if(eh&&!eh.ua){var ey=new ep;eh.ua=ey.getResult();eh.ua.get=function(){return ey.getUA()};eh.ua.set=function(o){ey.setUA(o);var i=ey.getResult();for(var a in i){eh.ua[a]=i[a]}}}})(typeof window==="object"?window:this)}};var u={};function l(o){var i=u[o];if(i!==undefined){return i.exports}var a=u[o]={exports:{}};var s=true;try{c[o].call(a.exports,a,a.exports,l);s=false}finally{if(s)delete u[o]}return a.exports}if(typeof l!=="undefined")l.ab=__dirname+"/";var d=l(226);o.exports=d})()},635:o=>{"use strict";var i=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var s=Object.getOwnPropertyNames;var c=Object.prototype.hasOwnProperty;var u=(o,a)=>{for(var s in a)i(o,s,{get:a[s],enumerable:true})};var l=(o,u,l,d)=>{if(u&&typeof u==="object"||typeof u==="function"){for(let f of s(u))if(!c.call(o,f)&&f!==l)i(o,f,{get:()=>u[f],enumerable:!(d=a(u,f))||d.enumerable})}return o};var d=o=>l(i({},"__esModule",{value:true}),o);var f={};u(f,{RequestCookies:()=>E,ResponseCookies:()=>S,parseCookie:()=>h,parseSetCookie:()=>y,stringifyCookie:()=>p});o.exports=d(f);function p(o){var i;const a=["path"in o&&o.path&&`Path=${o.path}`,"expires"in o&&(o.expires||o.expires===0)&&`Expires=${(typeof o.expires==="number"?new Date(o.expires):o.expires).toUTCString()}`,"maxAge"in o&&typeof o.maxAge==="number"&&`Max-Age=${o.maxAge}`,"domain"in o&&o.domain&&`Domain=${o.domain}`,"secure"in o&&o.secure&&"Secure","httpOnly"in o&&o.httpOnly&&"HttpOnly","sameSite"in o&&o.sameSite&&`SameSite=${o.sameSite}`,"partitioned"in o&&o.partitioned&&"Partitioned","priority"in o&&o.priority&&`Priority=${o.priority}`].filter(Boolean);const s=`${o.name}=${encodeURIComponent((i=o.value)!=null?i:"")}`;return a.length===0?s:`${s}; ${a.join("; ")}`}function h(o){const i=new Map;for(const a of o.split(/; */)){if(!a)continue;const o=a.indexOf("=");if(o===-1){i.set(a,"true");continue}const[s,c]=[a.slice(0,o),a.slice(o+1)];try{i.set(s,decodeURIComponent(c!=null?c:"true"))}catch{}}return i}function y(o){if(!o){return void 0}const[[i,a],...s]=h(o);const{domain:c,expires:u,httponly:l,maxage:d,path:f,samesite:p,secure:y,partitioned:m,priority:w}=Object.fromEntries(s.map(([o,i])=>[o.toLowerCase().replace(/-/g,""),i]));const v={name:i,value:decodeURIComponent(a),domain:c,...u&&{expires:new Date(u)},...l&&{httpOnly:true},...typeof d==="string"&&{maxAge:Number(d)},path:f,...p&&{sameSite:b(p)},...y&&{secure:true},...w&&{priority:_(w)},...m&&{partitioned:true}};return g(v)}function g(o){const i={};for(const a in o){if(o[a]){i[a]=o[a]}}return i}var m=["strict","lax","none"];function b(o){o=o.toLowerCase();return m.includes(o)?o:void 0}var w=["low","medium","high"];function _(o){o=o.toLowerCase();return w.includes(o)?o:void 0}function v(o){if(!o)return[];var i=[];var a=0;var s;var c;var u;var l;var d;function f(){while(a<o.length&&/\s/.test(o.charAt(a))){a+=1}return a<o.length}function p(){c=o.charAt(a);return c!=="="&&c!==";"&&c!==","}while(a<o.length){s=a;d=false;while(f()){c=o.charAt(a);if(c===","){u=a;a+=1;f();l=a;while(a<o.length&&p()){a+=1}if(a<o.length&&o.charAt(a)==="="){d=true;a=l;i.push(o.substring(s,u));s=a}else{a=u+1}}else{a+=1}}if(!d||a>=o.length){i.push(o.substring(s,o.length))}}return i}var E=class{constructor(o){this._parsed=new Map;this._headers=o;const i=o.get("cookie");if(i){const o=h(i);for(const[i,a]of o){this._parsed.set(i,{name:i,value:a})}}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...o){const i=typeof o[0]==="string"?o[0]:o[0].name;return this._parsed.get(i)}getAll(...o){var i;const a=Array.from(this._parsed);if(!o.length){return a.map(([o,i])=>i)}const s=typeof o[0]==="string"?o[0]:(i=o[0])==null?void 0:i.name;return a.filter(([o])=>o===s).map(([o,i])=>i)}has(o){return this._parsed.has(o)}set(...o){const[i,a]=o.length===1?[o[0].name,o[0].value]:o;const s=this._parsed;s.set(i,{name:i,value:a});this._headers.set("cookie",Array.from(s).map(([o,i])=>p(i)).join("; "));return this}delete(o){const i=this._parsed;const a=!Array.isArray(o)?i.delete(o):o.map(o=>i.delete(o));this._headers.set("cookie",Array.from(i).map(([o,i])=>p(i)).join("; "));return a}clear(){this.delete(Array.from(this._parsed.keys()));return this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o=>`${o.name}=${encodeURIComponent(o.value)}`).join("; ")}};var S=class{constructor(o){this._parsed=new Map;var i,a,s;this._headers=o;const c=(s=(a=(i=o.getSetCookie)==null?void 0:i.call(o))!=null?a:o.get("set-cookie"))!=null?s:[];const u=Array.isArray(c)?c:v(c);for(const o of u){const i=y(o);if(i)this._parsed.set(i.name,i)}}get(...o){const i=typeof o[0]==="string"?o[0]:o[0].name;return this._parsed.get(i)}getAll(...o){var i;const a=Array.from(this._parsed.values());if(!o.length){return a}const s=typeof o[0]==="string"?o[0]:(i=o[0])==null?void 0:i.name;return a.filter(o=>o.name===s)}has(o){return this._parsed.has(o)}set(...o){const[i,a,s]=o.length===1?[o[0].name,o[0].value,o[0]]:o;const c=this._parsed;c.set(i,A({name:i,value:a,...s}));R(c,this._headers);return this}delete(...o){const[i,a]=typeof o[0]==="string"?[o[0]]:[o[0].name,o[0]];return this.set({...a,name:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(p).join("; ")}};function R(o,i){i.delete("set-cookie");for(const[,a]of o){const o=p(a);i.append("set-cookie",o)}}function A(o={name:"",value:""}){if(typeof o.expires==="number"){o.expires=new Date(o.expires)}if(o.maxAge){o.expires=new Date(Date.now()+o.maxAge*1e3)}if(o.path===null||o.path===void 0){o.path="/"}return o}0&&0},898:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{isNodeNextRequest:function(){return u},isNodeNextResponse:function(){return l},isWebNextRequest:function(){return s},isWebNextResponse:function(){return c}});const s=o=>"nodejs"==="edge";const c=o=>"nodejs"==="edge";const u=o=>"nodejs"!=="edge";const l=o=>"nodejs"!=="edge"},899:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"unauthorized",{enumerable:true,get:function(){return u}});const s=a(8704);const c=""+s.HTTP_ERROR_FALLBACK_ERROR_CODE+";401";function u(){if(true){throw Object.defineProperty(new Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:false,configurable:true})}const o=Object.defineProperty(new Error(c),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});o.digest=c;throw o}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},980:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{CachedRouteKind:function(){return s},IncrementalCacheKind:function(){return c}});var s=function(o){o["APP_PAGE"]="APP_PAGE";o["APP_ROUTE"]="APP_ROUTE";o["PAGES"]="PAGES";o["FETCH"]="FETCH";o["REDIRECT"]="REDIRECT";o["IMAGE"]="IMAGE";return o}({});var c=function(o){o["APP_PAGE"]="APP_PAGE";o["APP_ROUTE"]="APP_ROUTE";o["PAGES"]="PAGES";o["FETCH"]="FETCH";o["IMAGE"]="IMAGE";return o}({})},1042:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"unstable_rethrow",{enumerable:true,get:function(){return p}});const s=a(8388);const c=a(2637);const u=a(1846);const l=a(1162);const d=a(4971);const f=a(8479);function p(o){if((0,l.isNextRouterError)(o)||(0,u.isBailoutToCSRError)(o)||(0,f.isDynamicServerError)(o)||(0,d.isDynamicPostpone)(o)||(0,c.isPostpone)(o)||(0,s.isHangingPromiseRejectionError)(o)){throw o}if(o instanceof Error&&"cause"in o){p(o.cause)}}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},1076:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{getClientComponentLoaderMetrics:function(){return d},wrapClientComponentLoader:function(){return l}});let s=0;let c=0;let u=0;function l(o){if(!("performance"in globalThis)){return o.__next_app__}return{require:(...i)=>{const a=performance.now();if(s===0){s=a}try{u+=1;return o.__next_app__.require(...i)}finally{c+=performance.now()-a}},loadChunk:(...i)=>{const a=performance.now();const s=o.__next_app__.loadChunk(...i);s.finally(()=>{c+=performance.now()-a});return s}}}function d(o={}){const i=s===0?undefined:{clientComponentLoadStart:s,clientComponentLoadTimes:c,clientComponentLoadCount:u};if(o.reset){s=0;c=0;u=0}return i}},1120:(o,i,a)=>{"use strict";o.exports=a(5239).vendored["react-rsc"].React},1162:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"isNextRouterError",{enumerable:true,get:function(){return u}});const s=a(8704);const c=a(9026);function u(o){return(0,c.isRedirectError)(o)||(0,s.isHTTPAccessFallbackError)(o)}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},1243:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"URLPattern",{enumerable:true,get:function(){return a}});const a=typeof URLPattern==="undefined"?undefined:URLPattern},1289:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{BubbledError:function(){return m},SpanKind:function(){return y},SpanStatusCode:function(){return h},getTracer:function(){return P},isBubbledError:function(){return b}});const c=a(4823);const u=a(9098);let l;if(false){}else{try{l=a(2665)}catch(o){l=a(2665)}}const{context:d,propagation:f,trace:p,SpanStatusCode:h,SpanKind:y,ROOT_CONTEXT:g}=l;class m extends Error{constructor(o,i){super(),this.bubble=o,this.result=i}}function b(o){if(typeof o!=="object"||o===null)return false;return o instanceof m}const w=(o,i)=>{if(b(i)&&i.bubble){o.setAttribute("next.bubble",true)}else{if(i){o.recordException(i)}o.setStatus({code:h.ERROR,message:i==null?void 0:i.message})}o.end()};const _=new Map;const v=l.createContextKey("next.rootSpanId");let E=0;const S=()=>E++;const R={set(o,i,a){o.push({key:i,value:a})}};class A{getTracerInstance(){return p.getTracer("next.js","0.0.1")}getContext(){return d}getTracePropagationData(){const o=d.active();const i=[];f.inject(o,i,R);return i}getActiveScopeSpan(){return p.getSpan(d==null?void 0:d.active())}withPropagatedContext(o,i,a){const s=d.active();if(p.getSpanContext(s)){return i()}const c=f.extract(s,o,a);return d.with(c,i)}trace(...o){var i;const[a,s,l]=o;const{fn:f,options:h}=typeof s==="function"?{fn:s,options:{}}:{fn:l,options:{...s}};const y=h.spanName??a;if(!c.NextVanillaSpanAllowlist.includes(a)&&process.env.NEXT_OTEL_VERBOSE!=="1"||h.hideSpan){return f()}let m=this.getSpanContext((h==null?void 0:h.parentSpan)??this.getActiveScopeSpan());let b=false;if(!m){m=(d==null?void 0:d.active())??g;b=true}else if((i=p.getSpanContext(m))==null?void 0:i.isRemote){b=true}const E=S();h.attributes={"next.span_name":y,"next.span_type":a,...h.attributes};return d.with(m.setValue(v,E),()=>this.getTracerInstance().startActiveSpan(y,h,o=>{const i="performance"in globalThis&&"measure"in performance?globalThis.performance.now():undefined;const s=()=>{_.delete(E);if(i&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&c.LogSpanAllowList.includes(a||"")){performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(a.split(".").pop()||"").replace(/[A-Z]/g,o=>"-"+o.toLowerCase())}`,{start:i,end:performance.now()})}};if(b){_.set(E,new Map(Object.entries(h.attributes??{})))}try{if(f.length>1){return f(o,i=>w(o,i))}const i=f(o);if((0,u.isThenable)(i)){return i.then(i=>{o.end();return i}).catch(i=>{w(o,i);throw i}).finally(s)}else{o.end();s()}return i}catch(i){w(o,i);s();throw i}}))}wrap(...o){const i=this;const[a,s,u]=o.length===3?o:[o[0],{},o[1]];if(!c.NextVanillaSpanAllowlist.includes(a)&&process.env.NEXT_OTEL_VERBOSE!=="1"){return u}return function(){let o=s;if(typeof o==="function"&&typeof u==="function"){o=o.apply(this,arguments)}const c=arguments.length-1;const l=arguments[c];if(typeof l==="function"){const s=i.getContext().bind(d.active(),l);return i.trace(a,o,(o,i)=>{arguments[c]=function(o){i==null?void 0:i(o);return s.apply(this,arguments)};return u.apply(this,arguments)})}else{return i.trace(a,o,()=>u.apply(this,arguments))}}}startSpan(...o){const[i,a]=o;const s=this.getSpanContext((a==null?void 0:a.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(i,a,s)}getSpanContext(o){const i=o?p.setSpan(d.active(),o):undefined;return i}getRootSpanAttributes(){const o=d.active().getValue(v);return _.get(o)}setRootSpanAttribute(o,i){const a=d.active().getValue(v);const s=_.get(a);if(s){s.set(o,i)}}}const P=(()=>{const o=new A;return()=>o})()},1314:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"getHostname",{enumerable:true,get:function(){return a}});function a(o,i){let a;if((i==null?void 0:i.host)&&!Array.isArray(i.host)){a=i.host.toString().split(":",1)[0]}else if(o.hostname){a=o.hostname}else return;return a.toLowerCase()}},1617:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"InvariantError",{enumerable:true,get:function(){return a}});class a extends Error{constructor(o,i){super("Invariant: "+(o.endsWith(".")?o:o+".")+" This is a bug in Next.js.",i);this.name="InvariantError"}}},1846:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{BailoutToCSRError:function(){return c},isBailoutToCSRError:function(){return u}});const s="BAILOUT_TO_CLIENT_SIDE_RENDERING";class c extends Error{constructor(o){super("Bail out to client-side rendering: "+o),this.reason=o,this.digest=s}}function u(o){if(typeof o!=="object"||o===null||!("digest"in o)){return false}return o.digest===s}},1856:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{fromResponseCacheEntry:function(){return f},routeKindToIncrementalCacheKind:function(){return h},toResponseCacheEntry:function(){return p}});const c=a(980);const u=d(a(7778));const l=a(8088);function d(o){return o&&o.__esModule?o:{default:o}}async function f(o){var i,a;return{...o,value:((i=o.value)==null?void 0:i.kind)===c.CachedRouteKind.PAGES?{kind:c.CachedRouteKind.PAGES,html:await o.value.html.toUnchunkedString(true),pageData:o.value.pageData,headers:o.value.headers,status:o.value.status}:((a=o.value)==null?void 0:a.kind)===c.CachedRouteKind.APP_PAGE?{kind:c.CachedRouteKind.APP_PAGE,html:await o.value.html.toUnchunkedString(true),postponed:o.value.postponed,rscData:o.value.rscData,headers:o.value.headers,status:o.value.status,segmentData:o.value.segmentData}:o.value}}async function p(o){var i,a;if(!o)return null;return{isMiss:o.isMiss,isStale:o.isStale,cacheControl:o.cacheControl,isFallback:o.isFallback,value:((i=o.value)==null?void 0:i.kind)===c.CachedRouteKind.PAGES?{kind:c.CachedRouteKind.PAGES,html:u.default.fromStatic(o.value.html),pageData:o.value.pageData,headers:o.value.headers,status:o.value.status}:((a=o.value)==null?void 0:a.kind)===c.CachedRouteKind.APP_PAGE?{kind:c.CachedRouteKind.APP_PAGE,html:u.default.fromStatic(o.value.html),rscData:o.value.rscData,headers:o.value.headers,status:o.value.status,postponed:o.value.postponed,segmentData:o.value.segmentData}:o.value}}function h(o){switch(o){case l.RouteKind.PAGES:return c.IncrementalCacheKind.PAGES;case l.RouteKind.APP_PAGE:return c.IncrementalCacheKind.APP_PAGE;case l.RouteKind.IMAGE:return c.IncrementalCacheKind.IMAGE;case l.RouteKind.APP_ROUTE:return c.IncrementalCacheKind.APP_ROUTE;default:throw Object.defineProperty(new Error(`Unexpected route kind ${o}`),"__NEXT_ERROR_CODE",{value:"E64",enumerable:false,configurable:true})}}},1959:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"normalizeLocalePath",{enumerable:true,get:function(){return s}});const a=new WeakMap;function s(o,i){if(!i)return{pathname:o};let s=a.get(i);if(!s){s=i.map(o=>o.toLowerCase());a.set(i,s)}let c;const u=o.split("/",2);if(!u[1])return{pathname:o};const l=u[1].toLowerCase();const d=s.indexOf(l);if(d<0)return{pathname:o};c=i[d];o=o.slice(c.length+1)||"/";return{pathname:o,detectedLocale:c}}},2079:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"unstable_rootParams",{enumerable:true,get:function(){return h}});const s=a(1617);const c=a(4971);const u=a(9294);const l=a(3033);const d=a(8388);const f=a(2609);const p=new WeakMap;async function h(){const o=u.workAsyncStorage.getStore();if(!o){throw Object.defineProperty(new s.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:false,configurable:true})}const i=l.workUnitAsyncStorage.getStore();if(!i){throw Object.defineProperty(new Error(`Route ${o.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:false,configurable:true})}switch(i.type){case"unstable-cache":case"cache":{throw Object.defineProperty(new Error(`Route ${o.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:false,configurable:true})}case"prerender":case"prerender-ppr":case"prerender-legacy":return y(i.rootParams,o,i);default:return Promise.resolve(i.rootParams)}}function y(o,i,a){const s=i.fallbackRouteParams;if(s){let c=false;for(const i in o){if(s.has(i)){c=true;break}}if(c){if(a.type==="prerender"){const i=p.get(o);if(i){return i}const s=(0,d.makeHangingPromise)(a.renderSignal,"`unstable_rootParams`");p.set(o,s);return s}return g(o,s,i,a)}}return Promise.resolve(o)}function g(o,i,a,s){const u=p.get(o);if(u){return u}const l={...o};const d=Promise.resolve(l);p.set(o,d);Object.keys(o).forEach(u=>{if(f.wellKnownProperties.has(u)){}else{if(i.has(u)){Object.defineProperty(l,u,{get(){const o=(0,f.describeStringPropertyAccess)("unstable_rootParams",u);if(s.type==="prerender-ppr"){(0,c.postponeWithTracking)(a.route,o,s.dynamicTracking)}else{(0,c.throwToInterruptStaticGeneration)(o,a,s)}},enumerable:true})}else{;d[u]=o[u]}}});return d}},2174:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"ImageResponse",{enumerable:true,get:function(){return a}});function a(){throw Object.defineProperty(new Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:false,configurable:true})}},2471:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{isAbortError:function(){return p},pipeToNodeResponse:function(){return y}});const c=a(9893);const u=a(366);const l=a(1289);const d=a(4823);const f=a(1076);function p(o){return(o==null?void 0:o.name)==="AbortError"||(o==null?void 0:o.name)===c.ResponseAbortedName}function h(o,i){let a=false;let s=new u.DetachedPromise;function c(){s.resolve()}o.on("drain",c);o.once("close",()=>{o.off("drain",c);s.resolve()});const p=new u.DetachedPromise;o.once("finish",()=>{p.resolve()});return new WritableStream({write:async i=>{if(!a){a=true;if("performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){const o=(0,f.getClientComponentLoaderMetrics)();if(o){performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:o.clientComponentLoadStart,end:o.clientComponentLoadStart+o.clientComponentLoadTimes})}}o.flushHeaders();(0,l.getTracer)().trace(d.NextNodeServerSpan.startResponse,{spanName:"start response"},()=>undefined)}try{const a=o.write(i);if("flush"in o&&typeof o.flush==="function"){o.flush()}if(!a){await s.promise;s=new u.DetachedPromise}}catch(i){o.end();throw Object.defineProperty(new Error("failed to write chunk to response",{cause:i}),"__NEXT_ERROR_CODE",{value:"E321",enumerable:false,configurable:true})}},abort:i=>{if(o.writableFinished)return;o.destroy(i)},close:async()=>{if(i){await i}if(o.writableFinished)return;o.end();return p.promise}})}async function y(o,i,a){try{const{errored:s,destroyed:u}=i;if(s||u)return;const l=(0,c.createAbortController)(i);const d=h(i,a);await o.pipeTo(d,{signal:l.signal})}catch(o){if(p(o))return;throw Object.defineProperty(new Error("failed to pipe response",{cause:o}),"__NEXT_ERROR_CODE",{value:"E180",enumerable:false,configurable:true})}}},2584:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{HeadersAdapter:function(){return l},ReadonlyHeadersError:function(){return u}});const c=a(3763);class u extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new u}}class l extends Headers{constructor(o){super();this.headers=new Proxy(o,{get(i,a,s){if(typeof a==="symbol"){return c.ReflectAdapter.get(i,a,s)}const u=a.toLowerCase();const l=Object.keys(o).find(o=>o.toLowerCase()===u);if(typeof l==="undefined")return;return c.ReflectAdapter.get(i,l,s)},set(i,a,s,u){if(typeof a==="symbol"){return c.ReflectAdapter.set(i,a,s,u)}const l=a.toLowerCase();const d=Object.keys(o).find(o=>o.toLowerCase()===l);return c.ReflectAdapter.set(i,d??a,s,u)},has(i,a){if(typeof a==="symbol")return c.ReflectAdapter.has(i,a);const s=a.toLowerCase();const u=Object.keys(o).find(o=>o.toLowerCase()===s);if(typeof u==="undefined")return false;return c.ReflectAdapter.has(i,u)},deleteProperty(i,a){if(typeof a==="symbol")return c.ReflectAdapter.deleteProperty(i,a);const s=a.toLowerCase();const u=Object.keys(o).find(o=>o.toLowerCase()===s);if(typeof u==="undefined")return true;return c.ReflectAdapter.deleteProperty(i,u)}})}static seal(o){return new Proxy(o,{get(o,i,a){switch(i){case"append":case"delete":case"set":return u.callable;default:return c.ReflectAdapter.get(o,i,a)}}})}merge(o){if(Array.isArray(o))return o.join(", ");return o}static from(o){if(o instanceof Headers)return o;return new l(o)}append(o,i){const a=this.headers[o];if(typeof a==="string"){this.headers[o]=[a,i]}else if(Array.isArray(a)){a.push(i)}else{this.headers[o]=i}}delete(o){delete this.headers[o]}get(o){const i=this.headers[o];if(typeof i!=="undefined")return this.merge(i);return null}has(o){return typeof this.headers[o]!=="undefined"}set(o,i){this.headers[o]=i}forEach(o,i){for(const[a,s]of this.entries()){o.call(i,s,a,this)}}*entries(){for(const o of Object.keys(this.headers)){const i=o.toLowerCase();const a=this.get(i);yield[i,a]}}*keys(){for(const o of Object.keys(this.headers)){const i=o.toLowerCase();yield i}}*values(){for(const o of Object.keys(this.headers)){const i=this.get(o);yield i}}[Symbol.iterator](){return this.entries()}}},2609:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{describeHasCheckingStringProperty:function(){return u},describeStringPropertyAccess:function(){return c},wellKnownProperties:function(){return l}});const s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function c(o,i){if(s.test(i)){return"`"+o+"."+i+"`"}return"`"+o+"["+JSON.stringify(i)+"]`"}function u(o,i){const a=JSON.stringify(i);return"`Reflect.has("+o+", "+a+")`, `"+a+" in "+o+"`, or similar"}const l=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},2637:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"isPostpone",{enumerable:true,get:function(){return s}});const a=Symbol.for("react.postpone");function s(o){return typeof o==="object"&&o!==null&&o.$$typeof===a}},2665:o=>{(()=>{"use strict";var i={491:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.ContextAPI=void 0;const s=a(223);const c=a(172);const u=a(930);const l="context";const d=new s.NoopContextManager;class f{constructor(){}static getInstance(){if(!this._instance){this._instance=new f}return this._instance}setGlobalContextManager(o){return(0,c.registerGlobal)(l,o,u.DiagAPI.instance())}active(){return this._getContextManager().active()}with(o,i,a,...s){return this._getContextManager().with(o,i,a,...s)}bind(o,i){return this._getContextManager().bind(o,i)}_getContextManager(){return(0,c.getGlobal)(l)||d}disable(){this._getContextManager().disable();(0,c.unregisterGlobal)(l,u.DiagAPI.instance())}}i.ContextAPI=f},930:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagAPI=void 0;const s=a(56);const c=a(912);const u=a(957);const l=a(172);const d="diag";class f{constructor(){function o(o){return function(...i){const a=(0,l.getGlobal)("diag");if(!a)return;return a[o](...i)}}const i=this;const a=(o,a={logLevel:u.DiagLogLevel.INFO})=>{var s,d,f;if(o===i){const o=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");i.error((s=o.stack)!==null&&s!==void 0?s:o.message);return false}if(typeof a==="number"){a={logLevel:a}}const p=(0,l.getGlobal)("diag");const h=(0,c.createLogLevelDiagLogger)((d=a.logLevel)!==null&&d!==void 0?d:u.DiagLogLevel.INFO,o);if(p&&!a.suppressOverrideMessage){const o=(f=(new Error).stack)!==null&&f!==void 0?f:"<failed to generate stacktrace>";p.warn(`Current logger will be overwritten from ${o}`);h.warn(`Current logger will overwrite one already registered from ${o}`)}return(0,l.registerGlobal)("diag",h,i,true)};i.setLogger=a;i.disable=()=>{(0,l.unregisterGlobal)(d,i)};i.createComponentLogger=o=>new s.DiagComponentLogger(o);i.verbose=o("verbose");i.debug=o("debug");i.info=o("info");i.warn=o("warn");i.error=o("error")}static instance(){if(!this._instance){this._instance=new f}return this._instance}}i.DiagAPI=f},653:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.MetricsAPI=void 0;const s=a(660);const c=a(172);const u=a(930);const l="metrics";class d{constructor(){}static getInstance(){if(!this._instance){this._instance=new d}return this._instance}setGlobalMeterProvider(o){return(0,c.registerGlobal)(l,o,u.DiagAPI.instance())}getMeterProvider(){return(0,c.getGlobal)(l)||s.NOOP_METER_PROVIDER}getMeter(o,i,a){return this.getMeterProvider().getMeter(o,i,a)}disable(){(0,c.unregisterGlobal)(l,u.DiagAPI.instance())}}i.MetricsAPI=d},181:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.PropagationAPI=void 0;const s=a(172);const c=a(874);const u=a(194);const l=a(277);const d=a(369);const f=a(930);const p="propagation";const h=new c.NoopTextMapPropagator;class y{constructor(){this.createBaggage=d.createBaggage;this.getBaggage=l.getBaggage;this.getActiveBaggage=l.getActiveBaggage;this.setBaggage=l.setBaggage;this.deleteBaggage=l.deleteBaggage}static getInstance(){if(!this._instance){this._instance=new y}return this._instance}setGlobalPropagator(o){return(0,s.registerGlobal)(p,o,f.DiagAPI.instance())}inject(o,i,a=u.defaultTextMapSetter){return this._getGlobalPropagator().inject(o,i,a)}extract(o,i,a=u.defaultTextMapGetter){return this._getGlobalPropagator().extract(o,i,a)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,s.unregisterGlobal)(p,f.DiagAPI.instance())}_getGlobalPropagator(){return(0,s.getGlobal)(p)||h}}i.PropagationAPI=y},997:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.TraceAPI=void 0;const s=a(172);const c=a(846);const u=a(139);const l=a(607);const d=a(930);const f="trace";class p{constructor(){this._proxyTracerProvider=new c.ProxyTracerProvider;this.wrapSpanContext=u.wrapSpanContext;this.isSpanContextValid=u.isSpanContextValid;this.deleteSpan=l.deleteSpan;this.getSpan=l.getSpan;this.getActiveSpan=l.getActiveSpan;this.getSpanContext=l.getSpanContext;this.setSpan=l.setSpan;this.setSpanContext=l.setSpanContext}static getInstance(){if(!this._instance){this._instance=new p}return this._instance}setGlobalTracerProvider(o){const i=(0,s.registerGlobal)(f,this._proxyTracerProvider,d.DiagAPI.instance());if(i){this._proxyTracerProvider.setDelegate(o)}return i}getTracerProvider(){return(0,s.getGlobal)(f)||this._proxyTracerProvider}getTracer(o,i){return this.getTracerProvider().getTracer(o,i)}disable(){(0,s.unregisterGlobal)(f,d.DiagAPI.instance());this._proxyTracerProvider=new c.ProxyTracerProvider}}i.TraceAPI=p},277:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.deleteBaggage=i.setBaggage=i.getActiveBaggage=i.getBaggage=void 0;const s=a(491);const c=a(780);const u=(0,c.createContextKey)("OpenTelemetry Baggage Key");function l(o){return o.getValue(u)||undefined}i.getBaggage=l;function d(){return l(s.ContextAPI.getInstance().active())}i.getActiveBaggage=d;function f(o,i){return o.setValue(u,i)}i.setBaggage=f;function p(o){return o.deleteValue(u)}i.deleteBaggage=p},993:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.BaggageImpl=void 0;class a{constructor(o){this._entries=o?new Map(o):new Map}getEntry(o){const i=this._entries.get(o);if(!i){return undefined}return Object.assign({},i)}getAllEntries(){return Array.from(this._entries.entries()).map(([o,i])=>[o,i])}setEntry(o,i){const s=new a(this._entries);s._entries.set(o,i);return s}removeEntry(o){const i=new a(this._entries);i._entries.delete(o);return i}removeEntries(...o){const i=new a(this._entries);for(const a of o){i._entries.delete(a)}return i}clear(){return new a}}i.BaggageImpl=a},830:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.baggageEntryMetadataSymbol=void 0;i.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.baggageEntryMetadataFromString=i.createBaggage=void 0;const s=a(930);const c=a(993);const u=a(830);const l=s.DiagAPI.instance();function d(o={}){return new c.BaggageImpl(new Map(Object.entries(o)))}i.createBaggage=d;function f(o){if(typeof o!=="string"){l.error(`Cannot create baggage metadata from unknown type: ${typeof o}`);o=""}return{__TYPE__:u.baggageEntryMetadataSymbol,toString(){return o}}}i.baggageEntryMetadataFromString=f},67:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.context=void 0;const s=a(491);i.context=s.ContextAPI.getInstance()},223:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopContextManager=void 0;const s=a(780);class c{active(){return s.ROOT_CONTEXT}with(o,i,a,...s){return i.call(a,...s)}bind(o,i){return i}enable(){return this}disable(){return this}}i.NoopContextManager=c},780:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.ROOT_CONTEXT=i.createContextKey=void 0;function a(o){return Symbol.for(o)}i.createContextKey=a;class s{constructor(o){const i=this;i._currentContext=o?new Map(o):new Map;i.getValue=o=>i._currentContext.get(o);i.setValue=(o,a)=>{const c=new s(i._currentContext);c._currentContext.set(o,a);return c};i.deleteValue=o=>{const a=new s(i._currentContext);a._currentContext.delete(o);return a}}}i.ROOT_CONTEXT=new s},506:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.diag=void 0;const s=a(930);i.diag=s.DiagAPI.instance()},56:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagComponentLogger=void 0;const s=a(172);class c{constructor(o){this._namespace=o.namespace||"DiagComponentLogger"}debug(...o){return u("debug",this._namespace,o)}error(...o){return u("error",this._namespace,o)}info(...o){return u("info",this._namespace,o)}warn(...o){return u("warn",this._namespace,o)}verbose(...o){return u("verbose",this._namespace,o)}}i.DiagComponentLogger=c;function u(o,i,a){const c=(0,s.getGlobal)("diag");if(!c){return}a.unshift(i);return c[o](...a)}},972:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagConsoleLogger=void 0;const a=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class s{constructor(){function o(o){return function(...i){if(console){let a=console[o];if(typeof a!=="function"){a=console.log}if(typeof a==="function"){return a.apply(console,i)}}}}for(let i=0;i<a.length;i++){this[a[i].n]=o(a[i].c)}}}i.DiagConsoleLogger=s},912:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.createLogLevelDiagLogger=void 0;const s=a(957);function c(o,i){if(o<s.DiagLogLevel.NONE){o=s.DiagLogLevel.NONE}else if(o>s.DiagLogLevel.ALL){o=s.DiagLogLevel.ALL}i=i||{};function a(a,s){const c=i[a];if(typeof c==="function"&&o>=s){return c.bind(i)}return function(){}}return{error:a("error",s.DiagLogLevel.ERROR),warn:a("warn",s.DiagLogLevel.WARN),info:a("info",s.DiagLogLevel.INFO),debug:a("debug",s.DiagLogLevel.DEBUG),verbose:a("verbose",s.DiagLogLevel.VERBOSE)}}i.createLogLevelDiagLogger=c},957:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagLogLevel=void 0;var a;(function(o){o[o["NONE"]=0]="NONE";o[o["ERROR"]=30]="ERROR";o[o["WARN"]=50]="WARN";o[o["INFO"]=60]="INFO";o[o["DEBUG"]=70]="DEBUG";o[o["VERBOSE"]=80]="VERBOSE";o[o["ALL"]=9999]="ALL"})(a=i.DiagLogLevel||(i.DiagLogLevel={}))},172:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.unregisterGlobal=i.getGlobal=i.registerGlobal=void 0;const s=a(200);const c=a(521);const u=a(130);const l=c.VERSION.split(".")[0];const d=Symbol.for(`opentelemetry.js.api.${l}`);const f=s._globalThis;function p(o,i,a,s=false){var u;const l=f[d]=(u=f[d])!==null&&u!==void 0?u:{version:c.VERSION};if(!s&&l[o]){const i=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${o}`);a.error(i.stack||i.message);return false}if(l.version!==c.VERSION){const i=new Error(`@opentelemetry/api: Registration of version v${l.version} for ${o} does not match previously registered API v${c.VERSION}`);a.error(i.stack||i.message);return false}l[o]=i;a.debug(`@opentelemetry/api: Registered a global for ${o} v${c.VERSION}.`);return true}i.registerGlobal=p;function h(o){var i,a;const s=(i=f[d])===null||i===void 0?void 0:i.version;if(!s||!(0,u.isCompatible)(s)){return}return(a=f[d])===null||a===void 0?void 0:a[o]}i.getGlobal=h;function y(o,i){i.debug(`@opentelemetry/api: Unregistering a global for ${o} v${c.VERSION}.`);const a=f[d];if(a){delete a[o]}}i.unregisterGlobal=y},130:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.isCompatible=i._makeCompatibilityCheck=void 0;const s=a(521);const c=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function u(o){const i=new Set([o]);const a=new Set;const s=o.match(c);if(!s){return()=>false}const u={major:+s[1],minor:+s[2],patch:+s[3],prerelease:s[4]};if(u.prerelease!=null){return function i(i){return i===o}}function l(o){a.add(o);return false}function d(o){i.add(o);return true}return function o(o){if(i.has(o)){return true}if(a.has(o)){return false}const s=o.match(c);if(!s){return l(o)}const f={major:+s[1],minor:+s[2],patch:+s[3],prerelease:s[4]};if(f.prerelease!=null){return l(o)}if(u.major!==f.major){return l(o)}if(u.major===0){if(u.minor===f.minor&&u.patch<=f.patch){return d(o)}return l(o)}if(u.minor<=f.minor){return d(o)}return l(o)}}i._makeCompatibilityCheck=u;i.isCompatible=u(s.VERSION)},886:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.metrics=void 0;const s=a(653);i.metrics=s.MetricsAPI.getInstance()},901:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.ValueType=void 0;var a;(function(o){o[o["INT"]=0]="INT";o[o["DOUBLE"]=1]="DOUBLE"})(a=i.ValueType||(i.ValueType={}))},102:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.createNoopMeter=i.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=i.NOOP_OBSERVABLE_GAUGE_METRIC=i.NOOP_OBSERVABLE_COUNTER_METRIC=i.NOOP_UP_DOWN_COUNTER_METRIC=i.NOOP_HISTOGRAM_METRIC=i.NOOP_COUNTER_METRIC=i.NOOP_METER=i.NoopObservableUpDownCounterMetric=i.NoopObservableGaugeMetric=i.NoopObservableCounterMetric=i.NoopObservableMetric=i.NoopHistogramMetric=i.NoopUpDownCounterMetric=i.NoopCounterMetric=i.NoopMetric=i.NoopMeter=void 0;class a{constructor(){}createHistogram(o,a){return i.NOOP_HISTOGRAM_METRIC}createCounter(o,a){return i.NOOP_COUNTER_METRIC}createUpDownCounter(o,a){return i.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(o,a){return i.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(o,a){return i.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(o,a){return i.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(o,i){}removeBatchObservableCallback(o){}}i.NoopMeter=a;class s{}i.NoopMetric=s;class c extends s{add(o,i){}}i.NoopCounterMetric=c;class u extends s{add(o,i){}}i.NoopUpDownCounterMetric=u;class l extends s{record(o,i){}}i.NoopHistogramMetric=l;class d{addCallback(o){}removeCallback(o){}}i.NoopObservableMetric=d;class f extends d{}i.NoopObservableCounterMetric=f;class p extends d{}i.NoopObservableGaugeMetric=p;class h extends d{}i.NoopObservableUpDownCounterMetric=h;i.NOOP_METER=new a;i.NOOP_COUNTER_METRIC=new c;i.NOOP_HISTOGRAM_METRIC=new l;i.NOOP_UP_DOWN_COUNTER_METRIC=new u;i.NOOP_OBSERVABLE_COUNTER_METRIC=new f;i.NOOP_OBSERVABLE_GAUGE_METRIC=new p;i.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new h;function y(){return i.NOOP_METER}i.createNoopMeter=y},660:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.NOOP_METER_PROVIDER=i.NoopMeterProvider=void 0;const s=a(102);class c{getMeter(o,i,a){return s.NOOP_METER}}i.NoopMeterProvider=c;i.NOOP_METER_PROVIDER=new c},200:function(o,i,a){var s=this&&this.__createBinding||(Object.create?function(o,i,a,s){if(s===undefined)s=a;Object.defineProperty(o,s,{enumerable:true,get:function(){return i[a]}})}:function(o,i,a,s){if(s===undefined)s=a;o[s]=i[a]});var c=this&&this.__exportStar||function(o,i){for(var a in o)if(a!=="default"&&!Object.prototype.hasOwnProperty.call(i,a))s(i,o,a)};Object.defineProperty(i,"__esModule",{value:true});c(a(46),i)},651:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i._globalThis=void 0;i._globalThis=typeof globalThis==="object"?globalThis:global},46:function(o,i,a){var s=this&&this.__createBinding||(Object.create?function(o,i,a,s){if(s===undefined)s=a;Object.defineProperty(o,s,{enumerable:true,get:function(){return i[a]}})}:function(o,i,a,s){if(s===undefined)s=a;o[s]=i[a]});var c=this&&this.__exportStar||function(o,i){for(var a in o)if(a!=="default"&&!Object.prototype.hasOwnProperty.call(i,a))s(i,o,a)};Object.defineProperty(i,"__esModule",{value:true});c(a(651),i)},939:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.propagation=void 0;const s=a(181);i.propagation=s.PropagationAPI.getInstance()},874:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopTextMapPropagator=void 0;class a{inject(o,i){}extract(o,i){return o}fields(){return[]}}i.NoopTextMapPropagator=a},194:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.defaultTextMapSetter=i.defaultTextMapGetter=void 0;i.defaultTextMapGetter={get(o,i){if(o==null){return undefined}return o[i]},keys(o){if(o==null){return[]}return Object.keys(o)}};i.defaultTextMapSetter={set(o,i,a){if(o==null){return}o[i]=a}}},845:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.trace=void 0;const s=a(997);i.trace=s.TraceAPI.getInstance()},403:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.NonRecordingSpan=void 0;const s=a(476);class c{constructor(o=s.INVALID_SPAN_CONTEXT){this._spanContext=o}spanContext(){return this._spanContext}setAttribute(o,i){return this}setAttributes(o){return this}addEvent(o,i){return this}setStatus(o){return this}updateName(o){return this}end(o){}isRecording(){return false}recordException(o,i){}}i.NonRecordingSpan=c},614:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopTracer=void 0;const s=a(491);const c=a(607);const u=a(403);const l=a(139);const d=s.ContextAPI.getInstance();class f{startSpan(o,i,a=d.active()){const s=Boolean(i===null||i===void 0?void 0:i.root);if(s){return new u.NonRecordingSpan}const f=a&&(0,c.getSpanContext)(a);if(p(f)&&(0,l.isSpanContextValid)(f)){return new u.NonRecordingSpan(f)}else{return new u.NonRecordingSpan}}startActiveSpan(o,i,a,s){let u;let l;let f;if(arguments.length<2){return}else if(arguments.length===2){f=i}else if(arguments.length===3){u=i;f=a}else{u=i;l=a;f=s}const p=l!==null&&l!==void 0?l:d.active();const h=this.startSpan(o,u,p);const y=(0,c.setSpan)(p,h);return d.with(y,f,undefined,h)}}i.NoopTracer=f;function p(o){return typeof o==="object"&&typeof o["spanId"]==="string"&&typeof o["traceId"]==="string"&&typeof o["traceFlags"]==="number"}},124:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopTracerProvider=void 0;const s=a(614);class c{getTracer(o,i,a){return new s.NoopTracer}}i.NoopTracerProvider=c},125:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.ProxyTracer=void 0;const s=a(614);const c=new s.NoopTracer;class u{constructor(o,i,a,s){this._provider=o;this.name=i;this.version=a;this.options=s}startSpan(o,i,a){return this._getTracer().startSpan(o,i,a)}startActiveSpan(o,i,a,s){const c=this._getTracer();return Reflect.apply(c.startActiveSpan,c,arguments)}_getTracer(){if(this._delegate){return this._delegate}const o=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!o){return c}this._delegate=o;return this._delegate}}i.ProxyTracer=u},846:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.ProxyTracerProvider=void 0;const s=a(125);const c=a(124);const u=new c.NoopTracerProvider;class l{getTracer(o,i,a){var c;return(c=this.getDelegateTracer(o,i,a))!==null&&c!==void 0?c:new s.ProxyTracer(this,o,i,a)}getDelegate(){var o;return(o=this._delegate)!==null&&o!==void 0?o:u}setDelegate(o){this._delegate=o}getDelegateTracer(o,i,a){var s;return(s=this._delegate)===null||s===void 0?void 0:s.getTracer(o,i,a)}}i.ProxyTracerProvider=l},996:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.SamplingDecision=void 0;var a;(function(o){o[o["NOT_RECORD"]=0]="NOT_RECORD";o[o["RECORD"]=1]="RECORD";o[o["RECORD_AND_SAMPLED"]=2]="RECORD_AND_SAMPLED"})(a=i.SamplingDecision||(i.SamplingDecision={}))},607:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.getSpanContext=i.setSpanContext=i.deleteSpan=i.setSpan=i.getActiveSpan=i.getSpan=void 0;const s=a(780);const c=a(403);const u=a(491);const l=(0,s.createContextKey)("OpenTelemetry Context Key SPAN");function d(o){return o.getValue(l)||undefined}i.getSpan=d;function f(){return d(u.ContextAPI.getInstance().active())}i.getActiveSpan=f;function p(o,i){return o.setValue(l,i)}i.setSpan=p;function h(o){return o.deleteValue(l)}i.deleteSpan=h;function y(o,i){return p(o,new c.NonRecordingSpan(i))}i.setSpanContext=y;function g(o){var i;return(i=d(o))===null||i===void 0?void 0:i.spanContext()}i.getSpanContext=g},325:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.TraceStateImpl=void 0;const s=a(564);const c=32;const u=512;const l=",";const d="=";class f{constructor(o){this._internalState=new Map;if(o)this._parse(o)}set(o,i){const a=this._clone();if(a._internalState.has(o)){a._internalState.delete(o)}a._internalState.set(o,i);return a}unset(o){const i=this._clone();i._internalState.delete(o);return i}get(o){return this._internalState.get(o)}serialize(){return this._keys().reduce((o,i)=>{o.push(i+d+this.get(i));return o},[]).join(l)}_parse(o){if(o.length>u)return;this._internalState=o.split(l).reverse().reduce((o,i)=>{const a=i.trim();const c=a.indexOf(d);if(c!==-1){const u=a.slice(0,c);const l=a.slice(c+1,i.length);if((0,s.validateKey)(u)&&(0,s.validateValue)(l)){o.set(u,l)}else{}}return o},new Map);if(this._internalState.size>c){this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,c))}}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){const o=new f;o._internalState=new Map(this._internalState);return o}}i.TraceStateImpl=f},564:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.validateValue=i.validateKey=void 0;const a="[_0-9a-z-*/]";const s=`[a-z]${a}{0,255}`;const c=`[a-z0-9]${a}{0,240}@[a-z]${a}{0,13}`;const u=new RegExp(`^(?:${s}|${c})$`);const l=/^[ -~]{0,255}[!-~]$/;const d=/,|=/;function f(o){return u.test(o)}i.validateKey=f;function p(o){return l.test(o)&&!d.test(o)}i.validateValue=p},98:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.createTraceState=void 0;const s=a(325);function c(o){return new s.TraceStateImpl(o)}i.createTraceState=c},476:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.INVALID_SPAN_CONTEXT=i.INVALID_TRACEID=i.INVALID_SPANID=void 0;const s=a(475);i.INVALID_SPANID="0000000000000000";i.INVALID_TRACEID="00000000000000000000000000000000";i.INVALID_SPAN_CONTEXT={traceId:i.INVALID_TRACEID,spanId:i.INVALID_SPANID,traceFlags:s.TraceFlags.NONE}},357:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.SpanKind=void 0;var a;(function(o){o[o["INTERNAL"]=0]="INTERNAL";o[o["SERVER"]=1]="SERVER";o[o["CLIENT"]=2]="CLIENT";o[o["PRODUCER"]=3]="PRODUCER";o[o["CONSUMER"]=4]="CONSUMER"})(a=i.SpanKind||(i.SpanKind={}))},139:(o,i,a)=>{Object.defineProperty(i,"__esModule",{value:true});i.wrapSpanContext=i.isSpanContextValid=i.isValidSpanId=i.isValidTraceId=void 0;const s=a(476);const c=a(403);const u=/^([0-9a-f]{32})$/i;const l=/^[0-9a-f]{16}$/i;function d(o){return u.test(o)&&o!==s.INVALID_TRACEID}i.isValidTraceId=d;function f(o){return l.test(o)&&o!==s.INVALID_SPANID}i.isValidSpanId=f;function p(o){return d(o.traceId)&&f(o.spanId)}i.isSpanContextValid=p;function h(o){return new c.NonRecordingSpan(o)}i.wrapSpanContext=h},847:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.SpanStatusCode=void 0;var a;(function(o){o[o["UNSET"]=0]="UNSET";o[o["OK"]=1]="OK";o[o["ERROR"]=2]="ERROR"})(a=i.SpanStatusCode||(i.SpanStatusCode={}))},475:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.TraceFlags=void 0;var a;(function(o){o[o["NONE"]=0]="NONE";o[o["SAMPLED"]=1]="SAMPLED"})(a=i.TraceFlags||(i.TraceFlags={}))},521:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.VERSION=void 0;i.VERSION="1.6.0"}};var a={};function s(o){var c=a[o];if(c!==undefined){return c.exports}var u=a[o]={exports:{}};var l=true;try{i[o].call(u.exports,u,u.exports,s);l=false}finally{if(l)delete a[o]}return u.exports}if(typeof s!=="undefined")s.ab=__dirname+"/";var c={};(()=>{var o=c;Object.defineProperty(o,"__esModule",{value:true});o.trace=o.propagation=o.metrics=o.diag=o.context=o.INVALID_SPAN_CONTEXT=o.INVALID_TRACEID=o.INVALID_SPANID=o.isValidSpanId=o.isValidTraceId=o.isSpanContextValid=o.createTraceState=o.TraceFlags=o.SpanStatusCode=o.SpanKind=o.SamplingDecision=o.ProxyTracerProvider=o.ProxyTracer=o.defaultTextMapSetter=o.defaultTextMapGetter=o.ValueType=o.createNoopMeter=o.DiagLogLevel=o.DiagConsoleLogger=o.ROOT_CONTEXT=o.createContextKey=o.baggageEntryMetadataFromString=void 0;var i=s(369);Object.defineProperty(o,"baggageEntryMetadataFromString",{enumerable:true,get:function(){return i.baggageEntryMetadataFromString}});var a=s(780);Object.defineProperty(o,"createContextKey",{enumerable:true,get:function(){return a.createContextKey}});Object.defineProperty(o,"ROOT_CONTEXT",{enumerable:true,get:function(){return a.ROOT_CONTEXT}});var u=s(972);Object.defineProperty(o,"DiagConsoleLogger",{enumerable:true,get:function(){return u.DiagConsoleLogger}});var l=s(957);Object.defineProperty(o,"DiagLogLevel",{enumerable:true,get:function(){return l.DiagLogLevel}});var d=s(102);Object.defineProperty(o,"createNoopMeter",{enumerable:true,get:function(){return d.createNoopMeter}});var f=s(901);Object.defineProperty(o,"ValueType",{enumerable:true,get:function(){return f.ValueType}});var p=s(194);Object.defineProperty(o,"defaultTextMapGetter",{enumerable:true,get:function(){return p.defaultTextMapGetter}});Object.defineProperty(o,"defaultTextMapSetter",{enumerable:true,get:function(){return p.defaultTextMapSetter}});var h=s(125);Object.defineProperty(o,"ProxyTracer",{enumerable:true,get:function(){return h.ProxyTracer}});var y=s(846);Object.defineProperty(o,"ProxyTracerProvider",{enumerable:true,get:function(){return y.ProxyTracerProvider}});var g=s(996);Object.defineProperty(o,"SamplingDecision",{enumerable:true,get:function(){return g.SamplingDecision}});var m=s(357);Object.defineProperty(o,"SpanKind",{enumerable:true,get:function(){return m.SpanKind}});var b=s(847);Object.defineProperty(o,"SpanStatusCode",{enumerable:true,get:function(){return b.SpanStatusCode}});var w=s(475);Object.defineProperty(o,"TraceFlags",{enumerable:true,get:function(){return w.TraceFlags}});var _=s(98);Object.defineProperty(o,"createTraceState",{enumerable:true,get:function(){return _.createTraceState}});var v=s(139);Object.defineProperty(o,"isSpanContextValid",{enumerable:true,get:function(){return v.isSpanContextValid}});Object.defineProperty(o,"isValidTraceId",{enumerable:true,get:function(){return v.isValidTraceId}});Object.defineProperty(o,"isValidSpanId",{enumerable:true,get:function(){return v.isValidSpanId}});var E=s(476);Object.defineProperty(o,"INVALID_SPANID",{enumerable:true,get:function(){return E.INVALID_SPANID}});Object.defineProperty(o,"INVALID_TRACEID",{enumerable:true,get:function(){return E.INVALID_TRACEID}});Object.defineProperty(o,"INVALID_SPAN_CONTEXT",{enumerable:true,get:function(){return E.INVALID_SPAN_CONTEXT}});const S=s(67);Object.defineProperty(o,"context",{enumerable:true,get:function(){return S.context}});const R=s(506);Object.defineProperty(o,"diag",{enumerable:true,get:function(){return R.diag}});const A=s(886);Object.defineProperty(o,"metrics",{enumerable:true,get:function(){return A.metrics}});const P=s(939);Object.defineProperty(o,"propagation",{enumerable:true,get:function(){return P.propagation}});const k=s(845);Object.defineProperty(o,"trace",{enumerable:true,get:function(){return k.trace}});o["default"]={context:S.context,diag:R.diag,metrics:A.metrics,propagation:P.propagation,trace:k.trace}})();o.exports=c})()},2765:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"notFound",{enumerable:true,get:function(){return u}});const s=a(8704);const c=""+s.HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function u(){const o=Object.defineProperty(new Error(c),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});o.digest=c;throw o}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},2829:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"pathHasPrefix",{enumerable:true,get:function(){return c}});const s=a(8631);function c(o,i){if(typeof o!=="string"){return false}const{pathname:a}=(0,s.parsePath)(o);return a===i||a.startsWith(i+"/")}},2836:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"RedirectStatusCode",{enumerable:true,get:function(){return a}});var a=function(o){o[o["SeeOther"]=303]="SeeOther";o[o["TemporaryRedirect"]=307]="TemporaryRedirect";o[o["PermanentRedirect"]=308]="PermanentRedirect";return o}({});if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},2887:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"removeTrailingSlash",{enumerable:true,get:function(){return a}});function a(o){return o.replace(/\/$/,"")||"/"}},2944:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"connection",{enumerable:true,get:function(){return p}});const s=a(9294);const c=a(3033);const u=a(4971);const l=a(23);const d=a(8388);const f=a(8719);function p(){const o=s.workAsyncStorage.getStore();const i=c.workUnitAsyncStorage.getStore();if(o){if(i&&i.phase==="after"&&!(0,f.isRequestAPICallableInsideAfter)()){throw Object.defineProperty(new Error(`Route ${o.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:false,configurable:true})}if(o.forceStatic){return Promise.resolve(undefined)}if(i){if(i.type==="cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:false,configurable:true})}else if(i.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:false,configurable:true})}}if(o.dynamicShouldError){throw Object.defineProperty(new l.StaticGenBailoutError(`Route ${o.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:false,configurable:true})}if(i){if(i.type==="prerender"){return(0,d.makeHangingPromise)(i.renderSignal,"`connection()`")}else if(i.type==="prerender-ppr"){(0,u.postponeWithTracking)(o.route,"connection",i.dynamicTracking)}else if(i.type==="prerender-legacy"){(0,u.throwToInterruptStaticGeneration)("connection",o,i)}}(0,u.trackDynamicDataInDynamicRender)(o,i)}return Promise.resolve(undefined)}},3158:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{RequestCookies:function(){return c.RequestCookies},ResponseCookies:function(){return c.ResponseCookies},stringifyCookie:function(){return c.stringifyCookie}});const c=a(635)},3182:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{isBot:function(){return l},userAgent:function(){return f},userAgentFromString:function(){return d}});const c=u(a(397));function u(o){return o&&o.__esModule?o:{default:o}}function l(o){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(o)}function d(o){return{...(0,c.default)(o),isBot:o===undefined?false:l(o)}}function f({headers:o}){return d(o.get("user-agent")||undefined)}},3365:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"default",{enumerable:true,get:function(){return d}});0&&0;const s=a(8737);const c=a(4523);const u=a(1856);l(a(980),i);function l(o,i){Object.keys(o).forEach(function(a){if(a!=="default"&&!Object.prototype.hasOwnProperty.call(i,a)){Object.defineProperty(i,a,{enumerable:true,get:function(){return o[a]}})}});return o}class d{constructor(o){this.batcher=s.Batcher.create({cacheKeyFn:({key:o,isOnDemandRevalidate:i})=>`${o}-${i?"1":"0"}`,schedulerFn:c.scheduleOnNextTick});const i="minimalMode";this[i]=o}async get(o,i,a){if(!o){return i({hasResolved:false,previousCacheEntry:null})}const{incrementalCache:s,isOnDemandRevalidate:c=false,isFallback:l=false,isRoutePPREnabled:d=false}=a;const f=await this.batcher.batch({key:o,isOnDemandRevalidate:c},async(f,p)=>{var h;if(this.minimalMode&&((h=this.previousCacheItem)==null?void 0:h.key)===f&&this.previousCacheItem.expiresAt>Date.now()){return this.previousCacheItem.entry}const y=(0,u.routeKindToIncrementalCacheKind)(a.routeKind);let g=false;let m=null;try{m=!this.minimalMode?await s.get(o,{kind:y,isRoutePPREnabled:a.isRoutePPREnabled,isFallback:l}):null;if(m&&!c){p(m);g=true;if(!m.isStale||a.isPrefetch){return null}}const h=await i({hasResolved:g,previousCacheEntry:m,isRevalidating:true});if(!h){if(this.minimalMode)this.previousCacheItem=undefined;return null}const b=await (0,u.fromResponseCacheEntry)({...h,isMiss:!m});if(!b){if(this.minimalMode)this.previousCacheItem=undefined;return null}if(!c&&!g){p(b);g=true}if(b.cacheControl){if(this.minimalMode){this.previousCacheItem={key:f,entry:b,expiresAt:Date.now()+1e3}}else{await s.set(o,b.value,{cacheControl:b.cacheControl,isRoutePPREnabled:d,isFallback:l})}}return b}catch(i){if(m==null?void 0:m.cacheControl){const i=Math.min(Math.max(m.cacheControl.revalidate||3,3),30);const a=m.cacheControl.expire===undefined?undefined:Math.max(i+3,m.cacheControl.expire);await s.set(o,m.value,{cacheControl:{revalidate:i,expire:a},isRoutePPREnabled:d,isFallback:l})}if(g){console.error(i);return null}throw i}});return(0,u.toResponseCacheEntry)(f)}}},3381:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;s(a(7252),i);function s(o,i){Object.keys(o).forEach(function(a){if(a!=="default"&&!Object.prototype.hasOwnProperty.call(i,a)){Object.defineProperty(i,a,{enumerable:true,get:function(){return o[a]}})}});return o}},3426:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"NextResponse",{enumerable:true,get:function(){return y}});const s=a(3158);const c=a(6608);const u=a(7912);const l=a(3763);const d=a(3158);const f=Symbol("internal response");const p=new Set([301,302,303,307,308]);function h(o,i){var a;if(o==null?void 0:(a=o.request)==null?void 0:a.headers){if(!(o.request.headers instanceof Headers)){throw Object.defineProperty(new Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:false,configurable:true})}const a=[];for(const[s,c]of o.request.headers){i.set("x-middleware-request-"+s,c);a.push(s)}i.set("x-middleware-override-headers",a.join(","))}}class y extends Response{constructor(o,i={}){super(o,i);const a=this.headers;const p=new d.ResponseCookies(a);const y=new Proxy(p,{get(o,c,u){switch(c){case"delete":case"set":{return(...u)=>{const l=Reflect.apply(o[c],o,u);const f=new Headers(a);if(l instanceof d.ResponseCookies){a.set("x-middleware-set-cookie",l.getAll().map(o=>(0,s.stringifyCookie)(o)).join(","))}h(i,f);return l}}default:return l.ReflectAdapter.get(o,c,u)}}});this[f]={cookies:y,url:i.url?new c.NextURL(i.url,{headers:(0,u.toNodeOutgoingHttpHeaders)(a),nextConfig:i.nextConfig}):undefined}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[f].cookies}static json(o,i){const a=Response.json(o,i);return new y(a.body,a)}static redirect(o,i){const a=typeof i==="number"?i:(i==null?void 0:i.status)??307;if(!p.has(a)){throw Object.defineProperty(new RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:false,configurable:true})}const s=typeof i==="object"?i:{};const c=new Headers(s==null?void 0:s.headers);c.set("Location",(0,u.validateURL)(o));return new y(null,{...s,headers:c,status:a})}static rewrite(o,i){const a=new Headers(i==null?void 0:i.headers);a.set("x-middleware-rewrite",(0,u.validateURL)(o));h(i,a);return new y(null,{...i,headers:a})}static next(o){const i=new Headers(o==null?void 0:o.headers);i.set("x-middleware-next","1");h(o,i);return new y(null,{...o,headers:i})}}},3560:(o,i,a)=>{"use strict";a.d(i,{A:()=>s});function s(o){const i=o?.enterprise?.baseUrl??"https://github.com";const a=o?.enterprise?.baseUrl?`${o?.enterprise?.baseUrl}/api/v3`:"https://api.github.com";return{id:"github",name:"GitHub",type:"oauth",authorization:{url:`${i}/login/oauth/authorize`,params:{scope:"read:user user:email"}},token:`${i}/login/oauth/access_token`,userinfo:{url:`${a}/user`,async request({tokens:o,provider:i}){const s=await fetch(i.userinfo?.url,{headers:{Authorization:`Bearer ${o.access_token}`,"User-Agent":"authjs"}}).then(async o=>await o.json());if(!s.email){const i=await fetch(`${a}/user/emails`,{headers:{Authorization:`Bearer ${o.access_token}`,"User-Agent":"authjs"}});if(i.ok){const o=await i.json();s.email=(o.find(o=>o.primary)??o[0]).email}}return s}},profile(o){return{id:o.id.toString(),name:o.name??o.login,email:o.email,image:o.avatar_url}},style:{bg:"#24292f",text:"#fff"},options:o}}},3611:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{PageSignatureError:function(){return s},RemovedPageError:function(){return c},RemovedUAError:function(){return u}});class s extends Error{constructor({page:o}){super(`The middleware "${o}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class c extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class u extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},3763:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"ReflectAdapter",{enumerable:true,get:function(){return a}});class a{static get(o,i,a){const s=Reflect.get(o,i,a);if(typeof s==="function"){return s.bind(o)}return s}static set(o,i,a,s){return Reflect.set(o,i,a,s)}static has(o,i){return Reflect.has(o,i)}static deleteProperty(o,i){return Reflect.deleteProperty(o,i)}}},3828:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"detectDomainLocale",{enumerable:true,get:function(){return a}});function a(o,i,a){if(!o)return;if(a){a=a.toLowerCase()}for(const u of o){var s,c;const o=(s=u.domain)==null?void 0:s.split(":",1)[0].toLowerCase();if(i===o||a===u.defaultLocale.toLowerCase()||((c=u.locales)==null?void 0:c.some(o=>o.toLowerCase()===a))){return u}}}},3913:(o,i,a)=>{"use strict";var s;s={value:true};s={enumerable:true,get:function(){return h}};const c=a(3033);const u=a(9294);const l=a(4971);const d=a(6926);const f=a(23);const p=a(8479);function h(){const o="draftMode";const i=u.workAsyncStorage.getStore();const a=c.workUnitAsyncStorage.getStore();if(!i||!a){(0,c.throwForMissingRequestStore)(o)}switch(a.type){case"request":return y(a.draftMode,i);case"cache":case"unstable-cache":const s=(0,c.getDraftModeProviderForCacheScope)(i,a);if(s){return y(s,i)}case"prerender":case"prerender-ppr":case"prerender-legacy":if(false){}else{return m(null)}default:const l=a;return l}}function y(o,i){const a=g.get(h);if(a){return a}let s;if(false){}else{s=m(o)}g.set(o,s);return s}const g=new WeakMap;function m(o){const i=new w(o);const a=Promise.resolve(i);Object.defineProperty(a,"isEnabled",{get(){return i.isEnabled},set(o){Object.defineProperty(a,"isEnabled",{value:o,writable:true,enumerable:true})},enumerable:true,configurable:true});a.enable=i.enable.bind(i);a.disable=i.disable.bind(i);return a}function b(o,i){const a=new w(o);const s=Promise.resolve(a);Object.defineProperty(s,"isEnabled",{get(){const o="`draftMode().isEnabled`";_(i,o);return a.isEnabled},set(o){Object.defineProperty(s,"isEnabled",{value:o,writable:true,enumerable:true})},enumerable:true,configurable:true});Object.defineProperty(s,"enable",{value:function o(){const o="`draftMode().enable()`";_(i,o);return a.enable.apply(a,arguments)}});Object.defineProperty(s,"disable",{value:function o(){const o="`draftMode().disable()`";_(i,o);return a.disable.apply(a,arguments)}});return s}class w{constructor(o){this._provider=o}get isEnabled(){if(this._provider!==null){return this._provider.isEnabled}return false}enable(){S("draftMode().enable()");if(this._provider!==null){this._provider.enable()}}disable(){S("draftMode().disable()");if(this._provider!==null){this._provider.disable()}}}function _(o,i){const a=c.workUnitAsyncStorage.getStore();if(a&&a.type==="request"&&a.prerenderPhase===true){const o=a;(0,l.trackSynchronousRequestDataAccessInDev)(o)}v(o,i)}const v=(0,d.createDedupedByCallsiteServerErrorLoggerDev)(E);function E(o,i){const a=o?`Route "${o}" `:"This route ";return Object.defineProperty(new Error(`${a}used ${i}. `+`\`draftMode()\` should be awaited before using its value. `+`Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:false,configurable:true})}function S(o){const i=u.workAsyncStorage.getStore();const a=c.workUnitAsyncStorage.getStore();if(i){if(a){if(a.type==="cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "${o}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:false,configurable:true})}else if(a.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "${o}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:false,configurable:true})}else if(a.phase==="after"){throw Object.defineProperty(new Error(`Route ${i.route} used "${o}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:false,configurable:true})}}if(i.dynamicShouldError){throw Object.defineProperty(new f.StaticGenBailoutError(`Route ${i.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${o}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:false,configurable:true})}if(a){if(a.type==="prerender"){const s=Object.defineProperty(new Error(`Route ${i.route} used ${o} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:false,configurable:true});(0,l.abortAndThrowOnSynchronousRequestDataAccess)(i.route,o,s,a)}else if(a.type==="prerender-ppr"){(0,l.postponeWithTracking)(i.route,o,a.dynamicTracking)}else if(a.type==="prerender-legacy"){a.revalidate=0;const s=Object.defineProperty(new p.DynamicServerError(`Route ${i.route} couldn't be rendered statically because it used \`${o}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:false,configurable:true});i.dynamicUsageDescription=o;i.dynamicUsageStack=s.stack;throw s}else if(false){}}}}},4069:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return f},RequestCookiesAdapter:function(){return p},appendMutableCookies:function(){return g},areCookiesMutableInCurrentPhase:function(){return w},getModifiedCookieValues:function(){return y},responseCookiesToRequestCookies:function(){return v},wrapWithMutableAccessCheck:function(){return b}});const c=a(3158);const u=a(3763);const l=a(9294);const d=a(3033);class f extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new f}}class p{static seal(o){return new Proxy(o,{get(o,i,a){switch(i){case"clear":case"delete":case"set":return f.callable;default:return u.ReflectAdapter.get(o,i,a)}}})}}const h=Symbol.for("next.mutated.cookies");function y(o){const i=o[h];if(!i||!Array.isArray(i)||i.length===0){return[]}return i}function g(o,i){const a=y(i);if(a.length===0){return false}const s=new c.ResponseCookies(o);const u=s.getAll();for(const o of a){s.set(o)}for(const o of u){s.set(o)}return true}class m{static wrap(o,i){const a=new c.ResponseCookies(new Headers);for(const i of o.getAll()){a.set(i)}let s=[];const d=new Set;const f=()=>{const o=l.workAsyncStorage.getStore();if(o){o.pathWasRevalidated=true}const u=a.getAll();s=u.filter(o=>d.has(o.name));if(i){const o=[];for(const i of s){const a=new c.ResponseCookies(new Headers);a.set(i);o.push(a.toString())}i(o)}};const p=new Proxy(a,{get(o,i,a){switch(i){case h:return s;case"delete":return function(...i){d.add(typeof i[0]==="string"?i[0]:i[0].name);try{o.delete(...i);return p}finally{f()}};case"set":return function(...i){d.add(typeof i[0]==="string"?i[0]:i[0].name);try{o.set(...i);return p}finally{f()}};default:return u.ReflectAdapter.get(o,i,a)}}});return p}}function b(o){const i=new Proxy(o,{get(o,a,s){switch(a){case"delete":return function(...a){_("cookies().delete");o.delete(...a);return i};case"set":return function(...a){_("cookies().set");o.set(...a);return i};default:return u.ReflectAdapter.get(o,a,s)}}});return i}function w(o){return o.phase==="action"}function _(o){const i=(0,d.getExpectedRequestStore)(o);if(!w(i)){throw new f}}function v(o){const i=new c.RequestCookies(new Headers);for(const a of o.getAll()){i.set(a)}return i}},4113:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"MISSING_ROOT_TAGS_ERROR",{enumerable:true,get:function(){return a}});const a="NEXT_MISSING_ROOT_TAGS";if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},4436:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"createDedupeFetch",{enumerable:true,get:function(){return h}});const s=d(a(1120));const c=a(9169);const u=a(1617);function l(o){if(typeof WeakMap!=="function")return null;var i=new WeakMap;var a=new WeakMap;return(l=function(o){return o?a:i})(o)}function d(o,i){if(!i&&o&&o.__esModule){return o}if(o===null||typeof o!=="object"&&typeof o!=="function"){return{default:o}}var a=l(i);if(a&&a.has(o)){return a.get(o)}var s={__proto__:null};var c=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in o){if(u!=="default"&&Object.prototype.hasOwnProperty.call(o,u)){var d=c?Object.getOwnPropertyDescriptor(o,u):null;if(d&&(d.get||d.set)){Object.defineProperty(s,u,d)}else{s[u]=o[u]}}}s.default=o;if(a){a.set(o,s)}return s}const f='["GET",[],null,"follow",null,null,null,null]';function p(o){return JSON.stringify([o.method,Array.from(o.headers.entries()),o.mode,o.redirect,o.credentials,o.referrer,o.referrerPolicy,o.integrity])}function h(o){const i=s.cache(o=>[]);return function a(a,s){if(s&&s.signal){return o(a,s)}let l;let d;if(typeof a==="string"&&!s){d=f;l=a}else{const i=typeof a==="string"||a instanceof URL?new Request(a,s):a;if(i.method!=="GET"&&i.method!=="HEAD"||i.keepalive){return o(a,s)}d=p(i);l=i.url}const h=i(l);for(let o=0,i=h.length;o<i;o+=1){const[i,a]=h[o];if(i===d){return a.then(()=>{const i=h[o][2];if(!i)throw Object.defineProperty(new u.InvariantError("No cached response"),"__NEXT_ERROR_CODE",{value:"E579",enumerable:false,configurable:true});const[a,s]=(0,c.cloneResponse)(i);h[o][2]=s;return a})}}const y=o(a,s);const g=[d,y,null];h.push(g);return y.then(o=>{const[i,a]=(0,c.cloneResponse)(o);g[2]=a;return i})}}},4523:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{atLeastOneTask:function(){return u},scheduleImmediate:function(){return c},scheduleOnNextTick:function(){return s},waitAtLeastOneReactRenderTask:function(){return l}});const s=o=>{Promise.resolve().then(()=>{if(false){}else{process.nextTick(o)}})};const c=o=>{if(false){}else{setImmediate(o)}};function u(){return new Promise(o=>c(o))}function l(){if(false){}else{return new Promise(o=>setImmediate(o))}}},4525:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{ImageResponse:function(){return c.ImageResponse},NextRequest:function(){return u.NextRequest},NextResponse:function(){return l.NextResponse},URLPattern:function(){return f.URLPattern},after:function(){return p.after},connection:function(){return h.connection},unstable_rootParams:function(){return y.unstable_rootParams},userAgent:function(){return d.userAgent},userAgentFromString:function(){return d.userAgentFromString}});const c=a(2174);const u=a(6268);const l=a(3426);const d=a(3182);const f=a(1243);const p=a(3381);const h=a(2944);const y=a(2079)},4823:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{AppRenderSpan:function(){return p},AppRouteRouteHandlersSpan:function(){return g},BaseServerSpan:function(){return s},LoadComponentsSpan:function(){return c},LogSpanAllowList:function(){return _},MiddlewareSpan:function(){return b},NextNodeServerSpan:function(){return l},NextServerSpan:function(){return u},NextVanillaSpanAllowlist:function(){return w},NodeSpan:function(){return y},RenderSpan:function(){return f},ResolveMetadataSpan:function(){return m},RouterSpan:function(){return h},StartServerSpan:function(){return d}});var s=function(o){o["handleRequest"]="BaseServer.handleRequest";o["run"]="BaseServer.run";o["pipe"]="BaseServer.pipe";o["getStaticHTML"]="BaseServer.getStaticHTML";o["render"]="BaseServer.render";o["renderToResponseWithComponents"]="BaseServer.renderToResponseWithComponents";o["renderToResponse"]="BaseServer.renderToResponse";o["renderToHTML"]="BaseServer.renderToHTML";o["renderError"]="BaseServer.renderError";o["renderErrorToResponse"]="BaseServer.renderErrorToResponse";o["renderErrorToHTML"]="BaseServer.renderErrorToHTML";o["render404"]="BaseServer.render404";return o}(s||{});var c=function(o){o["loadDefaultErrorComponents"]="LoadComponents.loadDefaultErrorComponents";o["loadComponents"]="LoadComponents.loadComponents";return o}(c||{});var u=function(o){o["getRequestHandler"]="NextServer.getRequestHandler";o["getServer"]="NextServer.getServer";o["getServerRequestHandler"]="NextServer.getServerRequestHandler";o["createServer"]="createServer.createServer";return o}(u||{});var l=function(o){o["compression"]="NextNodeServer.compression";o["getBuildId"]="NextNodeServer.getBuildId";o["createComponentTree"]="NextNodeServer.createComponentTree";o["clientComponentLoading"]="NextNodeServer.clientComponentLoading";o["getLayoutOrPageModule"]="NextNodeServer.getLayoutOrPageModule";o["generateStaticRoutes"]="NextNodeServer.generateStaticRoutes";o["generateFsStaticRoutes"]="NextNodeServer.generateFsStaticRoutes";o["generatePublicRoutes"]="NextNodeServer.generatePublicRoutes";o["generateImageRoutes"]="NextNodeServer.generateImageRoutes.route";o["sendRenderResult"]="NextNodeServer.sendRenderResult";o["proxyRequest"]="NextNodeServer.proxyRequest";o["runApi"]="NextNodeServer.runApi";o["render"]="NextNodeServer.render";o["renderHTML"]="NextNodeServer.renderHTML";o["imageOptimizer"]="NextNodeServer.imageOptimizer";o["getPagePath"]="NextNodeServer.getPagePath";o["getRoutesManifest"]="NextNodeServer.getRoutesManifest";o["findPageComponents"]="NextNodeServer.findPageComponents";o["getFontManifest"]="NextNodeServer.getFontManifest";o["getServerComponentManifest"]="NextNodeServer.getServerComponentManifest";o["getRequestHandler"]="NextNodeServer.getRequestHandler";o["renderToHTML"]="NextNodeServer.renderToHTML";o["renderError"]="NextNodeServer.renderError";o["renderErrorToHTML"]="NextNodeServer.renderErrorToHTML";o["render404"]="NextNodeServer.render404";o["startResponse"]="NextNodeServer.startResponse";o["route"]="route";o["onProxyReq"]="onProxyReq";o["apiResolver"]="apiResolver";o["internalFetch"]="internalFetch";return o}(l||{});var d=function(o){o["startServer"]="startServer.startServer";return o}(d||{});var f=function(o){o["getServerSideProps"]="Render.getServerSideProps";o["getStaticProps"]="Render.getStaticProps";o["renderToString"]="Render.renderToString";o["renderDocument"]="Render.renderDocument";o["createBodyResult"]="Render.createBodyResult";return o}(f||{});var p=function(o){o["renderToString"]="AppRender.renderToString";o["renderToReadableStream"]="AppRender.renderToReadableStream";o["getBodyResult"]="AppRender.getBodyResult";o["fetch"]="AppRender.fetch";return o}(p||{});var h=function(o){o["executeRoute"]="Router.executeRoute";return o}(h||{});var y=function(o){o["runHandler"]="Node.runHandler";return o}(y||{});var g=function(o){o["runHandler"]="AppRouteRouteHandlers.runHandler";return o}(g||{});var m=function(o){o["generateMetadata"]="ResolveMetadata.generateMetadata";o["generateViewport"]="ResolveMetadata.generateViewport";return o}(m||{});var b=function(o){o["execute"]="Middleware.execute";return o}(b||{});const w=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"];const _=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},4971:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{Postpone:function(){return x},abortAndThrowOnSynchronousRequestDataAccess:function(){return O},abortOnSynchronousPlatformIOAccess:function(){return P},accessedDynamicData:function(){return L},annotateDynamicAccess:function(){return q},consumeDynamicAccess:function(){return $},createDynamicTrackingState:function(){return b},createDynamicValidationState:function(){return w},createHangingInputAbortSignal:function(){return B},createPostponedAbortSignal:function(){return K},formatDynamicAPIAccesses:function(){return H},getFirstDynamicReason:function(){return _},isDynamicPostpone:function(){return N},isPrerenderInterruptedError:function(){return M},markCurrentScopeAsDynamic:function(){return v},postponeWithTracking:function(){return C},throwIfDisallowedDynamic:function(){return Z},throwToInterruptStaticGeneration:function(){return S},trackAllowedDynamicAccess:function(){return X},trackDynamicDataInDynamicRender:function(){return R},trackFallbackParamAccessed:function(){return E},trackSynchronousPlatformIOAccessInDev:function(){return k},trackSynchronousRequestDataAccessInDev:function(){return T},useDynamicRouteParams:function(){return G}});const c=g(a(1120));const u=a(8479);const l=a(23);const d=a(3033);const f=a(9294);const p=a(8388);const h=a(7625);const y=a(4523);function g(o){return o&&o.__esModule?o:{default:o}}const m=typeof c.default.unstable_postpone==="function";function b(o){return{isDebugDynamicAccesses:o,dynamicAccesses:[],syncDynamicExpression:undefined,syncDynamicErrorWithStack:null}}function w(){return{hasSuspendedDynamic:false,hasDynamicMetadata:false,hasDynamicViewport:false,hasSyncDynamicErrors:false,dynamicErrors:[]}}function _(o){var i;return(i=o.dynamicAccesses[0])==null?void 0:i.expression}function v(o,i,a){if(i){if(i.type==="cache"||i.type==="unstable-cache"){return}}if(o.forceDynamic||o.forceStatic)return;if(o.dynamicShouldError){throw Object.defineProperty(new l.StaticGenBailoutError(`Route ${o.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:false,configurable:true})}if(i){if(i.type==="prerender-ppr"){C(o.route,a,i.dynamicTracking)}else if(i.type==="prerender-legacy"){i.revalidate=0;const s=Object.defineProperty(new u.DynamicServerError(`Route ${o.route} couldn't be rendered statically because it used ${a}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:false,configurable:true});o.dynamicUsageDescription=a;o.dynamicUsageStack=s.stack;throw s}else if(false){}}}function E(o,i){const a=d.workUnitAsyncStorage.getStore();if(!a||a.type!=="prerender-ppr")return;C(o.route,i,a.dynamicTracking)}function S(o,i,a){const s=Object.defineProperty(new u.DynamicServerError(`Route ${i.route} couldn't be rendered statically because it used \`${o}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:false,configurable:true});a.revalidate=0;i.dynamicUsageDescription=o;i.dynamicUsageStack=s.stack;throw s}function R(o,i){if(i){if(i.type==="cache"||i.type==="unstable-cache"){return}if(i.type==="prerender"||i.type==="prerender-legacy"){i.revalidate=0}if(false){}}}function A(o,i,a){const s=`Route ${o} needs to bail out of prerendering at this point because it used ${i}.`;const c=U(s);a.controller.abort(c);const u=a.dynamicTracking;if(u){u.dynamicAccesses.push({stack:u.isDebugDynamicAccesses?new Error().stack:undefined,expression:i})}}function P(o,i,a,s){const c=s.dynamicTracking;if(c){if(c.syncDynamicErrorWithStack===null){c.syncDynamicExpression=i;c.syncDynamicErrorWithStack=a}}A(o,i,s)}function k(o){o.prerenderPhase=false}function O(o,i,a,s){const c=s.controller.signal;if(c.aborted===false){const c=s.dynamicTracking;if(c){if(c.syncDynamicErrorWithStack===null){c.syncDynamicExpression=i;c.syncDynamicErrorWithStack=a;if(s.validating===true){c.syncDynamicLogged=true}}}A(o,i,s)}throw U(`Route ${o} needs to bail out of prerendering at this point because it used ${i}.`)}const T=k;function x({reason:o,route:i}){const a=d.workUnitAsyncStorage.getStore();const s=a&&a.type==="prerender-ppr"?a.dynamicTracking:null;C(i,o,s)}function C(o,i,a){W();if(a){a.dynamicAccesses.push({stack:a.isDebugDynamicAccesses?new Error().stack:undefined,expression:i})}c.default.unstable_postpone(j(o,i))}function j(o,i){return`Route ${o} needs to bail out of prerendering at this point because it used ${i}. `+`React throws this special object to indicate where. It should not be caught by `+`your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function N(o){if(typeof o==="object"&&o!==null&&typeof o.message==="string"){return I(o.message)}return false}function I(o){return o.includes("needs to bail out of prerendering at this point because it used")&&o.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(I(j("%%%","^^^"))===false){throw Object.defineProperty(new Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:false,configurable:true})}const D="NEXT_PRERENDER_INTERRUPTED";function U(o){const i=Object.defineProperty(new Error(o),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});i.digest=D;return i}function M(o){return typeof o==="object"&&o!==null&&o.digest===D&&"name"in o&&"message"in o&&o instanceof Error}function L(o){return o.length>0}function $(o,i){o.dynamicAccesses.push(...i.dynamicAccesses);return o.dynamicAccesses}function H(o){return o.filter(o=>typeof o.stack==="string"&&o.stack.length>0).map(({expression:o,stack:i})=>{i=i.split("\n").slice(4).filter(o=>{if(o.includes("node_modules/next/")){return false}if(o.includes(" (<anonymous>)")){return false}if(o.includes(" (node:")){return false}return true}).join("\n");return`Dynamic API Usage Debug - ${o}:
${i}`})}function W(){if(!m){throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`),"__NEXT_ERROR_CODE",{value:"E224",enumerable:false,configurable:true})}}function K(o){W();const i=new AbortController;try{c.default.unstable_postpone(o)}catch(o){i.abort(o)}return i.signal}function B(o){const i=new AbortController;if(o.cacheSignal){o.cacheSignal.inputReady().then(()=>{i.abort()})}else{(0,y.scheduleOnNextTick)(()=>i.abort())}return i.signal}function q(o,i){const a=i.dynamicTracking;if(a){a.dynamicAccesses.push({stack:a.isDebugDynamicAccesses?new Error().stack:undefined,expression:o})}}function G(o){const i=f.workAsyncStorage.getStore();if(i&&i.isStaticGeneration&&i.fallbackRouteParams&&i.fallbackRouteParams.size>0){const a=d.workUnitAsyncStorage.getStore();if(a){if(a.type==="prerender"){c.default.use((0,p.makeHangingPromise)(a.renderSignal,o))}else if(a.type==="prerender-ppr"){C(i.route,o,a.dynamicTracking)}else if(a.type==="prerender-legacy"){S(o,i,a)}}}}const F=/\n\s+at Suspense \(<anonymous>\)/;const J=new RegExp(`\\n\\s+at ${h.METADATA_BOUNDARY_NAME}[\\n\\s]`);const V=new RegExp(`\\n\\s+at ${h.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`);const z=new RegExp(`\\n\\s+at ${h.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function X(o,i,a,s,c){if(z.test(i)){return}else if(J.test(i)){a.hasDynamicMetadata=true;return}else if(V.test(i)){a.hasDynamicViewport=true;return}else if(F.test(i)){a.hasSuspendedDynamic=true;return}else if(s.syncDynamicErrorWithStack||c.syncDynamicErrorWithStack){a.hasSyncDynamicErrors=true;return}else{const s=`Route "${o}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;const c=Y(s,i);a.dynamicErrors.push(c);return}}function Y(o,i){const a=Object.defineProperty(new Error(o),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});a.stack="Error: "+o+i;return a}function Z(o,i,a,s){let c;let u;let d;if(a.syncDynamicErrorWithStack){c=a.syncDynamicErrorWithStack;u=a.syncDynamicExpression;d=a.syncDynamicLogged===true}else if(s.syncDynamicErrorWithStack){c=s.syncDynamicErrorWithStack;u=s.syncDynamicExpression;d=s.syncDynamicLogged===true}else{c=null;u=undefined;d=false}if(i.hasSyncDynamicErrors&&c){if(!d){console.error(c)}throw new l.StaticGenBailoutError}const f=i.dynamicErrors;if(f.length){for(let o=0;o<f.length;o++){console.error(f[o])}throw new l.StaticGenBailoutError}if(!i.hasSuspendedDynamic){if(i.hasDynamicMetadata){if(c){console.error(c);throw Object.defineProperty(new l.StaticGenBailoutError(`Route "${o}" has a \`generateMetadata\` that could not finish rendering before ${u} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:false,configurable:true})}throw Object.defineProperty(new l.StaticGenBailoutError(`Route "${o}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:false,configurable:true})}else if(i.hasDynamicViewport){if(c){console.error(c);throw Object.defineProperty(new l.StaticGenBailoutError(`Route "${o}" has a \`generateViewport\` that could not finish rendering before ${u} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:false,configurable:true})}throw Object.defineProperty(new l.StaticGenBailoutError(`Route "${o}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:false,configurable:true})}}}},5239:(o,i,a)=>{"use strict";if(false){}else{if(false){}else{if(false){}else{if(false){}else{o.exports=a(846)}}}}},6056:(o,i,a)=>{"use strict";a.d(i,{A:()=>s});function s(o){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:o}}},6143:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{ACTION_SUFFIX:function(){return g},APP_DIR_ALIAS:function(){return U},CACHE_ONE_YEAR:function(){return O},DOT_NEXT_ALIAS:function(){return I},ESLINT_DEFAULT_DIRS:function(){return et},GSP_NO_RETURNED_VALUE:function(){return z},GSSP_COMPONENT_MEMBER_ERROR:function(){return Z},GSSP_NO_RETURNED_VALUE:function(){return X},INFINITE_CACHE:function(){return T},INSTRUMENTATION_HOOK_FILENAME:function(){return j},MATCHED_PATH_HEADER:function(){return u},MIDDLEWARE_FILENAME:function(){return x},MIDDLEWARE_LOCATION_REGEXP:function(){return C},NEXT_BODY_SUFFIX:function(){return w},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return k},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return v},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return E},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return P},NEXT_CACHE_TAGS_HEADER:function(){return _},NEXT_CACHE_TAG_MAX_ITEMS:function(){return R},NEXT_CACHE_TAG_MAX_LENGTH:function(){return A},NEXT_DATA_SUFFIX:function(){return m},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return c},NEXT_META_SUFFIX:function(){return b},NEXT_QUERY_PARAM_PREFIX:function(){return s},NEXT_RESUME_HEADER:function(){return S},NON_STANDARD_NODE_ENV:function(){return Q},PAGES_DIR_ALIAS:function(){return N},PRERENDER_REVALIDATE_HEADER:function(){return l},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return d},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return B},ROOT_DIR_ALIAS:function(){return D},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return K},RSC_ACTION_ENCRYPTION_ALIAS:function(){return W},RSC_ACTION_PROXY_ALIAS:function(){return $},RSC_ACTION_VALIDATE_ALIAS:function(){return L},RSC_CACHE_WRAPPER_ALIAS:function(){return H},RSC_MOD_REF_PROXY_ALIAS:function(){return M},RSC_PREFETCH_SUFFIX:function(){return f},RSC_SEGMENTS_DIR_SUFFIX:function(){return p},RSC_SEGMENT_SUFFIX:function(){return h},RSC_SUFFIX:function(){return y},SERVER_PROPS_EXPORT_ERROR:function(){return V},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return G},SERVER_PROPS_SSG_CONFLICT:function(){return F},SERVER_RUNTIME:function(){return en},SSG_FALLBACK_EXPORT_ERROR:function(){return ee},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return q},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return J},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return Y},WEBPACK_LAYERS:function(){return eo},WEBPACK_RESOURCE_QUERIES:function(){return ei}});const s="nxtP";const c="nxtI";const u="x-matched-path";const l="x-prerender-revalidate";const d="x-prerender-revalidate-if-generated";const f=".prefetch.rsc";const p=".segments";const h=".segment.rsc";const y=".rsc";const g=".action";const m=".json";const b=".meta";const w=".body";const _="x-next-cache-tags";const v="x-next-revalidated-tags";const E="x-next-revalidate-tag-token";const S="next-resume";const R=128;const A=256;const P=1024;const k="_N_T_";const O=31536e3;const T=0xfffffffe;const x="middleware";const C=`(?:src/)?${x}`;const j="instrumentation";const N="private-next-pages";const I="private-dot-next";const D="private-next-root-dir";const U="private-next-app-dir";const M="next/dist/build/webpack/loaders/next-flight-loader/module-proxy";const L="private-next-rsc-action-validate";const $="private-next-rsc-server-reference";const H="private-next-rsc-cache-wrapper";const W="private-next-rsc-action-encryption";const K="private-next-rsc-action-client-wrapper";const B=`You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`;const q=`You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`;const G=`You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`;const F=`You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`;const J=`can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`;const V=`pages with \`getServerSideProps\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`;const z="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?";const X="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?";const Y="The `unstable_revalidate` property is available for general use.\n"+"Please use `revalidate` instead.";const Z=`can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`;const Q=`You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`;const ee=`Pages with \`fallback\` enabled in \`getStaticPaths\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`;const et=["app","pages","components","lib","src"];const en={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"};const er={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};const eo={...er,GROUP:{builtinReact:[er.reactServerComponents,er.actionBrowser],serverOnly:[er.reactServerComponents,er.actionBrowser,er.instrument,er.middleware],neutralTarget:[er.apiNode,er.apiEdge],clientOnly:[er.serverSideRendering,er.appPagesBrowser],bundled:[er.reactServerComponents,er.actionBrowser,er.serverSideRendering,er.appPagesBrowser,er.shared,er.instrument,er.middleware],appPages:[er.reactServerComponents,er.serverSideRendering,er.appPagesBrowser,er.actionBrowser]}};const ei={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},6191:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{NEXT_REQUEST_META:function(){return s},addRequestMeta:function(){return l},getRequestMeta:function(){return c},removeRequestMeta:function(){return d},setRequestMeta:function(){return u}});const s=Symbol.for("NextInternalRequestMeta");function c(o,i){const a=o[s]||{};return typeof i==="string"?a[i]:a}function u(o,i){o[s]=i;return i}function l(o,i,a){const s=c(o);s[i]=a;return u(o,s)}function d(o,i){const a=c(o);delete a[i];return u(o,a)}},6268:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{INTERNALS:function(){return f},NextRequest:function(){return p}});const c=a(6608);const u=a(7912);const l=a(3611);const d=a(3158);const f=Symbol("internal request");class p extends Request{constructor(o,i={}){const a=typeof o!=="string"&&"url"in o?o.url:String(o);(0,u.validateURL)(a);if(true){if(i.body&&i.duplex!=="half"){i.duplex="half"}}if(o instanceof Request)super(o,i);else super(a,i);const s=new c.NextURL(a,{headers:(0,u.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:i.nextConfig});this[f]={cookies:new d.RequestCookies(this.headers),nextUrl:s,url:false?0:s.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[f].cookies}get nextUrl(){return this[f].nextUrl}get page(){throw new l.RemovedPageError}get ua(){throw new l.RemovedUAError}get url(){return this[f].url}}},6280:(o,i,a)=>{"use strict";var s;s={value:true};Object.defineProperty(i,"b",{enumerable:true,get:function(){return m}});const c=a(2584);const u=a(9294);const l=a(3033);const d=a(4971);const f=a(23);const p=a(8388);const h=a(6926);const y=a(4523);const g=a(8719);function m(){const o=u.workAsyncStorage.getStore();const i=l.workUnitAsyncStorage.getStore();if(o){if(i&&i.phase==="after"&&!(0,g.isRequestAPICallableInsideAfter)()){throw Object.defineProperty(new Error(`Route ${o.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:false,configurable:true})}if(o.forceStatic){const o=c.HeadersAdapter.seal(new Headers({}));return _(o)}if(i){if(i.type==="cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:false,configurable:true})}else if(i.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:false,configurable:true})}}if(o.dynamicShouldError){throw Object.defineProperty(new f.StaticGenBailoutError(`Route ${o.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:false,configurable:true})}if(i){if(i.type==="prerender"){return w(o.route,i)}else if(i.type==="prerender-ppr"){(0,d.postponeWithTracking)(o.route,"headers",i.dynamicTracking)}else if(i.type==="prerender-legacy"){(0,d.throwToInterruptStaticGeneration)("headers",o,i)}}(0,d.trackDynamicDataInDynamicRender)(o,i)}const a=(0,l.getExpectedRequestStore)("headers");if(false){}else{return _(a.headers)}}const b=new WeakMap;function w(o,i){const a=b.get(i);if(a){return a}const s=(0,p.makeHangingPromise)(i.renderSignal,"`headers()`");b.set(i,s);Object.defineProperties(s,{append:{value:function a(){const a=`\`headers().append(${E(arguments[0])}, ...)\``;const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},delete:{value:function a(){const a=`\`headers().delete(${E(arguments[0])})\``;const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},get:{value:function a(){const a=`\`headers().get(${E(arguments[0])})\``;const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},has:{value:function a(){const a=`\`headers().has(${E(arguments[0])})\``;const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},set:{value:function a(){const a=`\`headers().set(${E(arguments[0])}, ...)\``;const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},getSetCookie:{value:function a(){const a="`headers().getSetCookie()`";const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},forEach:{value:function a(){const a="`headers().forEach(...)`";const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},keys:{value:function a(){const a="`headers().keys()`";const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},values:{value:function a(){const a="`headers().values()`";const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},entries:{value:function a(){const a="`headers().entries()`";const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},[Symbol.iterator]:{value:function(){const a="`headers()[Symbol.iterator]()`";const s=A(o,a);(0,d.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}}});return s}function _(o){const i=b.get(o);if(i){return i}const a=Promise.resolve(o);b.set(o,a);Object.defineProperties(a,{append:{value:o.append.bind(o)},delete:{value:o.delete.bind(o)},get:{value:o.get.bind(o)},has:{value:o.has.bind(o)},set:{value:o.set.bind(o)},getSetCookie:{value:o.getSetCookie.bind(o)},forEach:{value:o.forEach.bind(o)},keys:{value:o.keys.bind(o)},values:{value:o.values.bind(o)},entries:{value:o.entries.bind(o)},[Symbol.iterator]:{value:o[Symbol.iterator].bind(o)}});return a}function v(o,i){const a=b.get(o);if(a){return a}const s=new Promise(i=>(0,y.scheduleImmediate)(()=>i(o)));b.set(o,s);Object.defineProperties(s,{append:{value:function a(){const a=`\`headers().append(${E(arguments[0])}, ...)\``;S(i,a);return o.append.apply(o,arguments)}},delete:{value:function a(){const a=`\`headers().delete(${E(arguments[0])})\``;S(i,a);return o.delete.apply(o,arguments)}},get:{value:function a(){const a=`\`headers().get(${E(arguments[0])})\``;S(i,a);return o.get.apply(o,arguments)}},has:{value:function a(){const a=`\`headers().has(${E(arguments[0])})\``;S(i,a);return o.has.apply(o,arguments)}},set:{value:function a(){const a=`\`headers().set(${E(arguments[0])}, ...)\``;S(i,a);return o.set.apply(o,arguments)}},getSetCookie:{value:function a(){const a="`headers().getSetCookie()`";S(i,a);return o.getSetCookie.apply(o,arguments)}},forEach:{value:function a(){const a="`headers().forEach(...)`";S(i,a);return o.forEach.apply(o,arguments)}},keys:{value:function a(){const a="`headers().keys()`";S(i,a);return o.keys.apply(o,arguments)}},values:{value:function a(){const a="`headers().values()`";S(i,a);return o.values.apply(o,arguments)}},entries:{value:function a(){const a="`headers().entries()`";S(i,a);return o.entries.apply(o,arguments)}},[Symbol.iterator]:{value:function(){const a="`...headers()` or similar iteration";S(i,a);return o[Symbol.iterator].apply(o,arguments)}}});return s}function E(o){return typeof o==="string"?`'${o}'`:"..."}function S(o,i){const a=l.workUnitAsyncStorage.getStore();if(a&&a.type==="request"&&a.prerenderPhase===true){const o=a;(0,d.trackSynchronousRequestDataAccessInDev)(o)}R(o,i)}const R=(0,h.createDedupedByCallsiteServerErrorLoggerDev)(A);function A(o,i){const a=o?`Route "${o}" `:"This route ";return Object.defineProperty(new Error(`${a}used ${i}. `+`\`headers()\` should be awaited before using its value. `+`Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:false,configurable:true})}},6608:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"NextURL",{enumerable:true,get:function(){return h}});const s=a(3828);const c=a(7853);const u=a(1314);const l=a(9938);const d=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function f(o,i){return new URL(String(o).replace(d,"localhost"),i&&String(i).replace(d,"localhost"))}const p=Symbol("NextURLInternal");class h{constructor(o,i,a){let s;let c;if(typeof i==="object"&&"pathname"in i||typeof i==="string"){s=i;c=a||{}}else{c=a||i||{}}this[p]={url:f(o,s??c.base),options:c,basePath:""};this.analyze()}analyze(){var o,i,a,c,d;const f=(0,l.getNextPathnameInfo)(this[p].url.pathname,{nextConfig:this[p].options.nextConfig,parseData:!undefined,i18nProvider:this[p].options.i18nProvider});const h=(0,u.getHostname)(this[p].url,this[p].options.headers);this[p].domainLocale=this[p].options.i18nProvider?this[p].options.i18nProvider.detectDomainLocale(h):(0,s.detectDomainLocale)((i=this[p].options.nextConfig)==null?void 0:(o=i.i18n)==null?void 0:o.domains,h);const y=((a=this[p].domainLocale)==null?void 0:a.defaultLocale)||((d=this[p].options.nextConfig)==null?void 0:(c=d.i18n)==null?void 0:c.defaultLocale);this[p].url.pathname=f.pathname;this[p].defaultLocale=y;this[p].basePath=f.basePath??"";this[p].buildId=f.buildId;this[p].locale=f.locale??y;this[p].trailingSlash=f.trailingSlash}formatPathname(){return(0,c.formatNextPathnameInfo)({basePath:this[p].basePath,buildId:this[p].buildId,defaultLocale:!this[p].options.forceLocale?this[p].defaultLocale:undefined,locale:this[p].locale,pathname:this[p].url.pathname,trailingSlash:this[p].trailingSlash})}formatSearch(){return this[p].url.search}get buildId(){return this[p].buildId}set buildId(o){this[p].buildId=o}get locale(){return this[p].locale??""}set locale(o){var i,a;if(!this[p].locale||!((a=this[p].options.nextConfig)==null?void 0:(i=a.i18n)==null?void 0:i.locales.includes(o))){throw Object.defineProperty(new TypeError(`The NextURL configuration includes no locale "${o}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:false,configurable:true})}this[p].locale=o}get defaultLocale(){return this[p].defaultLocale}get domainLocale(){return this[p].domainLocale}get searchParams(){return this[p].url.searchParams}get host(){return this[p].url.host}set host(o){this[p].url.host=o}get hostname(){return this[p].url.hostname}set hostname(o){this[p].url.hostname=o}get port(){return this[p].url.port}set port(o){this[p].url.port=o}get protocol(){return this[p].url.protocol}set protocol(o){this[p].url.protocol=o}get href(){const o=this.formatPathname();const i=this.formatSearch();return`${this.protocol}//${this.host}${o}${i}${this.hash}`}set href(o){this[p].url=f(o);this.analyze()}get origin(){return this[p].url.origin}get pathname(){return this[p].url.pathname}set pathname(o){this[p].url.pathname=o}get hash(){return this[p].url.hash}set hash(o){this[p].url.hash=o}get search(){return this[p].url.search}set search(o){this[p].url.search=o}get password(){return this[p].url.password}set password(o){this[p].url.password=o}get username(){return this[p].url.username}set username(o){this[p].url.username=o}get basePath(){return this[p].basePath}set basePath(o){this[p].basePath=o.startsWith("/")?o:`/${o}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new h(String(this),this[p].options)}}},6897:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{getRedirectError:function(){return d},getRedirectStatusCodeFromError:function(){return g},getRedirectTypeFromError:function(){return y},getURLFromRedirectError:function(){return h},permanentRedirect:function(){return p},redirect:function(){return f}});const c=a(2836);const u=a(9026);const l=true?a(9121).actionAsyncStorage:0;function d(o,i,a){if(a===void 0)a=c.RedirectStatusCode.TemporaryRedirect;const s=Object.defineProperty(new Error(u.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});s.digest=u.REDIRECT_ERROR_CODE+";"+i+";"+o+";"+a+";";return s}function f(o,i){var a;i!=null?i:i=(l==null?void 0:(a=l.getStore())==null?void 0:a.isAction)?u.RedirectType.push:u.RedirectType.replace;throw d(o,i,c.RedirectStatusCode.TemporaryRedirect)}function p(o,i){if(i===void 0)i=u.RedirectType.replace;throw d(o,i,c.RedirectStatusCode.PermanentRedirect)}function h(o){if(!(0,u.isRedirectError)(o))return null;return o.digest.split(";").slice(2,-2).join(";")}function y(o){if(!(0,u.isRedirectError)(o)){throw Object.defineProperty(new Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:false,configurable:true})}return o.digest.split(";",2)[1]}function g(o){if(!(0,u.isRedirectError)(o)){throw Object.defineProperty(new Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:false,configurable:true})}return Number(o.digest.split(";").at(-2))}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},6926:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:true,get:function(){return h}});const s=u(a(1120));function c(o){if(typeof WeakMap!=="function")return null;var i=new WeakMap;var a=new WeakMap;return(c=function(o){return o?a:i})(o)}function u(o,i){if(!i&&o&&o.__esModule){return o}if(o===null||typeof o!=="object"&&typeof o!=="function"){return{default:o}}var a=c(i);if(a&&a.has(o)){return a.get(o)}var s={__proto__:null};var u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in o){if(l!=="default"&&Object.prototype.hasOwnProperty.call(o,l)){var d=u?Object.getOwnPropertyDescriptor(o,l):null;if(d&&(d.get||d.set)){Object.defineProperty(s,l,d)}else{s[l]=o[l]}}}s.default=o;if(a){a.set(o,s)}return s}const l={current:null};const d=typeof s.cache==="function"?s.cache:o=>o;const f=false?0:console.warn;const p=d(o=>{try{f(l.current)}finally{l.current=null}});function h(o){return function i(...a){const s=o(...a);if(false){var c}else{f(s)}}}},7017:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"addPathSuffix",{enumerable:true,get:function(){return c}});const s=a(8631);function c(o,i){if(!o.startsWith("/")||!i){return o}const{pathname:a,query:c,hash:u}=(0,s.parsePath)(o);return""+a+i+c+u}},7252:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"after",{enumerable:true,get:function(){return c}});const s=a(9294);function c(o){const i=s.workAsyncStorage.getStore();if(!i){throw Object.defineProperty(new Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:false,configurable:true})}const{afterContext:a}=i;return a.after(o)}},7348:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"addPathPrefix",{enumerable:true,get:function(){return c}});const s=a(8631);function c(o,i){if(!o.startsWith("/")||!i){return o}const{pathname:a,query:c,hash:u}=(0,s.parsePath)(o);return""+i+a+c+u}},7576:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{ReadonlyURLSearchParams:function(){return y},RedirectType:function(){return u.RedirectType},forbidden:function(){return d.forbidden},notFound:function(){return l.notFound},permanentRedirect:function(){return c.permanentRedirect},redirect:function(){return c.redirect},unauthorized:function(){return f.unauthorized},unstable_rethrow:function(){return p.unstable_rethrow}});const c=a(6897);const u=a(9026);const l=a(2765);const d=a(8976);const f=a(899);const p=a(163);class h extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class y extends URLSearchParams{append(){throw new h}delete(){throw new h}set(){throw new h}sort(){throw new h}}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},7625:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{METADATA_BOUNDARY_NAME:function(){return s},OUTLET_BOUNDARY_NAME:function(){return u},VIEWPORT_BOUNDARY_NAME:function(){return c}});const s="__next_metadata_boundary__";const c="__next_viewport_boundary__";const u="__next_outlet_boundary__"},7719:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{NEXT_PATCH_SYMBOL:function(){return b},createPatchedFetcher:function(){return S},patchFetch:function(){return R},validateRevalidate:function(){return _},validateTags:function(){return v}});const c=a(4823);const u=a(1289);const l=a(6143);const d=a(4971);const f=a(8388);const p=a(4436);const h=a(3365);const y=a(4523);const g=a(9169);const m="nodejs"==="edge";const b=Symbol.for("next-patch");function w(){return globalThis[b]===true}function _(o,i){try{let a=undefined;if(o===false){a=l.INFINITE_CACHE}else if(typeof o==="number"&&!isNaN(o)&&o>-1){a=o}else if(typeof o!=="undefined"){throw Object.defineProperty(new Error(`Invalid revalidate value "${o}" on "${i}", must be a non-negative number or false`),"__NEXT_ERROR_CODE",{value:"E179",enumerable:false,configurable:true})}return a}catch(o){if(o instanceof Error&&o.message.includes("Invalid revalidate")){throw o}return undefined}}function v(o,i){const a=[];const s=[];for(let c=0;c<o.length;c++){const u=o[c];if(typeof u!=="string"){s.push({tag:u,reason:"invalid type, must be a string"})}else if(u.length>l.NEXT_CACHE_TAG_MAX_LENGTH){s.push({tag:u,reason:`exceeded max length of ${l.NEXT_CACHE_TAG_MAX_LENGTH}`})}else{a.push(u)}if(a.length>l.NEXT_CACHE_TAG_MAX_ITEMS){console.warn(`Warning: exceeded max tag count for ${i}, dropped tags:`,o.slice(c).join(", "));break}}if(s.length>0){console.warn(`Warning: invalid tags passed to ${i}: `);for(const{tag:o,reason:i}of s){console.log(`tag: "${o}" ${i}`)}}return a}function E(o,i){var a;if(!o)return;if((a=o.requestEndedState)==null?void 0:a.ended)return;const s=(!!process.env.NEXT_DEBUG_BUILD||process.env.NEXT_SSG_FETCH_METRICS==="1")&&o.isStaticGeneration;const c="production"==="development";if(!s&&!c){return}o.fetchMetrics??=[];o.fetchMetrics.push({...i,end:performance.timeOrigin+performance.now(),idx:o.nextFetchId||0})}function S(o,{workAsyncStorage:i,workUnitAsyncStorage:a}){const s=async(s,p)=>{var b,w;let S;try{S=new URL(s instanceof Request?s.url:s);S.username="";S.password=""}catch{S=undefined}const R=(S==null?void 0:S.href)??"";const A=(p==null?void 0:(b=p.method)==null?void 0:b.toUpperCase())||"GET";const P=(p==null?void 0:(w=p.next)==null?void 0:w.internal)===true;const k=process.env.NEXT_OTEL_FETCH_DISABLED==="1";const O=P?undefined:performance.timeOrigin+performance.now();const T=i.getStore();const x=a.getStore();let C=x&&x.type==="prerender"?x.cacheSignal:null;if(C){C.beginRead()}const j=(0,u.getTracer)().trace(P?c.NextNodeServerSpan.internalFetch:c.AppRenderSpan.fetch,{hideSpan:k,kind:u.SpanKind.CLIENT,spanName:["fetch",A,R].filter(Boolean).join(" "),attributes:{"http.url":R,"http.method":A,"net.peer.name":S==null?void 0:S.hostname,"net.peer.port":(S==null?void 0:S.port)||undefined}},async()=>{var i;if(P){return o(s,p)}if(!T){return o(s,p)}if(T.isDraftMode){return o(s,p)}const a=s&&typeof s==="object"&&typeof s.method==="string";const c=o=>{const i=p==null?void 0:p[o];return i||(a?s[o]:null)};let u=undefined;const b=o=>{var i,c,u;return typeof(p==null?void 0:(i=p.next)==null?void 0:i[o])!=="undefined"?p==null?void 0:(c=p.next)==null?void 0:c[o]:a?(u=s.next)==null?void 0:u[o]:undefined};let w=b("revalidate");const S=v(b("tags")||[],`fetch ${s.toString()}`);const A=x&&(x.type==="cache"||x.type==="prerender"||x.type==="prerender-ppr"||x.type==="prerender-legacy")?x:undefined;if(A){if(Array.isArray(S)){const o=A.tags??(A.tags=[]);for(const i of S){if(!o.includes(i)){o.push(i)}}}}const k=x==null?void 0:x.implicitTags;const j=x&&x.type==="unstable-cache"?"force-no-store":T.fetchCache;const N=!!T.isUnstableNoStore;let I=c("cache");let D="";let U;if(typeof I==="string"&&typeof w!=="undefined"){const o=I==="force-cache"&&w===0||I==="no-store"&&(w>0||w===false);if(o){U=`Specified "cache: ${I}" and "revalidate: ${w}", only one should be specified.`;I=undefined;w=undefined}}const M=I==="no-cache"||I==="no-store"||j==="force-no-store"||j==="only-no-store";const L=!j&&!I&&!w&&T.forceDynamic;if(I==="force-cache"&&typeof w==="undefined"){w=false}else if((x==null?void 0:x.type)!=="cache"&&(M||L)){w=0}if(I==="no-cache"||I==="no-store"){D=`cache: ${I}`}u=_(w,T.route);const $=c("headers");const H=typeof($==null?void 0:$.get)==="function"?$:new Headers($||{});const W=H.get("authorization")||H.get("cookie");const K=!["get","head"].includes(((i=c("method"))==null?void 0:i.toLowerCase())||"get");const B=j==undefined&&(I==undefined||I==="default")&&w==undefined;const q=B&&!T.isPrerendering||(W||K)&&A&&A.revalidate===0;if(B&&x!==undefined&&x.type==="prerender"){if(C){C.endRead();C=null}return(0,f.makeHangingPromise)(x.renderSignal,"fetch()")}switch(j){case"force-no-store":{D="fetchCache = force-no-store";break}case"only-no-store":{if(I==="force-cache"||typeof u!=="undefined"&&u>0){throw Object.defineProperty(new Error(`cache: 'force-cache' used on fetch for ${R} with 'export const fetchCache = 'only-no-store'`),"__NEXT_ERROR_CODE",{value:"E448",enumerable:false,configurable:true})}D="fetchCache = only-no-store";break}case"only-cache":{if(I==="no-store"){throw Object.defineProperty(new Error(`cache: 'no-store' used on fetch for ${R} with 'export const fetchCache = 'only-cache'`),"__NEXT_ERROR_CODE",{value:"E521",enumerable:false,configurable:true})}break}case"force-cache":{if(typeof w==="undefined"||w===0){D="fetchCache = force-cache";u=l.INFINITE_CACHE}break}default:}if(typeof u==="undefined"){if(j==="default-cache"&&!N){u=l.INFINITE_CACHE;D="fetchCache = default-cache"}else if(j==="default-no-store"){u=0;D="fetchCache = default-no-store"}else if(N){u=0;D="noStore call"}else if(q){u=0;D="auto no cache"}else{D="auto cache";u=A?A.revalidate:l.INFINITE_CACHE}}else if(!D){D=`revalidate: ${u}`}if(!(T.forceStatic&&u===0)&&!q&&A&&u<A.revalidate){if(u===0){if(x&&x.type==="prerender"){if(C){C.endRead();C=null}return(0,f.makeHangingPromise)(x.renderSignal,"fetch()")}else{(0,d.markCurrentScopeAsDynamic)(T,x,`revalidate: 0 fetch ${s} ${T.route}`)}}if(A&&w===u){A.revalidate=u}}const G=typeof u==="number"&&u>0;let F;const{incrementalCache:J}=T;const V=(x==null?void 0:x.type)==="request"||(x==null?void 0:x.type)==="cache"?x:undefined;if(J&&(G||(V==null?void 0:V.serverComponentsHmrCache))){try{F=await J.generateCacheKey(R,a?s:p)}catch(o){console.error(`Failed to generate cache key for`,s)}}const z=T.nextFetchId??1;T.nextFetchId=z+1;let X=()=>Promise.resolve();const Y=async(i,c)=>{const d=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...i?[]:["signal"]];if(a){const o=s;const i={body:o._ogBody||o.body};for(const a of d){i[a]=o[a]}s=new Request(o.url,i)}else if(p){const{_ogBody:o,body:a,signal:s,...c}=p;p={...c,body:o||a,signal:i?undefined:s}}const f={...p,next:{...p==null?void 0:p.next,fetchType:"origin",fetchIdx:z}};return o(s,f).then(async o=>{if(!i&&O){E(T,{start:O,url:R,cacheReason:c||D,cacheStatus:u===0||c?"skip":"miss",cacheWarning:U,status:o.status,method:f.method||"GET"})}if(o.status===200&&J&&F&&(G||(V==null?void 0:V.serverComponentsHmrCache))){const i=u>=l.INFINITE_CACHE?l.CACHE_ONE_YEAR:u;if(x&&x.type==="prerender"){const a=await o.arrayBuffer();const s={headers:Object.fromEntries(o.headers.entries()),body:Buffer.from(a).toString("base64"),status:o.status,url:o.url};await J.set(F,{kind:h.CachedRouteKind.FETCH,data:s,revalidate:i},{fetchCache:true,fetchUrl:R,fetchIdx:z,tags:S});await X();return new Response(a,{headers:o.headers,status:o.status,statusText:o.statusText})}else{const[a,c]=(0,g.cloneResponse)(o);a.arrayBuffer().then(async o=>{var s;const c=Buffer.from(o);const u={headers:Object.fromEntries(a.headers.entries()),body:c.toString("base64"),status:a.status,url:a.url};V==null?void 0:(s=V.serverComponentsHmrCache)==null?void 0:s.set(F,u);if(G){await J.set(F,{kind:h.CachedRouteKind.FETCH,data:u,revalidate:i},{fetchCache:true,fetchUrl:R,fetchIdx:z,tags:S})}}).catch(o=>console.warn(`Failed to set fetch cache`,s,o)).finally(X);return c}}await X();return o}).catch(o=>{X();throw o})};let Z;let Q=false;let ee=false;if(F&&J){let o;if((V==null?void 0:V.isHmrRefresh)&&V.serverComponentsHmrCache){o=V.serverComponentsHmrCache.get(F);ee=true}if(G&&!o){X=await J.lock(F);const i=T.isOnDemandRevalidate?null:await J.get(F,{kind:h.IncrementalCacheKind.FETCH,revalidate:u,fetchUrl:R,fetchIdx:z,tags:S,softTags:k==null?void 0:k.tags});if(B){if(x&&x.type==="prerender"){await (0,y.waitAtLeastOneReactRenderTask)()}}if(i){await X()}else{Z="cache-control: no-cache (hard refresh)"}if((i==null?void 0:i.value)&&i.value.kind===h.CachedRouteKind.FETCH){if(T.isRevalidate&&i.isStale){Q=true}else{if(i.isStale){T.pendingRevalidates??={};if(!T.pendingRevalidates[F]){const o=Y(true).then(async o=>({body:await o.arrayBuffer(),headers:o.headers,status:o.status,statusText:o.statusText})).finally(()=>{T.pendingRevalidates??={};delete T.pendingRevalidates[F||""]});o.catch(console.error);T.pendingRevalidates[F]=o}}o=i.value.data}}}if(o){if(O){E(T,{start:O,url:R,cacheReason:D,cacheStatus:ee?"hmr":"hit",cacheWarning:U,status:o.status||200,method:(p==null?void 0:p.method)||"GET"})}const i=new Response(Buffer.from(o.body,"base64"),{headers:o.headers,status:o.status});Object.defineProperty(i,"url",{value:o.url});return i}}if(T.isStaticGeneration&&p&&typeof p==="object"){const{cache:o}=p;if(m)delete p.cache;if(o==="no-store"){if(x&&x.type==="prerender"){if(C){C.endRead();C=null}return(0,f.makeHangingPromise)(x.renderSignal,"fetch()")}else{(0,d.markCurrentScopeAsDynamic)(T,x,`no-store fetch ${s} ${T.route}`)}}const i="next"in p;const{next:a={}}=p;if(typeof a.revalidate==="number"&&A&&a.revalidate<A.revalidate){if(a.revalidate===0){if(x&&x.type==="prerender"){return(0,f.makeHangingPromise)(x.renderSignal,"fetch()")}else{(0,d.markCurrentScopeAsDynamic)(T,x,`revalidate: 0 fetch ${s} ${T.route}`)}}if(!T.forceStatic||a.revalidate!==0){A.revalidate=a.revalidate}}if(i)delete p.next}if(F&&Q){const o=F;T.pendingRevalidates??={};let i=T.pendingRevalidates[o];if(i){const o=await i;return new Response(o.body,{headers:o.headers,status:o.status,statusText:o.statusText})}const a=Y(true,Z).then(g.cloneResponse);i=a.then(async o=>{const i=o[0];return{body:await i.arrayBuffer(),headers:i.headers,status:i.status,statusText:i.statusText}}).finally(()=>{var i;if(!((i=T.pendingRevalidates)==null?void 0:i[o])){return}delete T.pendingRevalidates[o]});i.catch(()=>{});T.pendingRevalidates[o]=i;return a.then(o=>o[1])}else{return Y(false,Z)}});if(C){try{return await j}finally{if(C){C.endRead()}}}return j};s.__nextPatched=true;s.__nextGetStaticStore=()=>i;s._nextOriginalFetch=o;globalThis[b]=true;return s}function R(o){if(w())return;const i=(0,p.createDedupeFetch)(globalThis.fetch);globalThis.fetch=S(i,o)}},7778:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"default",{enumerable:true,get:function(){return u}});const s=a(7855);const c=a(2471);class u{static fromStatic(o){return new u(o,{metadata:{}})}constructor(o,{contentType:i,waitUntil:a,metadata:s}){this.response=o;this.contentType=i;this.metadata=s;this.waitUntil=a}assignMetadata(o){Object.assign(this.metadata,o)}get isNull(){return this.response===null}get isDynamic(){return typeof this.response!=="string"}toUnchunkedBuffer(o=false){if(this.response===null){throw Object.defineProperty(new Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:false,configurable:true})}if(typeof this.response!=="string"){if(!o){throw Object.defineProperty(new Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:false,configurable:true})}return(0,s.streamToBuffer)(this.readable)}return Buffer.from(this.response)}toUnchunkedString(o=false){if(this.response===null){throw Object.defineProperty(new Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:false,configurable:true})}if(typeof this.response!=="string"){if(!o){throw Object.defineProperty(new Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:false,configurable:true})}return(0,s.streamToString)(this.readable)}return this.response}get readable(){if(this.response===null){throw Object.defineProperty(new Error("Invariant: null responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E14",enumerable:false,configurable:true})}if(typeof this.response==="string"){throw Object.defineProperty(new Error("Invariant: static responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E151",enumerable:false,configurable:true})}if(Buffer.isBuffer(this.response)){return(0,s.streamFromBuffer)(this.response)}if(Array.isArray(this.response)){return(0,s.chainStreams)(...this.response)}return this.response}chain(o){if(this.response===null){throw Object.defineProperty(new Error("Invariant: response is null. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E258",enumerable:false,configurable:true})}let i;if(typeof this.response==="string"){i=[(0,s.streamFromString)(this.response)]}else if(Array.isArray(this.response)){i=this.response}else if(Buffer.isBuffer(this.response)){i=[(0,s.streamFromBuffer)(this.response)]}else{i=[this.response]}i.push(o);this.response=i}async pipeTo(o){try{await this.readable.pipeTo(o,{preventClose:true});if(this.waitUntil)await this.waitUntil;await o.close()}catch(i){if((0,c.isAbortError)(i)){await o.abort(i);return}throw i}}async pipeToNodeResponse(o){await (0,c.pipeToNodeResponse)(this.readable,o,this.waitUntil)}}},7853:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"formatNextPathnameInfo",{enumerable:true,get:function(){return d}});const s=a(2887);const c=a(7348);const u=a(7017);const l=a(9034);function d(o){let i=(0,l.addLocale)(o.pathname,o.locale,o.buildId?undefined:o.defaultLocale,o.ignorePrefix);if(o.buildId||!o.trailingSlash){i=(0,s.removeTrailingSlash)(i)}if(o.buildId){i=(0,u.addPathSuffix)((0,c.addPathPrefix)(i,"/_next/data/"+o.buildId),o.pathname==="/"?"index.json":".json")}i=(0,c.addPathPrefix)(i,o.basePath);return!o.buildId&&o.trailingSlash?!i.endsWith("/")?(0,u.addPathSuffix)(i,"/"):i:(0,s.removeTrailingSlash)(i)}},7855:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{chainStreams:function(){return m},continueDynamicHTMLResume:function(){return D},continueDynamicPrerender:function(){return N},continueFizzStream:function(){return j},continueStaticPrerender:function(){return I},createBufferedTransformStream:function(){return E},createDocumentClosingStream:function(){return U},createRootLayoutValidatorStream:function(){return x},renderToInitialFizzStream:function(){return S},streamFromBuffer:function(){return w},streamFromString:function(){return b},streamToBuffer:function(){return _},streamToString:function(){return v}});const c=a(1289);const u=a(4823);const l=a(366);const d=a(4523);const f=a(7866);const p=a(8684);const h=a(4113);function y(){}const g=new TextEncoder;function m(...o){if(o.length===0){throw Object.defineProperty(new Error("Invariant: chainStreams requires at least one stream"),"__NEXT_ERROR_CODE",{value:"E437",enumerable:false,configurable:true})}if(o.length===1){return o[0]}const{readable:i,writable:a}=new TransformStream;let s=o[0].pipeTo(a,{preventClose:true});let c=1;for(;c<o.length-1;c++){const i=o[c];s=s.then(()=>i.pipeTo(a,{preventClose:true}))}const u=o[c];s=s.then(()=>u.pipeTo(a));s.catch(y);return i}function b(o){return new ReadableStream({start(i){i.enqueue(g.encode(o));i.close()}})}function w(o){return new ReadableStream({start(i){i.enqueue(o);i.close()}})}async function _(o){const i=o.getReader();const a=[];while(true){const{done:o,value:s}=await i.read();if(o){break}a.push(s)}return Buffer.concat(a)}async function v(o,i){const a=new TextDecoder("utf-8",{fatal:true});let s="";for await(const c of o){if(i==null?void 0:i.aborted){return s}s+=a.decode(c,{stream:true})}s+=a.decode();return s}function E(){let o=[];let i=0;let a;const s=s=>{if(a)return;const c=new l.DetachedPromise;a=c;(0,d.scheduleImmediate)(()=>{try{const a=new Uint8Array(i);let c=0;for(let i=0;i<o.length;i++){const s=o[i];a.set(s,c);c+=s.byteLength}o.length=0;i=0;s.enqueue(a)}catch{}finally{a=undefined;c.resolve()}})};return new TransformStream({transform(a,c){o.push(a);i+=a.byteLength;s(c)},flush(){if(!a)return;return a.promise}})}function S({ReactDOMServer:o,element:i,streamOptions:a}){return(0,c.getTracer)().trace(u.AppRenderSpan.renderToReadableStream,async()=>o.renderToReadableStream(i,a))}function R(o){let i=false;let a=false;return new TransformStream({async transform(s,c){a=true;const u=await o();if(i){if(u){const o=g.encode(u);c.enqueue(o)}c.enqueue(s)}else{const o=(0,p.indexOfUint8Array)(s,f.ENCODED_TAGS.CLOSED.HEAD);if(o!==-1){if(u){const i=g.encode(u);const a=new Uint8Array(s.length+i.length);a.set(s.slice(0,o));a.set(i,o);a.set(s.slice(o),o+i.length);c.enqueue(a)}else{c.enqueue(s)}i=true}else{if(u){c.enqueue(g.encode(u))}c.enqueue(s);i=true}}},async flush(i){if(a){const a=await o();if(a){i.enqueue(g.encode(a))}}}})}function A(o){let i=false;let a;const s=i=>{const s=new l.DetachedPromise;a=s;(0,d.scheduleImmediate)(()=>{try{i.enqueue(g.encode(o))}catch{}finally{a=undefined;s.resolve()}})};return new TransformStream({transform(o,a){a.enqueue(o);if(i)return;i=true;s(a)},flush(s){if(a)return a.promise;if(i)return;s.enqueue(g.encode(o))}})}function P(o){let i=null;let a=false;async function s(s){if(i){return}const c=o.getReader();await (0,d.atLeastOneTask)();try{while(true){const{done:o,value:i}=await c.read();if(o){a=true;return}s.enqueue(i)}}catch(o){s.error(o)}}return new TransformStream({transform(o,a){a.enqueue(o);if(!i){i=s(a)}},flush(o){if(a){return}return i||s(o)}})}const k="</body></html>";function O(){let o=false;return new TransformStream({transform(i,a){if(o){return a.enqueue(i)}const s=(0,p.indexOfUint8Array)(i,f.ENCODED_TAGS.CLOSED.BODY_AND_HTML);if(s>-1){o=true;if(i.length===f.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length){return}const c=i.slice(0,s);a.enqueue(c);if(i.length>f.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length+s){const o=i.slice(s+f.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length);a.enqueue(o)}}else{a.enqueue(i)}},flush(o){o.enqueue(f.ENCODED_TAGS.CLOSED.BODY_AND_HTML)}})}function T(){return new TransformStream({transform(o,i){if((0,p.isEquivalentUint8Arrays)(o,f.ENCODED_TAGS.CLOSED.BODY_AND_HTML)||(0,p.isEquivalentUint8Arrays)(o,f.ENCODED_TAGS.CLOSED.BODY)||(0,p.isEquivalentUint8Arrays)(o,f.ENCODED_TAGS.CLOSED.HTML)){return}o=(0,p.removeFromUint8Array)(o,f.ENCODED_TAGS.CLOSED.BODY);o=(0,p.removeFromUint8Array)(o,f.ENCODED_TAGS.CLOSED.HTML);i.enqueue(o)}})}function x(){let o=false;let i=false;return new TransformStream({async transform(a,s){if(!o&&(0,p.indexOfUint8Array)(a,f.ENCODED_TAGS.OPENING.HTML)>-1){o=true}if(!i&&(0,p.indexOfUint8Array)(a,f.ENCODED_TAGS.OPENING.BODY)>-1){i=true}s.enqueue(a)},flush(a){const s=[];if(!o)s.push("html");if(!i)s.push("body");if(!s.length)return;a.enqueue(g.encode(`<html id="__next_error__">
            <template
              data-next-error-message="Missing ${s.map(o=>`<${o}>`).join(s.length>1?" and ":"")} tags in the root layout.
Read more at https://nextjs.org/docs/messages/missing-root-layout-tags""
              data-next-error-digest="${h.MISSING_ROOT_TAGS_ERROR}"
              data-next-error-stack=""
            ></template>
          `))}})}function C(o,i){let a=o;for(const o of i){if(!o)continue;a=a.pipeThrough(o)}return a}async function j(o,{suffix:i,inlinedDataStream:a,isStaticGeneration:s,getServerInsertedHTML:c,getServerInsertedMetadata:u,validateRootLayout:l}){const d=i?i.split(k,1)[0]:null;if(s&&"allReady"in o){await o.allReady}return C(o,[E(),R(u),d!=null&&d.length>0?A(d):null,a?P(a):null,l?x():null,O(),R(c)])}async function N(o,{getServerInsertedHTML:i,getServerInsertedMetadata:a}){return o.pipeThrough(E()).pipeThrough(T()).pipeThrough(R(i)).pipeThrough(R(a))}async function I(o,{inlinedDataStream:i,getServerInsertedHTML:a,getServerInsertedMetadata:s}){return o.pipeThrough(E()).pipeThrough(R(a)).pipeThrough(R(s)).pipeThrough(P(i)).pipeThrough(O())}async function D(o,{inlinedDataStream:i,getServerInsertedHTML:a,getServerInsertedMetadata:s}){return o.pipeThrough(E()).pipeThrough(R(a)).pipeThrough(R(s)).pipeThrough(P(i)).pipeThrough(O())}function U(){return b(k)}},7866:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"ENCODED_TAGS",{enumerable:true,get:function(){return a}});const a={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}}},7912:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{fromNodeOutgoingHttpHeaders:function(){return u},normalizeNextQueryParam:function(){return p},splitCookiesString:function(){return l},toNodeOutgoingHttpHeaders:function(){return d},validateURL:function(){return f}});const c=a(6143);function u(o){const i=new Headers;for(let[a,s]of Object.entries(o)){const o=Array.isArray(s)?s:[s];for(let s of o){if(typeof s==="undefined")continue;if(typeof s==="number"){s=s.toString()}i.append(a,s)}}return i}function l(o){var i=[];var a=0;var s;var c;var u;var l;var d;function f(){while(a<o.length&&/\s/.test(o.charAt(a))){a+=1}return a<o.length}function p(){c=o.charAt(a);return c!=="="&&c!==";"&&c!==","}while(a<o.length){s=a;d=false;while(f()){c=o.charAt(a);if(c===","){u=a;a+=1;f();l=a;while(a<o.length&&p()){a+=1}if(a<o.length&&o.charAt(a)==="="){d=true;a=l;i.push(o.substring(s,u));s=a}else{a=u+1}}else{a+=1}}if(!d||a>=o.length){i.push(o.substring(s,o.length))}}return i}function d(o){const i={};const a=[];if(o){for(const[s,c]of o.entries()){if(s.toLowerCase()==="set-cookie"){a.push(...l(c));i[s]=a.length===1?a[0]:a}else{i[s]=c}}}return i}function f(o){try{return String(new URL(String(o)))}catch(i){throw Object.defineProperty(new Error(`URL is malformed "${String(o)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:i}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:false,configurable:true})}}function p(o){const i=[c.NEXT_QUERY_PARAM_PREFIX,c.NEXT_INTERCEPTION_MARKER_PREFIX];for(const a of i){if(o!==a&&o.startsWith(a)){return o.substring(a.length)}}return null}},8088:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"RouteKind",{enumerable:true,get:function(){return a}});var a=function(o){o["PAGES"]="PAGES";o["PAGES_API"]="PAGES_API";o["APP_PAGE"]="APP_PAGE";o["APP_ROUTE"]="APP_ROUTE";o["IMAGE"]="IMAGE";return o}({})},8388:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{isHangingPromiseRejectionError:function(){return s},makeHangingPromise:function(){return d}});function s(o){if(typeof o!=="object"||o===null||!("digest"in o)){return false}return o.digest===c}const c="HANGING_PROMISE_REJECTION";class u extends Error{constructor(o){super(`During prerendering, ${o} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${o} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=o,this.digest=c}}const l=new WeakMap;function d(o,i){if(o.aborted){return Promise.reject(new u(i))}else{const a=new Promise((a,s)=>{const c=s.bind(null,new u(i));let d=l.get(o);if(d){d.push(c)}else{const i=[c];l.set(o,i);o.addEventListener("abort",()=>{for(let o=0;o<i.length;o++){i[o]()}},{once:true})}});a.catch(f);return a}}function f(){}},8479:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{DynamicServerError:function(){return c},isDynamicServerError:function(){return u}});const s="DYNAMIC_SERVER_USAGE";class c extends Error{constructor(o){super("Dynamic server usage: "+o),this.description=o,this.digest=s}}function u(o){if(typeof o!=="object"||o===null||!("digest"in o)||typeof o.digest!=="string"){return false}return o.digest===s}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},8631:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"parsePath",{enumerable:true,get:function(){return a}});function a(o){const i=o.indexOf("#");const a=o.indexOf("?");const s=a>-1&&(i<0||a<i);if(s||i>-1){return{pathname:o.substring(0,s?a:i),query:s?o.substring(a,i>-1?i:undefined):"",hash:i>-1?o.slice(i):""}}return{pathname:o,query:"",hash:""}}},8684:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{indexOfUint8Array:function(){return s},isEquivalentUint8Arrays:function(){return c},removeFromUint8Array:function(){return u}});function s(o,i){if(i.length===0)return 0;if(o.length===0||i.length>o.length)return-1;for(let a=0;a<=o.length-i.length;a++){let s=true;for(let c=0;c<i.length;c++){if(o[a+c]!==i[c]){s=false;break}}if(s){return a}}return-1}function c(o,i){if(o.length!==i.length)return false;for(let a=0;a<o.length;a++){if(o[a]!==i[a])return false}return true}function u(o,i){const a=s(o,i);if(a===0)return o.subarray(i.length);if(a>-1){const s=new Uint8Array(o.length-i.length);s.set(o.slice(0,a));s.set(o.slice(a+i.length),a);return s}else{return o}}},8704:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}a(i,{HTTPAccessErrorStatus:function(){return s},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return u},getAccessFallbackErrorTypeByStatus:function(){return f},getAccessFallbackHTTPStatus:function(){return d},isHTTPAccessFallbackError:function(){return l}});const s={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401};const c=new Set(Object.values(s));const u="NEXT_HTTP_ERROR_FALLBACK";function l(o){if(typeof o!=="object"||o===null||!("digest"in o)||typeof o.digest!=="string"){return false}const[i,a]=o.digest.split(";");return i===u&&c.has(Number(a))}function d(o){const i=o.digest.split(";")[1];return Number(i)}function f(o){switch(o){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},8719:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{isRequestAPICallableInsideAfter:function(){return p},throwForSearchParamsAccessInUseCache:function(){return f},throwWithStaticGenerationBailoutError:function(){return l},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return d}});const c=a(23);const u=a(3295);function l(o,i){throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${o} couldn't be rendered statically because it used ${i}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:false,configurable:true})}function d(o,i){throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${o} with \`dynamic = "error"\` couldn't be rendered statically because it used ${i}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:false,configurable:true})}function f(o){const i=Object.defineProperty(new Error(`Route ${o.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:false,configurable:true});o.invalidUsageError??=i;throw i}function p(){const o=u.afterTaskAsyncStorage.getStore();return(o==null?void 0:o.rootTaskSpawnPhase)==="action"}},8737:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"Batcher",{enumerable:true,get:function(){return c}});const s=a(366);class c{constructor(o,i=o=>o()){this.cacheKeyFn=o;this.schedulerFn=i;this.pending=new Map}static create(o){return new c(o==null?void 0:o.cacheKeyFn,o==null?void 0:o.schedulerFn)}async batch(o,i){const a=this.cacheKeyFn?await this.cacheKeyFn(o):o;if(a===null){return i(a,Promise.resolve)}const c=this.pending.get(a);if(c)return c;const{promise:u,resolve:l,reject:d}=new s.DetachedPromise;this.pending.set(a,u);this.schedulerFn(async()=>{try{const o=await i(a,l);l(o)}catch(o){d(o)}finally{this.pending.delete(a)}});return u}}},8976:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"forbidden",{enumerable:true,get:function(){return u}});const s=a(8704);const c=""+s.HTTP_ERROR_FALLBACK_ERROR_CODE+";403";function u(){if(true){throw Object.defineProperty(new Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:false,configurable:true})}const o=Object.defineProperty(new Error(c),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});o.digest=c;throw o}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},9026:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{REDIRECT_ERROR_CODE:function(){return u},RedirectType:function(){return l},isRedirectError:function(){return d}});const c=a(2836);const u="NEXT_REDIRECT";var l=function(o){o["push"]="push";o["replace"]="replace";return o}({});function d(o){if(typeof o!=="object"||o===null||!("digest"in o)||typeof o.digest!=="string"){return false}const i=o.digest.split(";");const[a,s]=i;const l=i.slice(2,-2).join(";");const d=i.at(-2);const f=Number(d);return a===u&&(s==="replace"||s==="push")&&typeof l==="string"&&!isNaN(f)&&f in c.RedirectStatusCode}if((typeof i.default==="function"||typeof i.default==="object"&&i.default!==null)&&typeof i.default.__esModule==="undefined"){Object.defineProperty(i.default,"__esModule",{value:true});Object.assign(i.default,i);o.exports=i.default}},9034:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"addLocale",{enumerable:true,get:function(){return u}});const s=a(7348);const c=a(2829);function u(o,i,a,u){if(!i||i===a)return o;const l=o.toLowerCase();if(!u){if((0,c.pathHasPrefix)(l,"/api"))return o;if((0,c.pathHasPrefix)(l,"/"+i.toLowerCase()))return o}return(0,s.addPathPrefix)(o,"/"+i)}},9098:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"isThenable",{enumerable:true,get:function(){return a}});function a(o){return o!==null&&typeof o==="object"&&"then"in o&&typeof o.then==="function"}},9169:(o,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"cloneResponse",{enumerable:true,get:function(){return a}});function a(o){if(!o.body){return[o,o]}const[i,a]=o.body.tee();const s=new Response(i,{status:o.status,statusText:o.statusText,headers:o.headers});Object.defineProperty(s,"url",{value:o.url});const c=new Response(a,{status:o.status,statusText:o.statusText,headers:o.headers});Object.defineProperty(c,"url",{value:o.url});return[s,c]}},9229:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"removePathPrefix",{enumerable:true,get:function(){return c}});const s=a(2829);function c(o,i){if(!(0,s.pathHasPrefix)(o,i)){return o}const a=o.slice(i.length);if(a.startsWith("/")){return a}return"/"+a}},9859:(o,i,a)=>{"use strict";a.d(i,{Ay:()=>ut});var s={};a.r(s);a.d(s,{q:()=>nk,l:()=>nx});var c=undefined&&undefined.__classPrivateFieldSet||function(o,i,a,s,c){if(s==="m")throw new TypeError("Private method is not writable");if(s==="a"&&!c)throw new TypeError("Private accessor was defined without a setter");if(typeof i==="function"?o!==i||!c:!i.has(o))throw new TypeError("Cannot write private member to an object whose class did not declare it");return s==="a"?c.call(o,a):c?c.value=a:i.set(o,a),a};var u=undefined&&undefined.__classPrivateFieldGet||function(o,i,a,s){if(a==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof i==="function"?o!==i||!s:!i.has(o))throw new TypeError("Cannot read private member from an object whose class did not declare it");return a==="m"?s:a==="a"?s.call(o):s?s.value:i.get(o)};var l,d,f,p,h,y;const g=4096;const m=160;const b=g-m;function w(o){const i=o?"__Secure-":"";return{sessionToken:{name:`${i}authjs.session-token`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},callbackUrl:{name:`${i}authjs.callback-url`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},csrfToken:{name:`${o?"__Host-":""}authjs.csrf-token`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},pkceCodeVerifier:{name:`${i}authjs.pkce.code_verifier`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o,maxAge:60*15}},state:{name:`${i}authjs.state`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o,maxAge:60*15}},nonce:{name:`${i}authjs.nonce`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},webauthnChallenge:{name:`${i}authjs.challenge`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o,maxAge:60*15}}}}class _{constructor(o,i,a){l.add(this);d.set(this,{});f.set(this,void 0);p.set(this,void 0);c(this,p,a,"f");c(this,f,o,"f");if(!i)return;const{name:s}=o;for(const[o,a]of Object.entries(i)){if(!o.startsWith(s)||!a)continue;u(this,d,"f")[o]=a}}get value(){const o=Object.keys(u(this,d,"f")).sort((o,i)=>{const a=parseInt(o.split(".").pop()||"0");const s=parseInt(i.split(".").pop()||"0");return a-s});return o.map(o=>u(this,d,"f")[o]).join("")}chunk(o,i){const a=u(this,l,"m",y).call(this);const s=u(this,l,"m",h).call(this,{name:u(this,f,"f").name,value:o,options:{...u(this,f,"f").options,...i}});for(const o of s){a[o.name]=o}return Object.values(a)}clean(){return Object.values(u(this,l,"m",y).call(this))}}d=new WeakMap,f=new WeakMap,p=new WeakMap,l=new WeakSet,h=function o(o){const i=Math.ceil(o.value.length/b);if(i===1){u(this,d,"f")[o.name]=o.value;return[o]}const a=[];for(let s=0;s<i;s++){const i=`${o.name}.${s}`;const c=o.value.substr(s*b,b);a.push({...o,name:i,value:c});u(this,d,"f")[i]=c}u(this,p,"f").debug("CHUNKING_SESSION_COOKIE",{message:`Session cookie exceeds allowed ${g} bytes.`,emptyCookieSize:m,valueSize:o.value.length,chunks:a.map(o=>o.value.length+m)});return a},y=function o(){const o={};for(const i in u(this,d,"f")){delete u(this,d,"f")?.[i];o[i]={name:i,value:"",options:{...u(this,f,"f").options,maxAge:0}}}return o};class v extends Error{constructor(o,i){if(o instanceof Error){super(undefined,{cause:{err:o,...o.cause,...i}})}else if(typeof o==="string"){if(i instanceof Error){i={err:i,...i.cause}}super(o,i)}else{super(undefined,o)}this.name=this.constructor.name;this.type=this.constructor.type??"AuthError";this.kind=this.constructor.kind??"error";Error.captureStackTrace?.(this,this.constructor);const a=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${a}`}}class E extends v{}E.kind="signIn";class S extends v{}S.type="AdapterError";class R extends v{}R.type="AccessDenied";class A extends v{}A.type="CallbackRouteError";class P extends v{}P.type="ErrorPageLoop";class k extends v{}k.type="EventError";class O extends v{}O.type="InvalidCallbackUrl";class T extends E{constructor(){super(...arguments);this.code="credentials"}}T.type="CredentialsSignin";class x extends v{}x.type="InvalidEndpoints";class C extends v{}C.type="InvalidCheck";class j extends v{}j.type="JWTSessionError";class N extends v{}N.type="MissingAdapter";class I extends v{}I.type="MissingAdapterMethods";class D extends v{}D.type="MissingAuthorize";class U extends v{}U.type="MissingSecret";class M extends E{}M.type="OAuthAccountNotLinked";class L extends E{}L.type="OAuthCallbackError";class $ extends v{}$.type="OAuthProfileParseError";class H extends v{}H.type="SessionTokenError";class W extends E{}W.type="OAuthSignInError";class K extends E{}K.type="EmailSignInError";class B extends v{}B.type="SignOutError";class q extends v{}q.type="UnknownAction";class G extends v{}G.type="UnsupportedStrategy";class F extends v{}F.type="InvalidProvider";class J extends v{}J.type="UntrustedHost";class V extends v{}V.type="Verification";class z extends E{}z.type="MissingCSRF";const X=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function Y(o){if(o instanceof v)return X.has(o.type);return false}class Z extends v{}Z.type="DuplicateConditionalUI";class Q extends v{}Q.type="MissingWebAuthnAutocomplete";class ee extends v{}ee.type="WebAuthnVerificationError";class et extends E{}et.type="AccountNotLinked";class en extends v{}en.type="ExperimentalFeatureNotEnabled";let er=false;function eo(o,i){try{return/^https?:/.test(new URL(o,o.startsWith("/")?i:undefined).protocol)}catch{return false}}function ei(o){return/^v\d+(?:\.\d+){0,2}$/.test(o)}let ea=false;let es=false;let ec=false;const eu=["createVerificationToken","useVerificationToken","getUserByEmail"];const el=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"];const ed=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];function ef(o,i){const{url:a}=o;const s=[];if(!er&&i.debug)s.push("debug-enabled");if(!i.trustHost){return new J(`Host must be trusted. URL was: ${o.url}`)}if(!i.secret?.length){return new U("Please define a `secret`")}const c=o.query?.callbackUrl;if(c&&!eo(c,a.origin)){return new O(`Invalid callback URL. Received: ${c}`)}const{callbackUrl:u}=w(i.useSecureCookies??a.protocol==="https:");const l=o.cookies?.[i.cookies?.callbackUrl?.name??u.name];if(l&&!eo(l,a.origin)){return new O(`Invalid callback URL. Received: ${l}`)}let d=false;for(const o of i.providers){const i=typeof o==="function"?o():o;if((i.type==="oauth"||i.type==="oidc")&&!(i.issuer??i.options?.issuer)){const{authorization:o,token:a,userinfo:s}=i;let c;if(typeof o!=="string"&&!o?.url)c="authorization";else if(typeof a!=="string"&&!a?.url)c="token";else if(typeof s!=="string"&&!s?.url)c="userinfo";if(c){return new x(`Provider "${i.id}" is missing both \`issuer\` and \`${c}\` endpoint config. At least one of them is required`)}}if(i.type==="credentials")ea=true;else if(i.type==="email")es=true;else if(i.type==="webauthn"){ec=true;if(i.simpleWebAuthnBrowserVersion&&!ei(i.simpleWebAuthnBrowserVersion)){return new v(`Invalid provider config for "${i.id}": simpleWebAuthnBrowserVersion "${i.simpleWebAuthnBrowserVersion}" must be a valid semver string.`)}if(i.enableConditionalUI){if(d){return new Z(`Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time`)}d=true;const o=Object.values(i.formFields).some(o=>o.autocomplete&&o.autocomplete.toString().indexOf("webauthn")>-1);if(!o){return new Q(`Provider "${i.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}}if(ea){const o=i.session?.strategy==="database";const a=!i.providers.some(o=>(typeof o==="function"?o():o).type!=="credentials");if(o&&a){return new G("Signing in with credentials only supported if JWT strategy is enabled")}const s=i.providers.some(o=>{const i=typeof o==="function"?o():o;return i.type==="credentials"&&!i.authorize});if(s){return new D("Must define an authorize() handler to use credentials authentication provider")}}const{adapter:f,session:p}=i;const h=[];if(es||p?.strategy==="database"||!p?.strategy&&f){if(es){if(!f)return new N("Email login requires an adapter");h.push(...eu)}else{if(!f)return new N("Database session requires an adapter");h.push(...el)}}if(ec){if(i.experimental?.enableWebAuthn){s.push("experimental-webauthn")}else{return new en("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config")}if(!f)return new N("WebAuthn requires an adapter");h.push(...ed)}if(f){const o=h.filter(o=>!(o in f));if(o.length){return new I(`Required adapter methods were missing: ${o.join(", ")}`)}}if(!er)er=true;return s}var ep=a(5511);const eh=(o,i,a,s,c)=>{const u=parseInt(o.substr(3),10)>>3||20;const l=(0,ep.createHmac)(o,a.byteLength?a:new Uint8Array(u)).update(i).digest();const d=Math.ceil(c/u);const f=new Uint8Array(u*d+s.byteLength+1);let p=0;let h=0;for(let i=1;i<=d;i++){f.set(s,h);f[h+s.byteLength]=i;f.set((0,ep.createHmac)(o,l).update(f.subarray(p,h+s.byteLength+1)).digest(),h);p=h;h+=u}return f.slice(0,c)};let ey;if(typeof ep.hkdf==="function"&&!process.versions.electron){ey=async(...o)=>new Promise((i,a)=>{ep.hkdf(...o,(o,s)=>{if(o)a(o);else i(new Uint8Array(s))})})}const eg=async(o,i,a,s,c)=>(ey||eh)(o,i,a,s,c);function em(o){switch(o){case"sha256":case"sha384":case"sha512":case"sha1":return o;default:throw new TypeError('unsupported "digest" value')}}function eb(o,i){if(typeof o==="string")return new TextEncoder().encode(o);if(!(o instanceof Uint8Array))throw new TypeError(`"${i}"" must be an instance of Uint8Array or a string`);return o}function ew(o){const i=eb(o,"ikm");if(!i.byteLength)throw new TypeError(`"ikm" must be at least one byte in length`);return i}function e_(o){const i=eb(o,"info");if(i.byteLength>1024){throw TypeError('"info" must not contain more than 1024 bytes')}return i}function ev(o,i){if(typeof o!=="number"||!Number.isInteger(o)||o<1){throw new TypeError('"keylen" must be a positive integer')}const a=parseInt(i.substr(3),10)>>3||20;if(o>255*a){throw new TypeError('"keylen" too large')}return o}async function eE(o,i,a,s,c){return eg(em(o),ew(i),eb(a,"salt"),e_(s),ev(c,o))};const eS=async(o,i)=>{const a=`SHA-${o.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(a,i))};const eR=new TextEncoder;const eA=new TextDecoder;const eP=2**32;function ek(...o){const i=o.reduce((o,{length:i})=>o+i,0);const a=new Uint8Array(i);let s=0;for(const i of o){a.set(i,s);s+=i.length}return a}function eO(o,i,a){if(i<0||i>=eP){throw new RangeError(`value must be >= 0 and <= ${eP-1}. Received ${i}`)}o.set([i>>>24,i>>>16,i>>>8,i&255],a)}function eT(o){const i=Math.floor(o/eP);const a=o%eP;const s=new Uint8Array(8);eO(s,i,0);eO(s,a,4);return s}function ex(o){const i=new Uint8Array(4);eO(i,o);return i};function eC(o){if(Uint8Array.prototype.toBase64){return o.toBase64()}const i=32768;const a=[];for(let s=0;s<o.length;s+=i){a.push(String.fromCharCode.apply(null,o.subarray(s,s+i)))}return btoa(a.join(""))}function ej(o){if(Uint8Array.fromBase64){return Uint8Array.fromBase64(o)}const i=atob(o);const a=new Uint8Array(i.length);for(let o=0;o<i.length;o++){a[o]=i.charCodeAt(o)}return a};function eN(o){if(Uint8Array.fromBase64){return Uint8Array.fromBase64(typeof o==="string"?o:eA.decode(o),{alphabet:"base64url"})}let i=o;if(i instanceof Uint8Array){i=eA.decode(i)}i=i.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return ej(i)}catch{throw new TypeError("The input to be decoded is not correctly encoded.")}}function eI(o){let i=o;if(typeof i==="string"){i=eR.encode(i)}if(Uint8Array.prototype.toBase64){return i.toBase64({alphabet:"base64url",omitPadding:true})}return eC(i).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")};class eD extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(o,i){super(o,i);this.name=this.constructor.name;Error.captureStackTrace?.(this,this.constructor)}}class eU extends eD{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(o,i,a="unspecified",s="unspecified"){super(o,{cause:{claim:a,reason:s,payload:i}});this.claim=a;this.reason=s;this.payload=i}}class eM extends eD{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(o,i,a="unspecified",s="unspecified"){super(o,{cause:{claim:a,reason:s,payload:i}});this.claim=a;this.reason=s;this.payload=i}}class eL extends eD{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class e$ extends eD{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class eH extends eD{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(o="decryption operation failed",i){super(o,i)}}class eW extends eD{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eK extends(null&&eD){static code=null&&"ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class eB extends eD{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eq extends eD{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eG extends(null&&eD){static code=null&&"ERR_JWKS_INVALID";code="ERR_JWKS_INVALID"}class eF extends eD{static code="ERR_JWKS_NO_MATCHING_KEY";code="ERR_JWKS_NO_MATCHING_KEY";constructor(o="no applicable key found in the JSON Web Key Set",i){super(o,i)}}class eJ extends eD{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(o="multiple matching keys found in the JSON Web Key Set",i){super(o,i)}}class eV extends eD{static code="ERR_JWKS_TIMEOUT";code="ERR_JWKS_TIMEOUT";constructor(o="request timed out",i){super(o,i)}}class ez extends eD{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(o="signature verification failed",i){super(o,i)}};function eX(o){if(!eY(o)){throw new Error("CryptoKey instance expected")}}function eY(o){return o?.[Symbol.toStringTag]==="CryptoKey"}function eZ(o){return o?.[Symbol.toStringTag]==="KeyObject"}const eQ=o=>{return eY(o)||eZ(o)};function e0(o){return typeof o==="object"&&o!==null}const e1=o=>{if(!e0(o)||Object.prototype.toString.call(o)!=="[object Object]"){return false}if(Object.getPrototypeOf(o)===null){return true}let i=o;while(Object.getPrototypeOf(i)!==null){i=Object.getPrototypeOf(i)}return Object.getPrototypeOf(o)===i};function e2(o){return e1(o)&&typeof o.kty==="string"}function e3(o){return o.kty!=="oct"&&typeof o.d==="string"}function e6(o){return o.kty!=="oct"&&typeof o.d==="undefined"}function e8(o){return o.kty==="oct"&&typeof o.k==="string"};function e5(o,i,...a){a=a.filter(Boolean);if(a.length>2){const i=a.pop();o+=`one of type ${a.join(", ")}, or ${i}.`}else if(a.length===2){o+=`one of type ${a[0]} or ${a[1]}.`}else{o+=`of type ${a[0]}.`}if(i==null){o+=` Received ${i}`}else if(typeof i==="function"&&i.name){o+=` Received function ${i.name}`}else if(typeof i==="object"&&i!=null){if(i.constructor?.name){o+=` Received an instance of ${i.constructor.name}`}}return o}const e4=(o,...i)=>{return e5("Key must be ",o,...i)};function e9(o,i,...a){return e5(`Key for the ${o} algorithm must be `,i,...a)};async function e7(o){if(eZ(o)){if(o.type==="secret"){o=o.export()}else{return o.export({format:"jwk"})}}if(o instanceof Uint8Array){return{kty:"oct",k:eI(o)}}if(!eY(o)){throw new TypeError(e4(o,"CryptoKey","KeyObject","Uint8Array"))}if(!o.extractable){throw new TypeError("non-extractable CryptoKey cannot be exported as a JWK")}const{ext:i,key_ops:a,alg:s,use:c,...u}=await crypto.subtle.exportKey("jwk",o);return u};async function te(o){return exportPublic(o)}async function tt(o){return exportPrivate(o)}async function tn(o){return e7(o)};const tr=(o,i)=>{if(typeof o!=="string"||!o){throw new eq(`${i} missing or invalid`)}};async function to(o,i){let a;if(e2(o)){a=o}else if(eQ(o)){a=await tn(o)}else{throw new TypeError(e4(o,"CryptoKey","KeyObject","JSON Web Key"))}i??="sha256";if(i!=="sha256"&&i!=="sha384"&&i!=="sha512"){throw new TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"')}let s;switch(a.kty){case"EC":tr(a.crv,'"crv" (Curve) Parameter');tr(a.x,'"x" (X Coordinate) Parameter');tr(a.y,'"y" (Y Coordinate) Parameter');s={crv:a.crv,kty:a.kty,x:a.x,y:a.y};break;case"OKP":tr(a.crv,'"crv" (Subtype of Key Pair) Parameter');tr(a.x,'"x" (Public Key) Parameter');s={crv:a.crv,kty:a.kty,x:a.x};break;case"RSA":tr(a.e,'"e" (Exponent) Parameter');tr(a.n,'"n" (Modulus) Parameter');s={e:a.e,kty:a.kty,n:a.n};break;case"oct":tr(a.k,'"k" (Key Value) Parameter');s={k:a.k,kty:a.kty};break;default:throw new e$('"kty" (Key Type) Parameter missing or unsupported')}const c=eR.encode(JSON.stringify(s));return eI(await eS(i,c))}async function ti(o,i){i??="sha256";const a=await to(o,i);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${i.slice(-3)}:${a}`};const ta=Symbol();function ts(o){switch(o){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new e$(`Unsupported JWE Algorithm: ${o}`)}}const tc=o=>crypto.getRandomValues(new Uint8Array(ts(o)>>3));const tu=(o,i)=>{if(i.length<<3!==ts(o)){throw new eW("Invalid Initialization Vector length")}};const tl=(o,i)=>{const a=o.byteLength<<3;if(a!==i){throw new eW(`Invalid Content Encryption Key length. Expected ${i} bits, got ${a} bits`)}};function td(o,i="algorithm.name"){return new TypeError(`CryptoKey does not support this operation, its ${i} must be ${o}`)}function tf(o,i){return o.name===i}function tp(o){return parseInt(o.name.slice(4),10)}function th(o){switch(o){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw new Error("unreachable")}}function ty(o,i){if(i&&!o.usages.includes(i)){throw new TypeError(`CryptoKey does not support this operation, its usages must include ${i}.`)}}function tg(o,i,a){switch(i){case"HS256":case"HS384":case"HS512":{if(!tf(o.algorithm,"HMAC"))throw td("HMAC");const a=parseInt(i.slice(2),10);const s=tp(o.algorithm.hash);if(s!==a)throw td(`SHA-${a}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!tf(o.algorithm,"RSASSA-PKCS1-v1_5"))throw td("RSASSA-PKCS1-v1_5");const a=parseInt(i.slice(2),10);const s=tp(o.algorithm.hash);if(s!==a)throw td(`SHA-${a}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!tf(o.algorithm,"RSA-PSS"))throw td("RSA-PSS");const a=parseInt(i.slice(2),10);const s=tp(o.algorithm.hash);if(s!==a)throw td(`SHA-${a}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":{if(!tf(o.algorithm,"Ed25519"))throw td("Ed25519");break}case"ES256":case"ES384":case"ES512":{if(!tf(o.algorithm,"ECDSA"))throw td("ECDSA");const a=th(i);const s=o.algorithm.namedCurve;if(s!==a)throw td(a,"algorithm.namedCurve");break}default:throw new TypeError("CryptoKey does not support this operation")}ty(o,a)}function tm(o,i,a){switch(i){case"A128GCM":case"A192GCM":case"A256GCM":{if(!tf(o.algorithm,"AES-GCM"))throw td("AES-GCM");const a=parseInt(i.slice(1,4),10);const s=o.algorithm.length;if(s!==a)throw td(a,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!tf(o.algorithm,"AES-KW"))throw td("AES-KW");const a=parseInt(i.slice(1,4),10);const s=o.algorithm.length;if(s!==a)throw td(a,"algorithm.length");break}case"ECDH":{switch(o.algorithm.name){case"ECDH":case"X25519":break;default:throw td("ECDH or X25519")}break}case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!tf(o.algorithm,"PBKDF2"))throw td("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!tf(o.algorithm,"RSA-OAEP"))throw td("RSA-OAEP");const a=parseInt(i.slice(9),10)||1;const s=tp(o.algorithm.hash);if(s!==a)throw td(`SHA-${a}`,"algorithm.hash");break}default:throw new TypeError("CryptoKey does not support this operation")}ty(o,a)};async function tb(o,i,a,s,c){if(!(a instanceof Uint8Array)){throw new TypeError(e4(a,"Uint8Array"))}const u=parseInt(o.slice(1,4),10);const l=await crypto.subtle.importKey("raw",a.subarray(u>>3),"AES-CBC",false,["encrypt"]);const d=await crypto.subtle.importKey("raw",a.subarray(0,u>>3),{hash:`SHA-${u<<1}`,name:"HMAC"},false,["sign"]);const f=new Uint8Array(await crypto.subtle.encrypt({iv:s,name:"AES-CBC"},l,i));const p=ek(c,s,f,eT(c.length<<3));const h=new Uint8Array((await crypto.subtle.sign("HMAC",d,p)).slice(0,u>>3));return{ciphertext:f,tag:h,iv:s}}async function tw(o,i,a,s,c){let u;if(a instanceof Uint8Array){u=await crypto.subtle.importKey("raw",a,"AES-GCM",false,["encrypt"])}else{tm(a,o,"encrypt");u=a}const l=new Uint8Array(await crypto.subtle.encrypt({additionalData:c,iv:s,name:"AES-GCM",tagLength:128},u,i));const d=l.slice(-16);const f=l.slice(0,-16);return{ciphertext:f,tag:d,iv:s}}const t_=async(o,i,a,s,c)=>{if(!eY(a)&&!(a instanceof Uint8Array)){throw new TypeError(e4(a,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"))}if(s){tu(o,s)}else{s=tc(o)}switch(o){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":if(a instanceof Uint8Array){tl(a,parseInt(o.slice(-3),10))}return tb(o,i,a,s,c);case"A128GCM":case"A192GCM":case"A256GCM":if(a instanceof Uint8Array){tl(a,parseInt(o.slice(1,4),10))}return tw(o,i,a,s,c);default:throw new e$("Unsupported JWE Content Encryption Algorithm")}};function tv(o,i){if(o.algorithm.length!==parseInt(i.slice(1,4),10)){throw new TypeError(`Invalid key size for alg: ${i}`)}}function tE(o,i,a){if(o instanceof Uint8Array){return crypto.subtle.importKey("raw",o,"AES-KW",true,[a])}tm(o,i,a);return o}async function tS(o,i,a){const s=await tE(i,o,"wrapKey");tv(s,o);const c=await crypto.subtle.importKey("raw",a,{hash:"SHA-256",name:"HMAC"},true,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",c,s,"AES-KW"))}async function tR(o,i,a){const s=await tE(i,o,"unwrapKey");tv(s,o);const c=await crypto.subtle.unwrapKey("raw",a,s,"AES-KW",{hash:"SHA-256",name:"HMAC"},true,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",c))};function tA(o){return ek(ex(o.length),o)}async function tP(o,i,a){const s=Math.ceil((i>>3)/32);const c=new Uint8Array(s*32);for(let i=0;i<s;i++){const s=new Uint8Array(4+o.length+a.length);s.set(ex(i+1));s.set(o,4);s.set(a,4+o.length);c.set(await eS("sha256",s),i*32)}return c.slice(0,i>>3)}async function tk(o,i,a,s,c=new Uint8Array(0),u=new Uint8Array(0)){tm(o,"ECDH");tm(i,"ECDH","deriveBits");const l=ek(tA(eR.encode(a)),tA(c),tA(u),ex(s));let d;if(o.algorithm.name==="X25519"){d=256}else{d=Math.ceil(parseInt(o.algorithm.namedCurve.slice(-3),10)/8)<<3}const f=new Uint8Array(await crypto.subtle.deriveBits({name:o.algorithm.name,public:o},i,d));return tP(f,s,l)}function tO(o){switch(o.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return true;default:return o.algorithm.name==="X25519"}};function tT(o,i){if(o instanceof Uint8Array){return crypto.subtle.importKey("raw",o,"PBKDF2",false,["deriveBits"])}tm(o,i,"deriveBits");return o}const tx=(o,i)=>ek(eR.encode(o),new Uint8Array([0]),i);async function tC(o,i,a,s){if(!(o instanceof Uint8Array)||o.length<8){throw new eW("PBES2 Salt Input must be 8 or more octets")}const c=tx(i,o);const u=parseInt(i.slice(13,16),10);const l={hash:`SHA-${i.slice(8,11)}`,iterations:a,name:"PBKDF2",salt:c};const d=await tT(s,i);return new Uint8Array(await crypto.subtle.deriveBits(l,d,u))}async function tj(o,i,a,s=2048,c=crypto.getRandomValues(new Uint8Array(16))){const u=await tC(c,o,s,i);const l=await tS(o.slice(-6),u,a);return{encryptedKey:l,p2c:s,p2s:eI(c)}}async function tN(o,i,a,s,c){const u=await tC(c,o,s,i);return tR(o.slice(-6),u,a)};const tI=(o,i)=>{if(o.startsWith("RS")||o.startsWith("PS")){const{modulusLength:a}=i.algorithm;if(typeof a!=="number"||a<2048){throw new TypeError(`${o} requires key modulusLength to be 2048 bits or larger`)}}};const tD=o=>{switch(o){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new e$(`alg ${o} is not supported either by JOSE or your javascript runtime`)}};async function tU(o,i,a){tm(i,o,"encrypt");tI(o,i);return new Uint8Array(await crypto.subtle.encrypt(tD(o),i,a))}async function tM(o,i,a){tm(i,o,"decrypt");tI(o,i);return new Uint8Array(await crypto.subtle.decrypt(tD(o),i,a))};function tL(o){let i;let a;switch(o.kty){case"RSA":{switch(o.alg){case"PS256":case"PS384":case"PS512":i={name:"RSA-PSS",hash:`SHA-${o.alg.slice(-3)}`};a=o.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":i={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${o.alg.slice(-3)}`};a=o.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":i={name:"RSA-OAEP",hash:`SHA-${parseInt(o.alg.slice(-3),10)||1}`};a=o.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new e$('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}case"EC":{switch(o.alg){case"ES256":i={name:"ECDSA",namedCurve:"P-256"};a=o.d?["sign"]:["verify"];break;case"ES384":i={name:"ECDSA",namedCurve:"P-384"};a=o.d?["sign"]:["verify"];break;case"ES512":i={name:"ECDSA",namedCurve:"P-521"};a=o.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":i={name:"ECDH",namedCurve:o.crv};a=o.d?["deriveBits"]:[];break;default:throw new e$('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}case"OKP":{switch(o.alg){case"Ed25519":case"EdDSA":i={name:"Ed25519"};a=o.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":i={name:o.crv};a=o.d?["deriveBits"]:[];break;default:throw new e$('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}default:throw new e$('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:i,keyUsages:a}}const t$=async o=>{if(!o.alg){throw new TypeError('"alg" argument is required when "jwk.alg" is not present')}const{algorithm:i,keyUsages:a}=tL(o);const s={...o};delete s.alg;delete s.use;return crypto.subtle.importKey("jwk",s,i,o.ext??(o.d?false:true),o.key_ops??a)};let tH;const tW=async(o,i,a,s=false)=>{tH||=new WeakMap;let c=tH.get(o);if(c?.[a]){return c[a]}const u=await t$({...i,alg:a});if(s)Object.freeze(o);if(!c){tH.set(o,{[a]:u})}else{c[a]=u}return u};const tK=(o,i)=>{tH||=new WeakMap;let a=tH.get(o);if(a?.[i]){return a[i]}const s=o.type==="public";const c=s?true:false;let u;if(o.asymmetricKeyType==="x25519"){switch(i){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw new TypeError("given KeyObject instance cannot be used for this algorithm")}u=o.toCryptoKey(o.asymmetricKeyType,c,s?[]:["deriveBits"])}if(o.asymmetricKeyType==="ed25519"){if(i!=="EdDSA"&&i!=="Ed25519"){throw new TypeError("given KeyObject instance cannot be used for this algorithm")}u=o.toCryptoKey(o.asymmetricKeyType,c,[s?"verify":"sign"])}if(o.asymmetricKeyType==="rsa"){let a;switch(i){case"RSA-OAEP":a="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":a="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":a="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":a="SHA-512";break;default:throw new TypeError("given KeyObject instance cannot be used for this algorithm")}if(i.startsWith("RSA-OAEP")){return o.toCryptoKey({name:"RSA-OAEP",hash:a},c,s?["encrypt"]:["decrypt"])}u=o.toCryptoKey({name:i.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:a},c,[s?"verify":"sign"])}if(o.asymmetricKeyType==="ec"){const a=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]);const l=a.get(o.asymmetricKeyDetails?.namedCurve);if(!l){throw new TypeError("given KeyObject instance cannot be used for this algorithm")}if(i==="ES256"&&l==="P-256"){u=o.toCryptoKey({name:"ECDSA",namedCurve:l},c,[s?"verify":"sign"])}if(i==="ES384"&&l==="P-384"){u=o.toCryptoKey({name:"ECDSA",namedCurve:l},c,[s?"verify":"sign"])}if(i==="ES512"&&l==="P-521"){u=o.toCryptoKey({name:"ECDSA",namedCurve:l},c,[s?"verify":"sign"])}if(i.startsWith("ECDH-ES")){u=o.toCryptoKey({name:"ECDH",namedCurve:l},c,s?[]:["deriveBits"])}}if(!u){throw new TypeError("given KeyObject instance cannot be used for this algorithm")}if(!a){tH.set(o,{[i]:u})}else{a[i]=u}return u};const tB=async(o,i)=>{if(o instanceof Uint8Array){return o}if(eY(o)){return o}if(eZ(o)){if(o.type==="secret"){return o.export()}if("toCryptoKey"in o&&typeof o.toCryptoKey==="function"){try{return tK(o,i)}catch(o){if(o instanceof TypeError){throw o}}}let a=o.export({format:"jwk"});return tW(o,a,i)}if(e2(o)){if(o.k){return eN(o.k)}return tW(o,o,i,true)}throw new Error("unreachable")};function tq(o){switch(o){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new e$(`Unsupported JWE Algorithm: ${o}`)}}const tG=o=>crypto.getRandomValues(new Uint8Array(tq(o)>>3));async function tF(o,i){if(!(o instanceof Uint8Array)){throw new TypeError("First argument must be a buffer")}if(!(i instanceof Uint8Array)){throw new TypeError("Second argument must be a buffer")}const a={name:"HMAC",hash:"SHA-256"};const s=await crypto.subtle.generateKey(a,false,["sign"]);const c=new Uint8Array(await crypto.subtle.sign(a,s,o));const u=new Uint8Array(await crypto.subtle.sign(a,s,i));let l=0;let d=-1;while(++d<32){l|=c[d]^u[d]}return l===0}async function tJ(o,i,a,s,c,u){if(!(i instanceof Uint8Array)){throw new TypeError(e4(i,"Uint8Array"))}const l=parseInt(o.slice(1,4),10);const d=await crypto.subtle.importKey("raw",i.subarray(l>>3),"AES-CBC",false,["decrypt"]);const f=await crypto.subtle.importKey("raw",i.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},false,["sign"]);const p=ek(u,s,a,eT(u.length<<3));const h=new Uint8Array((await crypto.subtle.sign("HMAC",f,p)).slice(0,l>>3));let y;try{y=await tF(c,h)}catch{}if(!y){throw new eH}let g;try{g=new Uint8Array(await crypto.subtle.decrypt({iv:s,name:"AES-CBC"},d,a))}catch{}if(!g){throw new eH}return g}async function tV(o,i,a,s,c,u){let l;if(i instanceof Uint8Array){l=await crypto.subtle.importKey("raw",i,"AES-GCM",false,["decrypt"])}else{tm(i,o,"decrypt");l=i}try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:u,iv:s,name:"AES-GCM",tagLength:128},l,ek(a,c)))}catch{throw new eH}}const tz=async(o,i,a,s,c,u)=>{if(!eY(i)&&!(i instanceof Uint8Array)){throw new TypeError(e4(i,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"))}if(!s){throw new eW("JWE Initialization Vector missing")}if(!c){throw new eW("JWE Authentication Tag missing")}tu(o,s);switch(o){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":if(i instanceof Uint8Array)tl(i,parseInt(o.slice(-3),10));return tJ(o,i,a,s,c,u);case"A128GCM":case"A192GCM":case"A256GCM":if(i instanceof Uint8Array)tl(i,parseInt(o.slice(1,4),10));return tV(o,i,a,s,c,u);default:throw new e$("Unsupported JWE Content Encryption Algorithm")}};async function tX(o,i,a,s){const c=o.slice(0,7);const u=await t_(c,a,i,s,new Uint8Array(0));return{encryptedKey:u.ciphertext,iv:eI(u.iv),tag:eI(u.tag)}}async function tY(o,i,a,s,c){const u=o.slice(0,7);return tz(u,i,a,s,c,new Uint8Array(0))};const tZ=async(o,i,a,s,c={})=>{let u;let l;let d;switch(o){case"dir":{d=a;break}case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{eX(a);if(!tO(a)){throw new e$("ECDH with the provided key is not allowed or not supported by your javascript runtime")}const{apu:f,apv:p}=c;let h;if(c.epk){h=await tB(c.epk,o)}else{h=(await crypto.subtle.generateKey(a.algorithm,true,["deriveBits"])).privateKey}const{x:y,y:g,crv:m,kty:b}=await tn(h);const w=await tk(a,h,o==="ECDH-ES"?i:o,o==="ECDH-ES"?tq(i):parseInt(o.slice(-5,-2),10),f,p);l={epk:{x:y,crv:m,kty:b}};if(b==="EC")l.epk.y=g;if(f)l.apu=eI(f);if(p)l.apv=eI(p);if(o==="ECDH-ES"){d=w;break}d=s||tG(i);const _=o.slice(-6);u=await tS(_,w,d);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{d=s||tG(i);eX(a);u=await tU(o,a,d);break}case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{d=s||tG(i);const{p2c:f,p2s:p}=c;({encryptedKey:u,...l}=await tj(o,a,d,f,p));break}case"A128KW":case"A192KW":case"A256KW":{d=s||tG(i);u=await tS(o,a,d);break}case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{d=s||tG(i);const{iv:f}=c;({encryptedKey:u,...l}=await tX(o,a,d,f));break}default:{throw new e$('Invalid or unsupported "alg" (JWE Algorithm) header value')}}return{cek:d,encryptedKey:u,parameters:l}};const tQ=(...o)=>{const i=o.filter(Boolean);if(i.length===0||i.length===1){return true}let a;for(const o of i){const i=Object.keys(o);if(!a||a.size===0){a=new Set(i);continue}for(const o of i){if(a.has(o)){return false}a.add(o)}}return true};const t0=(o,i,a,s,c)=>{if(c.crit!==undefined&&s?.crit===undefined){throw new o('"crit" (Critical) Header Parameter MUST be integrity protected')}if(!s||s.crit===undefined){return new Set}if(!Array.isArray(s.crit)||s.crit.length===0||s.crit.some(o=>typeof o!=="string"||o.length===0)){throw new o('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present')}let u;if(a!==undefined){u=new Map([...Object.entries(a),...i.entries()])}else{u=i}for(const i of s.crit){if(!u.has(i)){throw new e$(`Extension Header Parameter "${i}" is not recognized`)}if(c[i]===undefined){throw new o(`Extension Header Parameter "${i}" is missing`)}if(u.get(i)&&s[i]===undefined){throw new o(`Extension Header Parameter "${i}" MUST be integrity protected`)}}return new Set(s.crit)};const t1=o=>o?.[Symbol.toStringTag];const t2=(o,i,a)=>{if(i.use!==undefined){let o;switch(a){case"sign":case"verify":o="sig";break;case"encrypt":case"decrypt":o="enc";break}if(i.use!==o){throw new TypeError(`Invalid key for this operation, its "use" must be "${o}" when present`)}}if(i.alg!==undefined&&i.alg!==o){throw new TypeError(`Invalid key for this operation, its "alg" must be "${o}" when present`)}if(Array.isArray(i.key_ops)){let s;switch(true){case a==="sign"||a==="verify":case o==="dir":case o.includes("CBC-HS"):s=a;break;case o.startsWith("PBES2"):s="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(o):if(!o.includes("GCM")&&o.endsWith("KW")){s=a==="encrypt"?"wrapKey":"unwrapKey"}else{s=a}break;case a==="encrypt"&&o.startsWith("RSA"):s="wrapKey";break;case a==="decrypt":s=o.startsWith("RSA")?"unwrapKey":"deriveBits";break}if(s&&i.key_ops?.includes?.(s)===false){throw new TypeError(`Invalid key for this operation, its "key_ops" must include "${s}" when present`)}}return true};const t3=(o,i,a)=>{if(i instanceof Uint8Array)return;if(e2(i)){if(e8(i)&&t2(o,i,a))return;throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present`)}if(!eQ(i)){throw new TypeError(e9(o,i,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"))}if(i.type!=="secret"){throw new TypeError(`${t1(i)} instances for symmetric algorithms must be of type "secret"`)}};const t6=(o,i,a)=>{if(e2(i)){switch(a){case"decrypt":case"sign":if(e3(i)&&t2(o,i,a))return;throw new TypeError(`JSON Web Key for this operation be a private JWK`);case"encrypt":case"verify":if(e6(i)&&t2(o,i,a))return;throw new TypeError(`JSON Web Key for this operation be a public JWK`)}}if(!eQ(i)){throw new TypeError(e9(o,i,"CryptoKey","KeyObject","JSON Web Key"))}if(i.type==="secret"){throw new TypeError(`${t1(i)} instances for asymmetric algorithms must not be of type "secret"`)}if(i.type==="public"){switch(a){case"sign":throw new TypeError(`${t1(i)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw new TypeError(`${t1(i)} instances for asymmetric algorithm decryption must be of type "private"`);default:break}}if(i.type==="private"){switch(a){case"verify":throw new TypeError(`${t1(i)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw new TypeError(`${t1(i)} instances for asymmetric algorithm encryption must be of type "public"`);default:break}}};const t8=(o,i,a)=>{const s=o.startsWith("HS")||o==="dir"||o.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(o)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(o);if(s){t3(o,i,a)}else{t6(o,i,a)}};class t5{#e;#t;#n;#r;#o;#i;#a;#s;constructor(o){if(!(o instanceof Uint8Array)){throw new TypeError("plaintext must be an instance of Uint8Array")}this.#e=o}setKeyManagementParameters(o){if(this.#s){throw new TypeError("setKeyManagementParameters can only be called once")}this.#s=o;return this}setProtectedHeader(o){if(this.#t){throw new TypeError("setProtectedHeader can only be called once")}this.#t=o;return this}setSharedUnprotectedHeader(o){if(this.#n){throw new TypeError("setSharedUnprotectedHeader can only be called once")}this.#n=o;return this}setUnprotectedHeader(o){if(this.#r){throw new TypeError("setUnprotectedHeader can only be called once")}this.#r=o;return this}setAdditionalAuthenticatedData(o){this.#o=o;return this}setContentEncryptionKey(o){if(this.#i){throw new TypeError("setContentEncryptionKey can only be called once")}this.#i=o;return this}setInitializationVector(o){if(this.#a){throw new TypeError("setInitializationVector can only be called once")}this.#a=o;return this}async encrypt(o,i){if(!this.#t&&!this.#r&&!this.#n){throw new eW("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()")}if(!tQ(this.#t,this.#r,this.#n)){throw new eW("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint")}const a={...this.#t,...this.#r,...this.#n};t0(eW,new Map,i?.crit,this.#t,a);if(a.zip!==undefined){throw new e$('JWE "zip" (Compression Algorithm) Header Parameter is not supported.')}const{alg:s,enc:c}=a;if(typeof s!=="string"||!s){throw new eW('JWE "alg" (Algorithm) Header Parameter missing or invalid')}if(typeof c!=="string"||!c){throw new eW('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid')}let u;if(this.#i&&(s==="dir"||s==="ECDH-ES")){throw new TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${s}`)}t8(s==="dir"?c:s,o,"encrypt");let l;{let a;const d=await tB(o,s);({cek:l,encryptedKey:u,parameters:a}=await tZ(s,c,d,this.#i,this.#s));if(a){if(i&&ta in i){if(!this.#r){this.setUnprotectedHeader(a)}else{this.#r={...this.#r,...a}}}else if(!this.#t){this.setProtectedHeader(a)}else{this.#t={...this.#t,...a}}}}let d;let f;let p;if(this.#t){f=eR.encode(eI(JSON.stringify(this.#t)))}else{f=eR.encode("")}if(this.#o){p=eI(this.#o);d=ek(f,eR.encode("."),eR.encode(p))}else{d=f}const{ciphertext:h,tag:y,iv:g}=await t_(c,this.#e,l,this.#a,d);const m={ciphertext:eI(h)};if(g){m.iv=eI(g)}if(y){m.tag=eI(y)}if(u){m.encrypted_key=eI(u)}if(p){m.aad=p}if(this.#t){m.protected=eA.decode(f)}if(this.#n){m.unprotected=this.#n}if(this.#r){m.header=this.#r}return m}};class t4{#c;constructor(o){this.#c=new t5(o)}setContentEncryptionKey(o){this.#c.setContentEncryptionKey(o);return this}setInitializationVector(o){this.#c.setInitializationVector(o);return this}setProtectedHeader(o){this.#c.setProtectedHeader(o);return this}setKeyManagementParameters(o){this.#c.setKeyManagementParameters(o);return this}async encrypt(o,i){const a=await this.#c.encrypt(o,i);return[a.protected,a.encrypted_key,a.iv,a.ciphertext,a.tag].join(".")}};const t9=o=>Math.floor(o.getTime()/1e3);const t7=60;const ne=t7*60;const nt=ne*24;const nn=nt*7;const nr=nt*365.25;const no=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;const ni=o=>{const i=no.exec(o);if(!i||i[4]&&i[1]){throw new TypeError("Invalid time period format")}const a=parseFloat(i[2]);const s=i[3].toLowerCase();let c;switch(s){case"sec":case"secs":case"second":case"seconds":case"s":c=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":c=Math.round(a*t7);break;case"hour":case"hours":case"hr":case"hrs":case"h":c=Math.round(a*ne);break;case"day":case"days":case"d":c=Math.round(a*nt);break;case"week":case"weeks":case"w":c=Math.round(a*nn);break;default:c=Math.round(a*nr);break}if(i[1]==="-"||i[4]==="ago"){return-c}return c};function na(o,i){if(!Number.isFinite(i)){throw new TypeError(`Invalid ${o} input`)}return i}const ns=o=>{if(o.includes("/")){return o.toLowerCase()}return`application/${o.toLowerCase()}`};const nc=(o,i)=>{if(typeof o==="string"){return i.includes(o)}if(Array.isArray(o)){return i.some(Set.prototype.has.bind(new Set(o)))}return false};function nu(o,i,a={}){let s;try{s=JSON.parse(eA.decode(i))}catch{}if(!e1(s)){throw new eB("JWT Claims Set must be a top-level JSON object")}const{typ:c}=a;if(c&&(typeof o.typ!=="string"||ns(o.typ)!==ns(c))){throw new eU('unexpected "typ" JWT header value',s,"typ","check_failed")}const{requiredClaims:u=[],issuer:l,subject:d,audience:f,maxTokenAge:p}=a;const h=[...u];if(p!==undefined)h.push("iat");if(f!==undefined)h.push("aud");if(d!==undefined)h.push("sub");if(l!==undefined)h.push("iss");for(const o of new Set(h.reverse())){if(!(o in s)){throw new eU(`missing required "${o}" claim`,s,o,"missing")}}if(l&&!(Array.isArray(l)?l:[l]).includes(s.iss)){throw new eU('unexpected "iss" claim value',s,"iss","check_failed")}if(d&&s.sub!==d){throw new eU('unexpected "sub" claim value',s,"sub","check_failed")}if(f&&!nc(s.aud,typeof f==="string"?[f]:f)){throw new eU('unexpected "aud" claim value',s,"aud","check_failed")}let y;switch(typeof a.clockTolerance){case"string":y=ni(a.clockTolerance);break;case"number":y=a.clockTolerance;break;case"undefined":y=0;break;default:throw new TypeError("Invalid clockTolerance option type")}const{currentDate:g}=a;const m=t9(g||new Date);if((s.iat!==undefined||p)&&typeof s.iat!=="number"){throw new eU('"iat" claim must be a number',s,"iat","invalid")}if(s.nbf!==undefined){if(typeof s.nbf!=="number"){throw new eU('"nbf" claim must be a number',s,"nbf","invalid")}if(s.nbf>m+y){throw new eU('"nbf" claim timestamp check failed',s,"nbf","check_failed")}}if(s.exp!==undefined){if(typeof s.exp!=="number"){throw new eU('"exp" claim must be a number',s,"exp","invalid")}if(s.exp<=m-y){throw new eM('"exp" claim timestamp check failed',s,"exp","check_failed")}}if(p){const o=m-s.iat;const i=typeof p==="number"?p:ni(p);if(o-y>i){throw new eM('"iat" claim timestamp check failed (too far in the past)',s,"iat","check_failed")}if(o<0-y){throw new eU('"iat" claim timestamp check failed (it should be in the past)',s,"iat","check_failed")}}return s}class nl{#u;constructor(o){if(!e1(o)){throw new TypeError("JWT Claims Set MUST be an object")}this.#u=structuredClone(o)}data(){return eR.encode(JSON.stringify(this.#u))}get iss(){return this.#u.iss}set iss(o){this.#u.iss=o}get sub(){return this.#u.sub}set sub(o){this.#u.sub=o}get aud(){return this.#u.aud}set aud(o){this.#u.aud=o}set jti(o){this.#u.jti=o}set nbf(o){if(typeof o==="number"){this.#u.nbf=na("setNotBefore",o)}else if(o instanceof Date){this.#u.nbf=na("setNotBefore",t9(o))}else{this.#u.nbf=t9(new Date)+ni(o)}}set exp(o){if(typeof o==="number"){this.#u.exp=na("setExpirationTime",o)}else if(o instanceof Date){this.#u.exp=na("setExpirationTime",t9(o))}else{this.#u.exp=t9(new Date)+ni(o)}}set iat(o){if(typeof o==="undefined"){this.#u.iat=t9(new Date)}else if(o instanceof Date){this.#u.iat=na("setIssuedAt",t9(o))}else if(typeof o==="string"){this.#u.iat=na("setIssuedAt",t9(new Date)+ni(o))}else{this.#u.iat=na("setIssuedAt",o)}}};class nd{#i;#a;#s;#t;#l;#d;#f;#p;constructor(o={}){this.#p=new nl(o)}setIssuer(o){this.#p.iss=o;return this}setSubject(o){this.#p.sub=o;return this}setAudience(o){this.#p.aud=o;return this}setJti(o){this.#p.jti=o;return this}setNotBefore(o){this.#p.nbf=o;return this}setExpirationTime(o){this.#p.exp=o;return this}setIssuedAt(o){this.#p.iat=o;return this}setProtectedHeader(o){if(this.#t){throw new TypeError("setProtectedHeader can only be called once")}this.#t=o;return this}setKeyManagementParameters(o){if(this.#s){throw new TypeError("setKeyManagementParameters can only be called once")}this.#s=o;return this}setContentEncryptionKey(o){if(this.#i){throw new TypeError("setContentEncryptionKey can only be called once")}this.#i=o;return this}setInitializationVector(o){if(this.#a){throw new TypeError("setInitializationVector can only be called once")}this.#a=o;return this}replicateIssuerAsHeader(){this.#l=true;return this}replicateSubjectAsHeader(){this.#d=true;return this}replicateAudienceAsHeader(){this.#f=true;return this}async encrypt(o,i){const a=new t4(this.#p.data());if(this.#t&&(this.#l||this.#d||this.#f)){this.#t={...this.#t,iss:this.#l?this.#p.iss:undefined,sub:this.#d?this.#p.sub:undefined,aud:this.#f?this.#p.aud:undefined}}a.setProtectedHeader(this.#t);if(this.#a){a.setInitializationVector(this.#a)}if(this.#i){a.setContentEncryptionKey(this.#i)}if(this.#s){a.setKeyManagementParameters(this.#s)}return a.encrypt(o,i)}};async function nf(o,i,a){if(typeof o!=="string"||o.indexOf("-----BEGIN PUBLIC KEY-----")!==0){throw new TypeError('"spki" must be SPKI formatted string')}return fromSPKI(o,i,a)}async function np(o,i,a){if(typeof o!=="string"||o.indexOf("-----BEGIN CERTIFICATE-----")!==0){throw new TypeError('"x509" must be X.509 formatted string')}return fromX509(o,i,a)}async function nh(o,i,a){if(typeof o!=="string"||o.indexOf("-----BEGIN PRIVATE KEY-----")!==0){throw new TypeError('"pkcs8" must be PKCS#8 formatted string')}return fromPKCS8(o,i,a)}async function ny(o,i,a){if(!e1(o)){throw new TypeError("JWK must be an object")}let s;i??=o.alg;s??=a?.extractable??o.ext;switch(o.kty){case"oct":if(typeof o.k!=="string"||!o.k){throw new TypeError('missing "k" (Key Value) Parameter value')}return eN(o.k);case"RSA":if("oth"in o&&o.oth!==undefined){throw new e$('RSA JWK "oth" (Other Primes Info) Parameter value is not supported')}case"EC":case"OKP":return t$({...o,alg:i,ext:s});default:throw new e$('Unsupported "kty" (Key Type) Parameter value')}};const ng=async(o,i,a,s,c)=>{switch(o){case"dir":{if(a!==undefined)throw new eW("Encountered unexpected JWE Encrypted Key");return i}case"ECDH-ES":if(a!==undefined)throw new eW("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!e1(s.epk))throw new eW(`JOSE Header "epk" (Ephemeral Public Key) missing or invalid`);eX(i);if(!tO(i))throw new e$("ECDH with the provided key is not allowed or not supported by your javascript runtime");const c=await ny(s.epk,o);eX(c);let u;let l;if(s.apu!==undefined){if(typeof s.apu!=="string")throw new eW(`JOSE Header "apu" (Agreement PartyUInfo) invalid`);try{u=eN(s.apu)}catch{throw new eW("Failed to base64url decode the apu")}}if(s.apv!==undefined){if(typeof s.apv!=="string")throw new eW(`JOSE Header "apv" (Agreement PartyVInfo) invalid`);try{l=eN(s.apv)}catch{throw new eW("Failed to base64url decode the apv")}}const d=await tk(c,i,o==="ECDH-ES"?s.enc:o,o==="ECDH-ES"?tq(s.enc):parseInt(o.slice(-5,-2),10),u,l);if(o==="ECDH-ES")return d;if(a===undefined)throw new eW("JWE Encrypted Key missing");return tR(o.slice(-6),d,a)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(a===undefined)throw new eW("JWE Encrypted Key missing");eX(i);return tM(o,i,a)}case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{if(a===undefined)throw new eW("JWE Encrypted Key missing");if(typeof s.p2c!=="number")throw new eW(`JOSE Header "p2c" (PBES2 Count) missing or invalid`);const u=c?.maxPBES2Count||1e4;if(s.p2c>u)throw new eW(`JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds`);if(typeof s.p2s!=="string")throw new eW(`JOSE Header "p2s" (PBES2 Salt) missing or invalid`);let l;try{l=eN(s.p2s)}catch{throw new eW("Failed to base64url decode the p2s")}return tN(o,i,a,s.p2c,l)}case"A128KW":case"A192KW":case"A256KW":{if(a===undefined)throw new eW("JWE Encrypted Key missing");return tR(o,i,a)}case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{if(a===undefined)throw new eW("JWE Encrypted Key missing");if(typeof s.iv!=="string")throw new eW(`JOSE Header "iv" (Initialization Vector) missing or invalid`);if(typeof s.tag!=="string")throw new eW(`JOSE Header "tag" (Authentication Tag) missing or invalid`);let c;try{c=eN(s.iv)}catch{throw new eW("Failed to base64url decode the iv")}let u;try{u=eN(s.tag)}catch{throw new eW("Failed to base64url decode the tag")}return tY(o,i,a,c,u)}default:{throw new e$('Invalid or unsupported "alg" (JWE Algorithm) header value')}}};const nm=(o,i)=>{if(i!==undefined&&(!Array.isArray(i)||i.some(o=>typeof o!=="string"))){throw new TypeError(`"${o}" option must be an array of strings`)}if(!i){return undefined}return new Set(i)};async function nb(o,i,a){if(!e1(o)){throw new eW("Flattened JWE must be an object")}if(o.protected===undefined&&o.header===undefined&&o.unprotected===undefined){throw new eW("JOSE Header missing")}if(o.iv!==undefined&&typeof o.iv!=="string"){throw new eW("JWE Initialization Vector incorrect type")}if(typeof o.ciphertext!=="string"){throw new eW("JWE Ciphertext missing or incorrect type")}if(o.tag!==undefined&&typeof o.tag!=="string"){throw new eW("JWE Authentication Tag incorrect type")}if(o.protected!==undefined&&typeof o.protected!=="string"){throw new eW("JWE Protected Header incorrect type")}if(o.encrypted_key!==undefined&&typeof o.encrypted_key!=="string"){throw new eW("JWE Encrypted Key incorrect type")}if(o.aad!==undefined&&typeof o.aad!=="string"){throw new eW("JWE AAD incorrect type")}if(o.header!==undefined&&!e1(o.header)){throw new eW("JWE Shared Unprotected Header incorrect type")}if(o.unprotected!==undefined&&!e1(o.unprotected)){throw new eW("JWE Per-Recipient Unprotected Header incorrect type")}let s;if(o.protected){try{const i=eN(o.protected);s=JSON.parse(eA.decode(i))}catch{throw new eW("JWE Protected Header is invalid")}}if(!tQ(s,o.header,o.unprotected)){throw new eW("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint")}const c={...s,...o.header,...o.unprotected};t0(eW,new Map,a?.crit,s,c);if(c.zip!==undefined){throw new e$('JWE "zip" (Compression Algorithm) Header Parameter is not supported.')}const{alg:u,enc:l}=c;if(typeof u!=="string"||!u){throw new eW("missing JWE Algorithm (alg) in JWE Header")}if(typeof l!=="string"||!l){throw new eW("missing JWE Encryption Algorithm (enc) in JWE Header")}const d=a&&nm("keyManagementAlgorithms",a.keyManagementAlgorithms);const f=a&&nm("contentEncryptionAlgorithms",a.contentEncryptionAlgorithms);if(d&&!d.has(u)||!d&&u.startsWith("PBES2")){throw new eL('"alg" (Algorithm) Header Parameter value not allowed')}if(f&&!f.has(l)){throw new eL('"enc" (Encryption Algorithm) Header Parameter value not allowed')}let p;if(o.encrypted_key!==undefined){try{p=eN(o.encrypted_key)}catch{throw new eW("Failed to base64url decode the encrypted_key")}}let h=false;if(typeof i==="function"){i=await i(s,o);h=true}t8(u==="dir"?l:u,i,"decrypt");const y=await tB(i,u);let g;try{g=await ng(u,y,p,c,a)}catch(o){if(o instanceof TypeError||o instanceof eW||o instanceof e$){throw o}g=tG(l)}let m;let b;if(o.iv!==undefined){try{m=eN(o.iv)}catch{throw new eW("Failed to base64url decode the iv")}}if(o.tag!==undefined){try{b=eN(o.tag)}catch{throw new eW("Failed to base64url decode the tag")}}const w=eR.encode(o.protected??"");let _;if(o.aad!==undefined){_=ek(w,eR.encode("."),eR.encode(o.aad))}else{_=w}let v;try{v=eN(o.ciphertext)}catch{throw new eW("Failed to base64url decode the ciphertext")}const E=await tz(l,g,v,m,b,_);const S={plaintext:E};if(o.protected!==undefined){S.protectedHeader=s}if(o.aad!==undefined){try{S.additionalAuthenticatedData=eN(o.aad)}catch{throw new eW("Failed to base64url decode the aad")}}if(o.unprotected!==undefined){S.sharedUnprotectedHeader=o.unprotected}if(o.header!==undefined){S.unprotectedHeader=o.header}if(h){return{...S,key:y}}return S};async function nw(o,i,a){if(o instanceof Uint8Array){o=eA.decode(o)}if(typeof o!=="string"){throw new eW("Compact JWE must be a string or Uint8Array")}const{0:s,1:c,2:u,3:l,4:d,length:f}=o.split(".");if(f!==5){throw new eW("Invalid Compact JWE")}const p=await nb({ciphertext:l,iv:u||undefined,protected:s,tag:d||undefined,encrypted_key:c||undefined},i,a);const h={plaintext:p.plaintext,protectedHeader:p.protectedHeader};if(typeof i==="function"){return{...h,key:p.key}}return h};async function n_(o,i,a){const s=await nw(o,i,a);const c=nu(s.protectedHeader,s.plaintext,a);const{protectedHeader:u}=s;if(u.iss!==undefined&&u.iss!==c.iss){throw new eU('replicated "iss" claim header parameter mismatch',c,"iss","mismatch")}if(u.sub!==undefined&&u.sub!==c.sub){throw new eU('replicated "sub" claim header parameter mismatch',c,"sub","mismatch")}if(u.aud!==undefined&&JSON.stringify(u.aud)!==JSON.stringify(c.aud)){throw new eU('replicated "aud" claim header parameter mismatch',c,"aud","mismatch")}const l={payload:c,protectedHeader:u};if(typeof i==="function"){return{...l,key:s.key}}return l};const nv=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/;const nE=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/;const nS=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;const nR=/^[\u0020-\u003A\u003D-\u007E]*$/;const nA=Object.prototype.toString;const nP=(()=>{const o=function(){};o.prototype=Object.create(null);return o})();function nk(o,i){const a=new nP;const s=o.length;if(s<2)return a;const c=i?.decode||nC;let u=0;do{const i=o.indexOf("=",u);if(i===-1)break;const l=o.indexOf(";",u);const d=l===-1?s:l;if(i>d){u=o.lastIndexOf(";",i-1)+1;continue}const f=nO(o,u,i);const p=nT(o,i,f);const h=o.slice(f,p);if(a[h]===undefined){let s=nO(o,i+1,d);let u=nT(o,d,s);const l=c(o.slice(s,u));a[h]=l}u=d+1}while(u<s);return a}function nO(o,i,a){do{const a=o.charCodeAt(i);if(a!==32&&a!==9)return i}while(++i<a);return a}function nT(o,i,a){while(i>a){const a=o.charCodeAt(--i);if(a!==32&&a!==9)return i+1}return a}function nx(o,i,a){const s=a?.encode||encodeURIComponent;if(!nv.test(o)){throw new TypeError(`argument name is invalid: ${o}`)}const c=s(i);if(!nE.test(c)){throw new TypeError(`argument val is invalid: ${i}`)}let u=o+"="+c;if(!a)return u;if(a.maxAge!==undefined){if(!Number.isInteger(a.maxAge)){throw new TypeError(`option maxAge is invalid: ${a.maxAge}`)}u+="; Max-Age="+a.maxAge}if(a.domain){if(!nS.test(a.domain)){throw new TypeError(`option domain is invalid: ${a.domain}`)}u+="; Domain="+a.domain}if(a.path){if(!nR.test(a.path)){throw new TypeError(`option path is invalid: ${a.path}`)}u+="; Path="+a.path}if(a.expires){if(!nj(a.expires)||!Number.isFinite(a.expires.valueOf())){throw new TypeError(`option expires is invalid: ${a.expires}`)}u+="; Expires="+a.expires.toUTCString()}if(a.httpOnly){u+="; HttpOnly"}if(a.secure){u+="; Secure"}if(a.partitioned){u+="; Partitioned"}if(a.priority){const o=typeof a.priority==="string"?a.priority.toLowerCase():undefined;switch(o){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${a.priority}`)}}if(a.sameSite){const o=typeof a.sameSite==="string"?a.sameSite.toLowerCase():a.sameSite;switch(o){case true:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${a.sameSite}`)}}return u}function nC(o){if(o.indexOf("%")===-1)return o;try{return decodeURIComponent(o)}catch(i){return o}}function nj(o){return nA.call(o)==="[object Date]"};const{"q":nN}=s;const nI=30*24*60*60;const nD=()=>Date.now()/1e3|0;const nU="dir";const nM="A256CBC-HS512";async function nL(o){const{token:i={},secret:a,maxAge:s=nI,salt:c}=o;const u=Array.isArray(a)?a:[a];const l=await nW(nM,u[0],c);const d=await to({kty:"oct",k:eI(l)},`sha${l.byteLength<<3}`);return await new nd(i).setProtectedHeader({alg:nU,enc:nM,kid:d}).setIssuedAt().setExpirationTime(nD()+s).setJti(crypto.randomUUID()).encrypt(l)}async function n$(o){const{token:i,secret:a,salt:s}=o;const c=Array.isArray(a)?a:[a];if(!i)return null;const{payload:u}=await n_(i,async({kid:o,enc:i})=>{for(const a of c){const c=await nW(i,a,s);if(o===undefined)return c;const u=await to({kty:"oct",k:eI(c)},`sha${c.byteLength<<3}`);if(o===u)return c}throw new Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:[nU],contentEncryptionAlgorithms:[nM,"A256GCM"]});return u}async function nH(o){const{secureCookie:i,cookieName:a=defaultCookies(i??false).sessionToken.name,decode:s=n$,salt:c=a,secret:u,logger:l=console,raw:d,req:f}=o;if(!f)throw new Error("Must pass `req` to JWT getToken()");const p=f.headers instanceof Headers?f.headers:new Headers(f.headers);const h=new SessionStore({name:a,options:{secure:i}},nN(p.get("cookie")??""),l);let y=h.value;const g=p.get("authorization");if(!y&&g?.split(" ")[0]==="Bearer"){const o=g.split(" ")[1];y=decodeURIComponent(o)}if(!y)return null;if(d)return y;if(!u)throw new MissingSecret("Must pass `secret` if not set to JWT getToken()");try{return await s({token:y,secret:u,salt:c})}catch{return null}}async function nW(o,i,a){let s;switch(o){case"A256CBC-HS512":s=64;break;case"A256GCM":s=32;break;default:throw new Error("Unsupported JWT Content Encryption Algorithm")}return await eE("sha256",i,a,`Auth.js Generated Encryption Key (${a})`,s)};async function nK({options:o,paramValue:i,cookieValue:a}){const{url:s,callbacks:c}=o;let u=s.origin;if(i){u=await c.redirect({url:i,baseUrl:s.origin})}else if(a){u=await c.redirect({url:a,baseUrl:s.origin})}return{callbackUrl:u,callbackUrlCookie:u!==a?u:undefined}};const nB="\x1b[31m";const nq="\x1b[33m";const nG="\x1b[90m";const nF="\x1b[0m";const nJ={error(o){const i=o instanceof v?o.type:o.name;console.error(`${nB}[auth][error]${nF} ${i}: ${o.message}`);if(o.cause&&typeof o.cause==="object"&&"err"in o.cause&&o.cause.err instanceof Error){const{err:i,...a}=o.cause;console.error(`${nB}[auth][cause]${nF}:`,i.stack);if(a)console.error(`${nB}[auth][details]${nF}:`,JSON.stringify(a,null,2))}else if(o.stack){console.error(o.stack.replace(/.*/,"").substring(1))}},warn(o){const i=`https://warnings.authjs.dev`;console.warn(`${nq}[auth][warn][${o}]${nF}`,`Read more: ${i}`)},debug(o,i){console.log(`${nG}[auth][debug]:${nF} ${o}`,JSON.stringify(i,null,2))}};function nV(o){const i={...nJ};if(!o.debug)i.debug=()=>{};if(o.logger?.error)i.error=o.logger.error;if(o.logger?.warn)i.warn=o.logger.warn;if(o.logger?.debug)i.debug=o.logger.debug;o.logger??(o.logger=i);return i};const nz=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];function nX(o){return nz.includes(o)};const{"q":nY,"l":nZ}=s;async function nQ(o){if(!("body"in o)||!o.body||o.method!=="POST")return;const i=o.headers.get("content-type");if(i?.includes("application/json")){return await o.json()}else if(i?.includes("application/x-www-form-urlencoded")){const i=new URLSearchParams(await o.text());return Object.fromEntries(i)}}async function n0(o,i){try{if(o.method!=="GET"&&o.method!=="POST")throw new q("Only GET and POST requests are supported");i.basePath??(i.basePath="/auth");const a=new URL(o.url);const{action:s,providerId:c}=n8(a.pathname,i.basePath);return{url:a,action:s,providerId:c,method:o.method,headers:Object.fromEntries(o.headers),body:o.body?await nQ(o):undefined,cookies:nY(o.headers.get("cookie")??"")??{},error:a.searchParams.get("error")??undefined,query:Object.fromEntries(a.searchParams)}}catch(s){const a=nV(i);a.error(s);a.debug("request",o)}}function n1(o){return new Request(o.url,{headers:o.headers,method:o.method,body:o.method==="POST"?JSON.stringify(o.body??{}):undefined})}function n2(o){const i=new Headers(o.headers);o.cookies?.forEach(o=>{const{name:a,value:s,options:c}=o;const u=nZ(a,s,c);if(i.has("Set-Cookie"))i.append("Set-Cookie",u);else i.set("Set-Cookie",u)});let a=o.body;if(i.get("content-type")==="application/json")a=JSON.stringify(o.body);else if(i.get("content-type")==="application/x-www-form-urlencoded")a=new URLSearchParams(o.body).toString();const s=o.redirect?302:o.status??200;const c=new Response(a,{headers:i,status:s});if(o.redirect)c.headers.set("Location",o.redirect);return c}async function n3(o){const i=new TextEncoder().encode(o);const a=await crypto.subtle.digest("SHA-256",i);return Array.from(new Uint8Array(a)).map(o=>o.toString(16).padStart(2,"0")).join("").toString()}function n6(o){const i=o=>("0"+o.toString(16)).slice(-2);const a=(o,a)=>o+i(a);const s=crypto.getRandomValues(new Uint8Array(o));return Array.from(s).reduce(a,"")}function n8(o,i){const a=o.match(new RegExp(`^${i}(.+)`));if(a===null)throw new q(`Cannot parse action at ${o}`);const s=a.at(-1);const c=s.replace(/^\//,"").split("/").filter(Boolean);if(c.length!==1&&c.length!==2)throw new q(`Cannot parse action at ${o}`);const[u,l]=c;if(!nX(u))throw new q(`Cannot parse action at ${o}`);if(l&&!["signin","callback","webauthn-options"].includes(u))throw new q(`Cannot parse action at ${o}`);return{action:u,providerId:l=="undefined"?undefined:l}};async function n5({options:o,cookieValue:i,isPost:a,bodyValue:s}){if(i){const[c,u]=i.split("|");const l=await n3(`${c}${o.secret}`);if(u===l){const o=a&&c===s;return{csrfTokenVerified:o,csrfToken:c}}}const c=n6(32);const u=await n3(`${c}${o.secret}`);const l=`${c}|${u}`;return{cookie:l,csrfToken:c}}function n4(o,i){if(i)return;throw new z(`CSRF token was missing during an action ${o}`)};function n9(o){return o!==null&&typeof o==="object"}function n7(o,...i){if(!i.length)return o;const a=i.shift();if(n9(o)&&n9(a)){for(const i in a){if(n9(a[i])){if(!n9(o[i]))o[i]=Array.isArray(a[i])?[]:{};n7(o[i],a[i])}else if(a[i]!==undefined)o[i]=a[i]}}return n7(o,...i)};const re=Symbol("skip-csrf-check");const rt=Symbol("return-type-raw");const rn=Symbol("custom-fetch");const rr=Symbol("conform-internal");function ro(o){const{providerId:i,config:a}=o;const s=new URL(a.basePath??"/auth",o.url.origin);const c=a.providers.map(o=>{const i=typeof o==="function"?o():o;const{options:c,...u}=i;const l=c?.id??u.id;const d=n7(u,c,{signinUrl:`${s}/signin/${l}`,callbackUrl:`${s}/callback/${l}`});if(i.type==="oauth"||i.type==="oidc"){d.redirectProxyUrl??(d.redirectProxyUrl=c?.redirectProxyUrl??a.redirectProxyUrl);const o=ri(d);if(o.authorization?.url.searchParams.get("response_mode")==="form_post"){delete o.redirectProxyUrl}o[rn]??(o[rn]=c?.[rn]);return o}return d});const u=c.find(({id:o})=>o===i);if(i&&!u){const o=c.map(o=>o.id).join(", ");throw new Error(`Provider with id "${i}" not found. Available providers: [${o}].`)}return{providers:c,provider:u}}function ri(o){if(o.issuer)o.wellKnown??(o.wellKnown=`${o.issuer}/.well-known/openid-configuration`);const i=ru(o.authorization,o.issuer);if(i&&!i.url?.searchParams.has("scope")){i.url.searchParams.set("scope","openid profile email")}const a=ru(o.token,o.issuer);const s=ru(o.userinfo,o.issuer);const c=o.checks??["pkce"];if(o.redirectProxyUrl){if(!c.includes("state"))c.push("state");o.redirectProxyUrl=`${o.redirectProxyUrl}/callback/${o.id}`}return{...o,authorization:i,token:a,checks:c,userinfo:s,profile:o.profile??ra,account:o.account??rs}}const ra=o=>{return rc({id:o.sub??o.id??crypto.randomUUID(),name:o.name??o.nickname??o.preferred_username,email:o.email,image:o.picture})};const rs=o=>{return rc({access_token:o.access_token,id_token:o.id_token,refresh_token:o.refresh_token,expires_at:o.expires_at,scope:o.scope,token_type:o.token_type,session_state:o.session_state})};function rc(o){const i={};for(const[a,s]of Object.entries(o)){if(s!==undefined)i[a]=s}return i}function ru(o,i){if(!o&&i)return;if(typeof o==="string"){return{url:new URL(o)}}const a=new URL(o?.url??"https://authjs.dev");if(o?.params!=null){for(let[i,s]of Object.entries(o.params)){if(i==="claims"){s=JSON.stringify(s)}a.searchParams.set(i,String(s))}}return{url:a,request:o?.request,conform:o?.conform,...o?.clientPrivateKey?{clientPrivateKey:o?.clientPrivateKey}:null}}function rl(o){return o.type==="oidc"}function rd(o){return o.type==="oauth"}function rf(o){return o.type==="oauth"||o.type==="oidc"};const rp={signIn(){return true},redirect({url:o,baseUrl:i}){if(o.startsWith("/"))return`${i}${o}`;else if(new URL(o).origin===i)return o;return i},session({session:o}){return{user:{name:o.user?.name,email:o.user?.email,image:o.user?.image},expires:o.expires?.toISOString?.()??o.expires}},jwt({token:o}){return o}};async function rh({authOptions:o,providerId:i,action:a,url:s,cookies:c,callbackUrl:u,csrfToken:l,csrfDisabled:d,isPost:f}){const p=nV(o);const{providers:h,provider:y}=ro({url:s,providerId:i,config:o});const g=30*24*60*60;let m=false;if((y?.type==="oauth"||y?.type==="oidc")&&y.redirectProxyUrl){try{m=new URL(y.redirectProxyUrl).origin===s.origin}catch{throw new TypeError(`redirectProxyUrl must be a valid URL. Received: ${y.redirectProxyUrl}`)}}const b={debug:false,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...o,url:s,action:a,provider:y,cookies:n7(w(o.useSecureCookies??s.protocol==="https:"),o.cookies),providers:h,session:{strategy:o.adapter?"database":"jwt",maxAge:g,updateAge:24*60*60,generateSessionToken:()=>crypto.randomUUID(),...o.session},jwt:{secret:o.secret,maxAge:o.session?.maxAge??g,encode:nL,decode:n$,...o.jwt},events:ry(o.events??{},p),adapter:rg(o.adapter,p),callbacks:{...rp,...o.callbacks},logger:p,callbackUrl:s.origin,isOnRedirectProxy:m,experimental:{...o.experimental}};const _=[];if(d){b.csrfTokenVerified=true}else{const{csrfToken:o,cookie:i,csrfTokenVerified:a}=await n5({options:b,cookieValue:c?.[b.cookies.csrfToken.name],isPost:f,bodyValue:l});b.csrfToken=o;b.csrfTokenVerified=a;if(i){_.push({name:b.cookies.csrfToken.name,value:i,options:b.cookies.csrfToken.options})}}const{callbackUrl:v,callbackUrlCookie:E}=await nK({options:b,cookieValue:c?.[b.cookies.callbackUrl.name],paramValue:u});b.callbackUrl=v;if(E){_.push({name:b.cookies.callbackUrl.name,value:E,options:b.cookies.callbackUrl.options})}return{options:b,cookies:_}}function ry(o,i){return Object.keys(o).reduce((a,s)=>{a[s]=async(...a)=>{try{const i=o[s];return await i(...a)}catch(o){i.error(new k(o))}};return a},{})}function rg(o,i){if(!o)return;return Object.keys(o).reduce((a,s)=>{a[s]=async(...a)=>{try{i.debug(`adapter_${s}`,{args:a});const c=o[s];return await c(...a)}catch(a){const o=new S(a);i.error(o);throw o}};return a},{})};var rm,rb,rw,r_,rv,rE,rS,rR,rA,rP,rk,rO,rT={},rx=[],rC=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rj=Array.isArray;function rN(o,i){for(var a in i)o[a]=i[a];return o}function rI(o){o&&o.parentNode&&o.parentNode.removeChild(o)}function rD(o,i,a){var s,c,u,l={};for(u in i)"key"==u?s=i[u]:"ref"==u?c=i[u]:l[u]=i[u];if(arguments.length>2&&(l.children=arguments.length>3?rm.call(arguments,2):a),"function"==typeof o&&null!=o.defaultProps)for(u in o.defaultProps)void 0===l[u]&&(l[u]=o.defaultProps[u]);return rU(o,l,s,c,null)}function rU(o,i,a,s,c){var u={type:o,props:i,key:a,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==c?++rw:c,__i:-1,__u:0};return null==c&&null!=rb.vnode&&rb.vnode(u),u}function rM(){return{current:null}}function rL(o){return o.children}function r$(o,i){this.props=o,this.context=i}function rH(o,i){if(null==i)return o.__?rH(o.__,o.__i+1):null;for(var a;i<o.__k.length;i++)if(null!=(a=o.__k[i])&&null!=a.__e)return a.__e;return"function"==typeof o.type?rH(o):null}function rW(o){var i,a;if(null!=(o=o.__)&&null!=o.__c){for(o.__e=o.__c.base=null,i=0;i<o.__k.length;i++)if(null!=(a=o.__k[i])&&null!=a.__e){o.__e=o.__c.base=a.__e;break}return rW(o)}}function rK(o){(!o.__d&&(o.__d=!0)&&rv.push(o)&&!rB.__r++||rE!==rb.debounceRendering)&&((rE=rb.debounceRendering)||rS)(rB)}function rB(){var o,i,a,s,c,u,l,d;for(rv.sort(rR);o=rv.shift();)o.__d&&(i=rv.length,s=void 0,u=(c=(a=o).__v).__e,l=[],d=[],a.__P&&((s=rN({},c)).__v=c.__v+1,rb.vnode&&rb.vnode(s),rZ(a.__P,s,c,a.__n,a.__P.namespaceURI,32&c.__u?[u]:null,l,null==u?rH(c):u,!!(32&c.__u),d),s.__v=c.__v,s.__.__k[s.__i]=s,rQ(l,s,d),s.__e!=u&&rW(s)),rv.length>i&&rv.sort(rR));rB.__r=0}function rq(o,i,a,s,c,u,l,d,f,p,h){var y,g,m,b,w,_=s&&s.__k||rx,v=i.length;for(a.__d=f,rG(a,i,_),f=a.__d,y=0;y<v;y++)null!=(m=a.__k[y])&&(g=-1===m.__i?rT:_[m.__i]||rT,m.__i=y,rZ(o,m,g,c,u,l,d,f,p,h),b=m.__e,m.ref&&g.ref!=m.ref&&(g.ref&&r1(g.ref,null,m),h.push(m.ref,m.__c||b,m)),null==w&&null!=b&&(w=b),65536&m.__u||g.__k===m.__k?f=rF(m,f,o):"function"==typeof m.type&&void 0!==m.__d?f=m.__d:b&&(f=b.nextSibling),m.__d=void 0,m.__u&=-196609);a.__d=f,a.__e=w}function rG(o,i,a){var s,c,u,l,d,f=i.length,p=a.length,h=p,y=0;for(o.__k=[],s=0;s<f;s++)null!=(c=i[s])&&"boolean"!=typeof c&&"function"!=typeof c?(l=s+y,(c=o.__k[s]="string"==typeof c||"number"==typeof c||"bigint"==typeof c||c.constructor==String?rU(null,c,null,null,null):rj(c)?rU(rL,{children:c},null,null,null):void 0===c.constructor&&c.__b>0?rU(c.type,c.props,c.key,c.ref?c.ref:null,c.__v):c).__=o,c.__b=o.__b+1,u=null,-1!==(d=c.__i=rV(c,a,l,h))&&(h--,(u=a[d])&&(u.__u|=131072)),null==u||null===u.__v?(-1==d&&y--,"function"!=typeof c.type&&(c.__u|=65536)):d!==l&&(d==l-1?y--:d==l+1?y++:(d>l?y--:y++,c.__u|=65536))):c=o.__k[s]=null;if(h)for(s=0;s<p;s++)null!=(u=a[s])&&0==(131072&u.__u)&&(u.__e==o.__d&&(o.__d=rH(u)),r2(u,u))}function rF(o,i,a){var s,c;if("function"==typeof o.type){for(s=o.__k,c=0;s&&c<s.length;c++)s[c]&&(s[c].__=o,i=rF(s[c],i,a));return i}o.__e!=i&&(i&&o.type&&!a.contains(i)&&(i=rH(o)),a.insertBefore(o.__e,i||null),i=o.__e);do{i=i&&i.nextSibling}while(null!=i&&8===i.nodeType);return i}function rJ(o,i){return i=i||[],null==o||"boolean"==typeof o||(rj(o)?o.some(function(o){rJ(o,i)}):i.push(o)),i}function rV(o,i,a,s){var c=o.key,u=o.type,l=a-1,d=a+1,f=i[a];if(null===f||f&&c==f.key&&u===f.type&&0==(131072&f.__u))return a;if(s>(null!=f&&0==(131072&f.__u)?1:0))for(;l>=0||d<i.length;){if(l>=0){if((f=i[l])&&0==(131072&f.__u)&&c==f.key&&u===f.type)return l;l--}if(d<i.length){if((f=i[d])&&0==(131072&f.__u)&&c==f.key&&u===f.type)return d;d++}}return-1}function rz(o,i,a){"-"===i[0]?o.setProperty(i,null==a?"":a):o[i]=null==a?"":"number"!=typeof a||rC.test(i)?a:a+"px"}function rX(o,i,a,s,c){var u;e:if("style"===i)if("string"==typeof a)o.style.cssText=a;else{if("string"==typeof s&&(o.style.cssText=s=""),s)for(i in s)a&&i in a||rz(o.style,i,"");if(a)for(i in a)s&&a[i]===s[i]||rz(o.style,i,a[i])}else if("o"===i[0]&&"n"===i[1])u=i!==(i=i.replace(/(PointerCapture)$|Capture$/i,"$1")),i=i.toLowerCase()in o||"onFocusOut"===i||"onFocusIn"===i?i.toLowerCase().slice(2):i.slice(2),o.l||(o.l={}),o.l[i+u]=a,a?s?a.u=s.u:(a.u=rA,o.addEventListener(i,u?rk:rP,u)):o.removeEventListener(i,u?rk:rP,u);else{if("http://www.w3.org/2000/svg"==c)i=i.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=i&&"height"!=i&&"href"!=i&&"list"!=i&&"form"!=i&&"tabIndex"!=i&&"download"!=i&&"rowSpan"!=i&&"colSpan"!=i&&"role"!=i&&"popover"!=i&&i in o)try{o[i]=null==a?"":a;break e}catch(o){}"function"==typeof a||(null==a||!1===a&&"-"!==i[4]?o.removeAttribute(i):o.setAttribute(i,"popover"==i&&1==a?"":a))}}function rY(o){return function(i){if(this.l){var a=this.l[i.type+o];if(null==i.t)i.t=rA++;else if(i.t<a.u)return;return a(rb.event?rb.event(i):i)}}}function rZ(o,i,a,s,c,u,l,d,f,p){var h,y,g,m,b,w,_,v,E,S,R,A,P,k,O,T,x=i.type;if(void 0!==i.constructor)return null;128&a.__u&&(f=!!(32&a.__u),u=[d=i.__e=a.__e]),(h=rb.__b)&&h(i);e:if("function"==typeof x)try{if(v=i.props,E="prototype"in x&&x.prototype.render,S=(h=x.contextType)&&s[h.__c],R=h?S?S.props.value:h.__:s,a.__c?_=(y=i.__c=a.__c).__=y.__E:(E?i.__c=y=new x(v,R):(i.__c=y=new r$(v,R),y.constructor=x,y.render=r3),S&&S.sub(y),y.props=v,y.state||(y.state={}),y.context=R,y.__n=s,g=y.__d=!0,y.__h=[],y._sb=[]),E&&null==y.__s&&(y.__s=y.state),E&&null!=x.getDerivedStateFromProps&&(y.__s==y.state&&(y.__s=rN({},y.__s)),rN(y.__s,x.getDerivedStateFromProps(v,y.__s))),m=y.props,b=y.state,y.__v=i,g)E&&null==x.getDerivedStateFromProps&&null!=y.componentWillMount&&y.componentWillMount(),E&&null!=y.componentDidMount&&y.__h.push(y.componentDidMount);else{if(E&&null==x.getDerivedStateFromProps&&v!==m&&null!=y.componentWillReceiveProps&&y.componentWillReceiveProps(v,R),!y.__e&&(null!=y.shouldComponentUpdate&&!1===y.shouldComponentUpdate(v,y.__s,R)||i.__v===a.__v)){for(i.__v!==a.__v&&(y.props=v,y.state=y.__s,y.__d=!1),i.__e=a.__e,i.__k=a.__k,i.__k.some(function(o){o&&(o.__=i)}),A=0;A<y._sb.length;A++)y.__h.push(y._sb[A]);y._sb=[],y.__h.length&&l.push(y);break e}null!=y.componentWillUpdate&&y.componentWillUpdate(v,y.__s,R),E&&null!=y.componentDidUpdate&&y.__h.push(function(){y.componentDidUpdate(m,b,w)})}if(y.context=R,y.props=v,y.__P=o,y.__e=!1,P=rb.__r,k=0,E){for(y.state=y.__s,y.__d=!1,P&&P(i),h=y.render(y.props,y.state,y.context),O=0;O<y._sb.length;O++)y.__h.push(y._sb[O]);y._sb=[]}else do{y.__d=!1,P&&P(i),h=y.render(y.props,y.state,y.context),y.state=y.__s}while(y.__d&&++k<25);y.state=y.__s,null!=y.getChildContext&&(s=rN(rN({},s),y.getChildContext())),E&&!g&&null!=y.getSnapshotBeforeUpdate&&(w=y.getSnapshotBeforeUpdate(m,b)),rq(o,rj(T=null!=h&&h.type===rL&&null==h.key?h.props.children:h)?T:[T],i,a,s,c,u,l,d,f,p),y.base=i.__e,i.__u&=-161,y.__h.length&&l.push(y),_&&(y.__E=y.__=null)}catch(o){if(i.__v=null,f||null!=u){for(i.__u|=f?160:128;d&&8===d.nodeType&&d.nextSibling;)d=d.nextSibling;u[u.indexOf(d)]=null,i.__e=d}else i.__e=a.__e,i.__k=a.__k;rb.__e(o,i,a)}else null==u&&i.__v===a.__v?(i.__k=a.__k,i.__e=a.__e):i.__e=r0(a.__e,i,a,s,c,u,l,f,p);(h=rb.diffed)&&h(i)}function rQ(o,i,a){i.__d=void 0;for(var s=0;s<a.length;s++)r1(a[s],a[++s],a[++s]);rb.__c&&rb.__c(i,o),o.some(function(i){try{o=i.__h,i.__h=[],o.some(function(o){o.call(i)})}catch(o){rb.__e(o,i.__v)}})}function r0(o,i,a,s,c,u,l,d,f){var p,h,y,g,m,b,w,_=a.props,v=i.props,E=i.type;if("svg"===E?c="http://www.w3.org/2000/svg":"math"===E?c="http://www.w3.org/1998/Math/MathML":c||(c="http://www.w3.org/1999/xhtml"),null!=u){for(p=0;p<u.length;p++)if((m=u[p])&&"setAttribute"in m==!!E&&(E?m.localName===E:3===m.nodeType)){o=m,u[p]=null;break}}if(null==o){if(null===E)return document.createTextNode(v);o=document.createElementNS(c,E,v.is&&v),d&&(rb.__m&&rb.__m(i,u),d=!1),u=null}if(null===E)_===v||d&&o.data===v||(o.data=v);else{if(u=u&&rm.call(o.childNodes),_=a.props||rT,!d&&null!=u)for(_={},p=0;p<o.attributes.length;p++)_[(m=o.attributes[p]).name]=m.value;for(p in _)if(m=_[p],"children"==p);else if("dangerouslySetInnerHTML"==p)y=m;else if(!(p in v)){if("value"==p&&"defaultValue"in v||"checked"==p&&"defaultChecked"in v)continue;rX(o,p,null,m,c)}for(p in v)m=v[p],"children"==p?g=m:"dangerouslySetInnerHTML"==p?h=m:"value"==p?b=m:"checked"==p?w=m:d&&"function"!=typeof m||_[p]===m||rX(o,p,m,_[p],c);if(h)d||y&&(h.__html===y.__html||h.__html===o.innerHTML)||(o.innerHTML=h.__html),i.__k=[];else if(y&&(o.innerHTML=""),rq(o,rj(g)?g:[g],i,a,s,"foreignObject"===E?"http://www.w3.org/1999/xhtml":c,u,l,u?u[0]:a.__k&&rH(a,0),d,f),null!=u)for(p=u.length;p--;)rI(u[p]);d||(p="value","progress"===E&&null==b?o.removeAttribute("value"):void 0!==b&&(b!==o[p]||"progress"===E&&!b||"option"===E&&b!==_[p])&&rX(o,p,b,_[p],c),p="checked",void 0!==w&&w!==o[p]&&rX(o,p,w,_[p],c))}return o}function r1(o,i,a){try{if("function"==typeof o){var s="function"==typeof o.__u;s&&o.__u(),s&&null==i||(o.__u=o(i))}else o.current=i}catch(o){rb.__e(o,a)}}function r2(o,i,a){var s,c;if(rb.unmount&&rb.unmount(o),(s=o.ref)&&(s.current&&s.current!==o.__e||r1(s,null,i)),null!=(s=o.__c)){if(s.componentWillUnmount)try{s.componentWillUnmount()}catch(o){rb.__e(o,i)}s.base=s.__P=null}if(s=o.__k)for(c=0;c<s.length;c++)s[c]&&r2(s[c],i,a||"function"!=typeof o.type);a||rI(o.__e),o.__c=o.__=o.__e=o.__d=void 0}function r3(o,i,a){return this.constructor(o,a)}function r6(o,i,a){var s,c,u,l;rb.__&&rb.__(o,i),c=(s="function"==typeof a)?null:a&&a.__k||i.__k,u=[],l=[],rZ(i,o=(!s&&a||i).__k=rD(rL,null,[o]),c||rT,rT,i.namespaceURI,!s&&a?[a]:c?null:i.firstChild?rm.call(i.childNodes):null,u,!s&&a?a:c?c.__e:i.firstChild,s,l),rQ(u,o,l)}function r8(o,i){r6(o,i,r8)}function r5(o,i,a){var s,c,u,l,d=rN({},o.props);for(u in o.type&&o.type.defaultProps&&(l=o.type.defaultProps),i)"key"==u?s=i[u]:"ref"==u?c=i[u]:d[u]=void 0===i[u]&&void 0!==l?l[u]:i[u];return arguments.length>2&&(d.children=arguments.length>3?rm.call(arguments,2):a),rU(o.type,d,s||o.key,c||o.ref,null)}function r4(o,i){var a={__c:i="__cC"+rO++,__:o,Consumer:function(o,i){return o.children(i)},Provider:function(o){var a,s;return this.getChildContext||(a=new Set,(s={})[i]=this,this.getChildContext=function(){return s},this.componentWillUnmount=function(){a=null},this.shouldComponentUpdate=function(o){this.props.value!==o.value&&a.forEach(function(o){o.__e=!0,rK(o)})},this.sub=function(o){a.add(o);var i=o.componentWillUnmount;o.componentWillUnmount=function(){a&&a.delete(o),i&&i.call(o)}}),o.children}};return a.Provider.__=a.Consumer.contextType=a}rm=rx.slice,rb={__e:function(o,i,a,s){for(var c,u,l;i=i.__;)if((c=i.__c)&&!c.__)try{if((u=c.constructor)&&null!=u.getDerivedStateFromError&&(c.setState(u.getDerivedStateFromError(o)),l=c.__d),null!=c.componentDidCatch&&(c.componentDidCatch(o,s||{}),l=c.__d),l)return c.__E=c}catch(i){o=i}throw o}},rw=0,r_=function(o){return null!=o&&null==o.constructor},r$.prototype.setState=function(o,i){var a;a=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rN({},this.state),"function"==typeof o&&(o=o(rN({},a),this.props)),o&&rN(a,o),null!=o&&this.__v&&(i&&this._sb.push(i),rK(this))},r$.prototype.forceUpdate=function(o){this.__v&&(this.__e=!0,o&&this.__h.push(o),rK(this))},r$.prototype.render=rL,rv=[],rS="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rR=function(o,i){return o.__v.__b-i.__v.__b},rB.__r=0,rA=0,rP=rY(!1),rk=rY(!0),rO=0;var r9=/[\s\n\\/='"\0<>]/,r7=/^(xlink|xmlns|xml)([A-Z])/,oe=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,ot=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,on=new Set(["draggable","spellcheck"]),or=/["&<]/;function oo(o){if(0===o.length||!1===or.test(o))return o;for(var i=0,a=0,s="",c="";a<o.length;a++){switch(o.charCodeAt(a)){case 34:c="&quot;";break;case 38:c="&amp;";break;case 60:c="&lt;";break;default:continue}a!==i&&(s+=o.slice(i,a)),s+=c,i=a+1}return a!==i&&(s+=o.slice(i,a)),s}var oi={},oa=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),os=/[A-Z]/g;function oc(o){var i="";for(var a in o){var s=o[a];if(null!=s&&""!==s){var c="-"==a[0]?a:oi[a]||(oi[a]=a.replace(os,"-$&").toLowerCase()),u=";";"number"!=typeof s||c.startsWith("--")||oa.has(c)||(u="px;"),i=i+c+":"+s+u}}return i||void 0}function ou(){this.__d=!0}function ol(o,i){return{__v:o,context:i,props:o.props,setState:ou,forceUpdate:ou,__d:!0,__h:new Array(0)}}function od(o,i,a){if(!o.s){if(a instanceof of){if(!a.s)return void(a.o=od.bind(null,o,i));1&i&&(i=a.s),a=a.v}if(a&&a.then)return void a.then(od.bind(null,o,i),od.bind(null,o,2));o.s=i,o.v=a;const s=o.o;s&&s(o)}}var of=null&&function(){function o(){}return o.prototype.then=function(i,a){var s=new o,c=this.s;if(c){var u=1&c?i:a;if(u){try{od(s,1,u(this.v))}catch(o){od(s,2,o)}return s}return this}return this.o=function(o){try{var c=o.v;1&o.s?od(s,1,i?i(c):c):a?od(s,1,a(c)):od(s,2,c)}catch(o){od(s,2,o)}},s},o}();function op(o){return o instanceof of&&1&o.s}function oh(o,i,a){for(var s;;){var c=o();if(op(c)&&(c=c.v),!c)return u;if(c.then){s=0;break}var u=a();if(u&&u.then){if(!op(u)){s=1;break}u=u.s}if(i){var l=i();if(l&&l.then&&!op(l)){s=2;break}}}var d=new of,f=od.bind(null,d,2);return(0===s?c.then(h):1===s?u.then(p):l.then(y)).then(void 0,f),d;function p(s){u=s;do{if(i&&(l=i())&&l.then&&!op(l))return void l.then(y).then(void 0,f);if(!(c=o())||op(c)&&!c.v)return void od(d,1,u);if(c.then)return void c.then(h).then(void 0,f);op(u=a())&&(u=u.v)}while(!u||!u.then);u.then(p).then(void 0,f)}function h(o){o?(u=a())&&u.then?u.then(p).then(void 0,f):p(u):od(d,1,u)}function y(){(c=o())?c.then?c.then(h).then(void 0,f):h(c):od(d,1,u)}}function oy(o,i){try{var a=o()}catch(o){return i(!0,o)}return a&&a.then?a.then(i.bind(null,!1),i.bind(null,!0)):i(!1,a)}var og,om,ob,ow,o_=function(o,i){try{var a=e.__s;e.__s=!0,og=e.__b,om=e.diffed,ob=e.__r,ow=e.unmount;var s=t(n,null);return s.__k=[o],Promise.resolve(oy(function(){return Promise.resolve(oO(o,i||ov,!1,void 0,s,!0,void 0)).then(function(o){var i,a=function(){if(oS(o)){var a=function(){var o=c.join(oA);return i=1,o},s=0,c=o,u=oh(function(){return!!c.some(function(o){return o&&"function"==typeof o.then})&&s++<25},void 0,function(){return Promise.resolve(Promise.all(c)).then(function(o){c=o.flat()})});return u&&u.then?u.then(a):a()}}();return a&&a.then?a.then(function(a){return i?a:o}):i?a:o})},function(i,s){if(e.__c&&e.__c(o,oE),e.__s=a,oE.length=0,i)throw s;return s}))}catch(o){return Promise.reject(o)}},ov={},oE=[],oS=Array.isArray,oR=Object.assign,oA="";function oP(o,i,a){var s=rb.__s;rb.__s=!0,og=rb.__b,om=rb.diffed,ob=rb.__r,ow=rb.unmount;var c=rD(rL,null);c.__k=[o];try{var u=oO(o,i||ov,!1,void 0,c,!1,a);return oS(u)?u.join(oA):u}catch(o){if(o.then)throw new Error('Use "renderToStringAsync" for suspenseful rendering.');throw o}finally{rb.__c&&rb.__c(o,oE),rb.__s=s,oE.length=0}}function ok(o,i){var a,s=o.type,c=!0;return o.__c?(c=!1,(a=o.__c).state=a.__s):a=new s(o.props,i),o.__c=a,a.__v=o,a.props=o.props,a.context=i,a.__d=!0,null==a.state&&(a.state=ov),null==a.__s&&(a.__s=a.state),s.getDerivedStateFromProps?a.state=oR({},a.state,s.getDerivedStateFromProps(a.props,a.state)):c&&a.componentWillMount?(a.componentWillMount(),a.state=a.__s!==a.state?a.__s:a.state):!c&&a.componentWillUpdate&&a.componentWillUpdate(),ob&&ob(o),a.render(a.props,a.state,i)}function oO(o,i,a,s,c,u,l){if(null==o||!0===o||!1===o||o===oA)return oA;var d=typeof o;if("object"!=d)return"function"==d?oA:"string"==d?oo(o):o+oA;if(oS(o)){var f,p=oA;c.__k=o;for(var h=0;h<o.length;h++){var y=o[h];if(null!=y&&"boolean"!=typeof y){var g,m=oO(y,i,a,s,c,u,l);"string"==typeof m?p+=m:(f||(f=[]),p&&f.push(p),p=oA,oS(m)?(g=f).push.apply(g,m):f.push(m))}}return f?(p&&f.push(p),f):p}if(void 0!==o.constructor)return oA;o.__=c,og&&og(o);var b=o.type,w=o.props;if("function"==typeof b){var _,v,E,S=i;if(b===rL){if("tpl"in w){for(var R=oA,A=0;A<w.tpl.length;A++)if(R+=w.tpl[A],w.exprs&&A<w.exprs.length){var P=w.exprs[A];if(null==P)continue;"object"!=typeof P||void 0!==P.constructor&&!oS(P)?R+=P:R+=oO(P,i,a,s,o,u,l)}return R}if("UNSTABLE_comment"in w)return"\x3c!--"+oo(w.UNSTABLE_comment)+"--\x3e";v=w.children}else{if(null!=(_=b.contextType)){var k=i[_.__c];S=k?k.props.value:_.__}var O=b.prototype&&"function"==typeof b.prototype.render;if(O)v=ok(o,S),E=o.__c;else{o.__c=E=ol(o,S);for(var T=0;E.__d&&T++<25;)E.__d=!1,ob&&ob(o),v=b.call(E,w,S);E.__d=!0}if(null!=E.getChildContext&&(i=oR({},i,E.getChildContext())),O&&rb.errorBoundaries&&(b.getDerivedStateFromError||E.componentDidCatch)){v=null!=v&&v.type===rL&&null==v.key&&null==v.props.tpl?v.props.children:v;try{return oO(v,i,a,s,o,u,l)}catch(c){return b.getDerivedStateFromError&&(E.__s=b.getDerivedStateFromError(c)),E.componentDidCatch&&E.componentDidCatch(c,ov),E.__d?(v=ok(o,i),null!=(E=o.__c).getChildContext&&(i=oR({},i,E.getChildContext())),oO(v=null!=v&&v.type===rL&&null==v.key&&null==v.props.tpl?v.props.children:v,i,a,s,o,u,l)):oA}finally{om&&om(o),o.__=null,ow&&ow(o)}}}v=null!=v&&v.type===rL&&null==v.key&&null==v.props.tpl?v.props.children:v;try{var x=oO(v,i,a,s,o,u,l);return om&&om(o),o.__=null,rb.unmount&&rb.unmount(o),x}catch(c){if(!u&&l&&l.onError){var C=l.onError(c,o,function(c){return oO(c,i,a,s,o,u,l)});if(void 0!==C)return C;var j=rb.__e;return j&&j(c,o),oA}if(!u)throw c;if(!c||"function"!=typeof c.then)throw c;return c.then(function c(){try{return oO(v,i,a,s,o,u,l)}catch(d){if(!d||"function"!=typeof d.then)throw d;return d.then(function(){return oO(v,i,a,s,o,u,l)},c)}})}}var N,I="<"+b,D=oA;for(var U in w){var M=w[U];if("function"!=typeof M||"class"===U||"className"===U){switch(U){case"children":N=M;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in w)continue;U="for";break;case"className":if("class"in w)continue;U="class";break;case"defaultChecked":U="checked";break;case"defaultSelected":U="selected";break;case"defaultValue":case"value":switch(U="value",b){case"textarea":N=M;continue;case"select":s=M;continue;case"option":s!=M||"selected"in w||(I+=" selected")}break;case"dangerouslySetInnerHTML":D=M&&M.__html;continue;case"style":"object"==typeof M&&(M=oc(M));break;case"acceptCharset":U="accept-charset";break;case"httpEquiv":U="http-equiv";break;default:if(r7.test(U))U=U.replace(r7,"$1:$2").toLowerCase();else{if(r9.test(U))continue;"-"!==U[4]&&!on.has(U)||null==M?a?ot.test(U)&&(U="panose1"===U?"panose-1":U.replace(/([A-Z])/g,"-$1").toLowerCase()):oe.test(U)&&(U=U.toLowerCase()):M+=oA}}null!=M&&!1!==M&&(I=!0===M||M===oA?I+" "+U:I+" "+U+'="'+("string"==typeof M?oo(M):M+oA)+'"')}}if(r9.test(b))throw new Error(b+" is not a valid HTML tag name in "+I+">");if(D||("string"==typeof N?D=oo(N):null!=N&&!1!==N&&!0!==N&&(D=oO(N,i,"svg"===b||"foreignObject"!==b&&a,s,o,u,l))),om&&om(o),o.__=null,ow&&ow(o),!D&&oT.has(b))return I+"/>";var L="</"+b+">",$=I+">";return oS(D)?[$].concat(D,[L]):"string"!=typeof D?[$,D,L]:$+D+L}var oT=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),ox=null&&oP,oC=null&&oP;const oj=null&&oP;var oN=/["&<]/;function oI(o){if(0===o.length||!1===oN.test(o))return o;for(var i=0,a=0,s="",c="";a<o.length;a++){switch(o.charCodeAt(a)){case 34:c="&quot;";break;case 38:c="&amp;";break;case 60:c="&lt;";break;default:continue}a!==i&&(s+=o.slice(i,a)),s+=c,i=a+1}return a!==i&&(s+=o.slice(i,a)),s}var oD=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,oU=0,oM=Array.isArray;function oL(o,i,a,s,c,u){i||(i={});var l,d,f=i;"ref"in i&&(l=i.ref,delete i.ref);var p={type:o,props:f,key:a,ref:l,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--oU,__i:-1,__u:0,__source:c,__self:u};if("function"==typeof o&&(l=o.defaultProps))for(d in l)void 0===f[d]&&(f[d]=l[d]);return rb.vnode&&rb.vnode(p),p}function o$(o){var i=oL(e,{tpl:o,exprs:[].slice.call(arguments,1)});return i.key=i.__v,i}var oH={},oW=/[A-Z]/g;function oK(o,i){if(r.attr){var a=r.attr(o,i);if("string"==typeof a)return a}if("ref"===o||"key"===o)return"";if("style"===o&&"object"==typeof i){var s="";for(var c in i){var u=i[c];if(null!=u&&""!==u){var l="-"==c[0]?c:oH[c]||(oH[c]=c.replace(oW,"-$&").toLowerCase()),d=";";"number"!=typeof u||l.startsWith("--")||oD.test(l)||(d="px;"),s=s+l+":"+u+d}}return o+'="'+s+'"'}return null==i||!1===i||"function"==typeof i||"object"==typeof i?"":!0===i?o:o+'="'+oI(i)+'"'}function oB(o){if(null==o||"boolean"==typeof o||"function"==typeof o)return null;if("object"==typeof o){if(void 0===o.constructor)return o;if(oM(o)){for(var i=0;i<o.length;i++)o[i]=oB(o[i]);return o}}return oI(""+o)};function oq(o){const{url:i,error:a="default",theme:s}=o;const c=`${i}/signin`;const u={default:{status:200,heading:"Error",message:oL("p",{children:oL("a",{className:"site",href:i?.origin,children:i?.host})})},Configuration:{status:500,heading:"Server error",message:oL("div",{children:[oL("p",{children:"There is a problem with the server configuration."}),oL("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:oL("div",{children:[oL("p",{children:"You do not have permission to sign in."}),oL("p",{children:oL("a",{className:"button",href:c,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:oL("div",{children:[oL("p",{children:"The sign in link is no longer valid."}),oL("p",{children:"It may have been used already or it may have expired."})]}),signin:oL("a",{className:"button",href:c,children:"Sign in"})}};const{status:l,heading:d,message:f,signin:p}=u[a]??u.default;return{status:l,html:oL("div",{className:"error",children:[s?.brandColor&&oL("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${s?.brandColor}
        }
      `}}),oL("div",{className:"card",children:[s?.logo&&oL("img",{src:s?.logo,alt:"Logo",className:"logo"}),oL("h1",{children:d}),oL("div",{className:"message",children:f}),p]})]})}};async function oG(o,i){const a=window.SimpleWebAuthnBrowser;async function s(a){const s=new URL(`${o}/webauthn-options/${i}`);if(a)s.searchParams.append("action",a);const c=u();c.forEach(o=>{s.searchParams.append(o.name,o.value)});const l=await fetch(s);if(!l.ok){console.error("Failed to fetch options",l);return}return l.json()}function c(){const o=`#${i}-form`;const a=document.querySelector(o);if(!a)throw new Error(`Form '${o}' not found`);return a}function u(){const o=c();const i=Array.from(o.querySelectorAll("input[data-form-field]"));return i}async function l(o,i){const a=c();if(o){const i=document.createElement("input");i.type="hidden";i.name="action";i.value=o;a.appendChild(i)}if(i){const o=document.createElement("input");o.type="hidden";o.name="data";o.value=JSON.stringify(i);a.appendChild(o)}return a.submit()}async function d(o,i){const s=await a.startAuthentication(o,i);return await l("authenticate",s)}async function f(o){const i=u();i.forEach(o=>{if(o.required&&!o.value){throw new Error(`Missing required field: ${o.name}`)}});const s=await a.startRegistration(o);return await l("register",s)}async function p(){if(!a.browserSupportsWebAuthnAutofill())return;const o=await s("authenticate");if(!o){console.error("Failed to fetch option for autofill authentication");return}try{await d(o.options,true)}catch(o){console.error(o)}}async function h(){const o=c();if(!a.browserSupportsWebAuthn()){o.style.display="none";return}if(o){o.addEventListener("submit",async o=>{o.preventDefault();const i=await s(undefined);if(!i){console.error("Failed to fetch options for form submission");return}if(i.action==="authenticate"){try{await d(i.options,false)}catch(o){console.error(o)}}else if(i.action==="register"){try{await f(i.options)}catch(o){console.error(o)}}})}}h();p()};const oF={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."};function oJ(o){const i=`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${oG})(authURL, "${o}");
`;return oL(rL,{children:oL("script",{dangerouslySetInnerHTML:{__html:i}})})}function oV(o){const{csrfToken:i,providers:a=[],callbackUrl:s,theme:c,email:u,error:l}=o;if(typeof document!=="undefined"&&c?.brandColor){document.documentElement.style.setProperty("--brand-color",c.brandColor)}if(typeof document!=="undefined"&&c?.buttonText){document.documentElement.style.setProperty("--button-text-color",c.buttonText)}const d=l&&(oF[l]??oF.default);const f="https://authjs.dev/img/providers";const p=a.find(o=>o.type==="webauthn"&&o.enableConditionalUI)?.id;return oL("div",{className:"signin",children:[c?.brandColor&&oL("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${c.brandColor}}`}}),c?.buttonText&&oL("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${c.buttonText}
        }
      `}}),oL("div",{className:"card",children:[d&&oL("div",{className:"error",children:oL("p",{children:d})}),c?.logo&&oL("img",{src:c.logo,alt:"Logo",className:"logo"}),a.map((o,c)=>{let l,d,p;if(o.type==="oauth"||o.type==="oidc"){;({bg:l="#fff",brandColor:d,logo:p=`${f}/${o.id}.svg`}=o.style??{})}const h=d??l??"#fff";return oL("div",{className:"provider",children:[o.type==="oauth"||o.type==="oidc"?oL("form",{action:o.signinUrl,method:"POST",children:[oL("input",{type:"hidden",name:"csrfToken",value:i}),s&&oL("input",{type:"hidden",name:"callbackUrl",value:s}),oL("button",{type:"submit",className:"button",style:{"--provider-brand-color":h},tabIndex:0,children:[oL("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",o.name]}),p&&oL("img",{loading:"lazy",height:24,src:p})]})]}):null,(o.type==="email"||o.type==="credentials"||o.type==="webauthn")&&c>0&&a[c-1].type!=="email"&&a[c-1].type!=="credentials"&&a[c-1].type!=="webauthn"&&oL("hr",{}),o.type==="email"&&oL("form",{action:o.signinUrl,method:"POST",children:[oL("input",{type:"hidden",name:"csrfToken",value:i}),oL("label",{className:"section-header",htmlFor:`input-email-for-${o.id}-provider`,children:"Email"}),oL("input",{id:`input-email-for-${o.id}-provider`,autoFocus:true,type:"email",name:"email",value:u,placeholder:"<EMAIL>",required:true}),oL("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",o.name]})]}),o.type==="credentials"&&oL("form",{action:o.callbackUrl,method:"POST",children:[oL("input",{type:"hidden",name:"csrfToken",value:i}),Object.keys(o.credentials).map(i=>{return oL("div",{children:[oL("label",{className:"section-header",htmlFor:`input-${i}-for-${o.id}-provider`,children:o.credentials[i].label??i}),oL("input",{name:i,id:`input-${i}-for-${o.id}-provider`,type:o.credentials[i].type??"text",placeholder:o.credentials[i].placeholder??"",...o.credentials[i]})]},`input-group-${o.id}`)}),oL("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",o.name]})]}),o.type==="webauthn"&&oL("form",{action:o.callbackUrl,method:"POST",id:`${o.id}-form`,children:[oL("input",{type:"hidden",name:"csrfToken",value:i}),Object.keys(o.formFields).map(i=>{return oL("div",{children:[oL("label",{className:"section-header",htmlFor:`input-${i}-for-${o.id}-provider`,children:o.formFields[i].label??i}),oL("input",{name:i,"data-form-field":true,id:`input-${i}-for-${o.id}-provider`,type:o.formFields[i].type??"text",placeholder:o.formFields[i].placeholder??"",...o.formFields[i]})]},`input-group-${o.id}`)}),oL("button",{id:`submitButton-${o.id}`,type:"submit",tabIndex:0,children:["Sign in with ",o.name]})]}),(o.type==="email"||o.type==="credentials"||o.type==="webauthn")&&c+1<a.length&&oL("hr",{})]},o.id)})]}),p&&oJ(p)]})};function oz(o){const{url:i,csrfToken:a,theme:s}=o;return oL("div",{className:"signout",children:[s?.brandColor&&oL("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${s.brandColor}
        }
      `}}),s?.buttonText&&oL("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${s.buttonText}
        }
      `}}),oL("div",{className:"card",children:[s?.logo&&oL("img",{src:s.logo,alt:"Logo",className:"logo"}),oL("h1",{children:"Signout"}),oL("p",{children:"Are you sure you want to sign out?"}),oL("form",{action:i?.toString(),method:"POST",children:[oL("input",{type:"hidden",name:"csrfToken",value:a}),oL("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})};const oX=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function oY(o){const{url:i,theme:a}=o;return oL("div",{className:"verify-request",children:[a.brandColor&&oL("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${a.brandColor}
        }
      `}}),oL("div",{className:"card",children:[a.logo&&oL("img",{src:a.logo,alt:"Logo",className:"logo"}),oL("h1",{children:"Check your email"}),oL("p",{children:"A sign in link has been sent to your email address."}),oL("p",{children:oL("a",{className:"site",href:i.origin,children:i.host})})]})]})};function oZ({html:o,title:i,status:a,cookies:s,theme:c,headTags:u}){return{cookies:s,status:a,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${oX}</style><title>${i}</title>${u??""}</head><body class="__next-auth-theme-${c?.colorScheme??"auto"}"><div class="page">${oP(o)}</div></body></html>`}}function oQ(o){const{url:i,theme:a,query:s,cookies:c,pages:u,providers:l}=o;return{csrf(o,i,a){if(!o){return{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:i.csrfToken},cookies:a}}i.logger.warn("csrf-disabled");a.push({name:i.cookies.csrfToken.name,value:"",options:{...i.cookies.csrfToken.options,maxAge:0}});return{status:404,cookies:a}},providers(o){return{headers:{"Content-Type":"application/json"},body:o.reduce((o,{id:i,name:a,type:s,signinUrl:c,callbackUrl:u})=>{o[i]={id:i,name:a,type:s,signinUrl:c,callbackUrl:u};return o},{})}},signin(i,d){if(i)throw new q("Unsupported action");if(u?.signIn){let i=`${u.signIn}${u.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:o.callbackUrl??"/"})}`;if(d)i=`${i}&${new URLSearchParams({error:d})}`;return{redirect:i,cookies:c}}const f=l?.find(o=>o.type==="webauthn"&&o.enableConditionalUI&&!!o.simpleWebAuthnBrowserVersion);let p="";if(f){const{simpleWebAuthnBrowserVersion:o}=f;p=`<script src="https://unpkg.com/@simplewebauthn/browser@${o}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return oZ({cookies:c,theme:a,html:oV({csrfToken:o.csrfToken,providers:o.providers?.filter(o=>["email","oauth","oidc"].includes(o.type)||o.type==="credentials"&&o.credentials||o.type==="webauthn"&&o.formFields||false),callbackUrl:o.callbackUrl,theme:o.theme,error:d,...s}),title:"Sign In",headTags:p})},signout(){if(u?.signOut)return{redirect:u.signOut,cookies:c};return oZ({cookies:c,theme:a,html:oz({csrfToken:o.csrfToken,url:i,theme:a}),title:"Sign Out"})},verifyRequest(o){if(u?.verifyRequest)return{redirect:`${u.verifyRequest}${i?.search??""}`,cookies:c};return oZ({cookies:c,theme:a,html:oY({url:i,theme:a,...o}),title:"Verify Request"})},error(o){if(u?.error){return{redirect:`${u.error}${u.error.includes("?")?"&":"?"}error=${o}`,cookies:c}}return oZ({cookies:c,theme:a,...oq({url:i,theme:a,error:o}),title:"Error"})}}};function o0(o,i=Date.now()){return new Date(i+o*1e3)};async function o1(o,i,a,s){if(!a?.providerAccountId||!a.type)throw new Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(a.type))throw new Error("Provider not supported");const{adapter:c,jwt:u,events:l,session:{strategy:d,generateSessionToken:f}}=s;if(!c){return{user:i,account:a}}const p=i;let h=a;const{createUser:y,updateUser:g,getUser:m,getUserByAccount:b,getUserByEmail:w,linkAccount:_,createSession:v,getSessionAndUser:E,deleteSession:S}=c;let R=null;let A=null;let P=false;const k=d==="jwt";if(o){if(k){try{const i=s.cookies.sessionToken.name;R=await u.decode({...u,token:o,salt:i});if(R&&"sub"in R&&R.sub){A=await m(R.sub)}}catch{}}else{const i=await E(o);if(i){R=i.session;A=i.user}}}if(h.type==="email"){const i=await w(p.email);if(i){if(A?.id!==i.id&&!k&&o){await S(o)}A=await g({id:i.id,emailVerified:new Date});await l.updateUser?.({user:A})}else{A=await y({...p,emailVerified:new Date});await l.createUser?.({user:A});P=true}R=k?{}:await v({sessionToken:f(),userId:A.id,expires:o0(s.session.maxAge)});return{session:R,user:A,isNewUser:P}}else if(h.type==="webauthn"){const o=await b({providerAccountId:h.providerAccountId,provider:h.provider});if(o){if(A){if(o.id===A.id){const o={...h,userId:A.id};return{session:R,user:A,isNewUser:P,account:o}}throw new et("The account is already associated with another user",{provider:h.provider})}R=k?{}:await v({sessionToken:f(),userId:o.id,expires:o0(s.session.maxAge)});const i={...h,userId:o.id};return{session:R,user:o,isNewUser:P,account:i}}else{if(A){await _({...h,userId:A.id});await l.linkAccount?.({user:A,account:h,profile:p});const o={...h,userId:A.id};return{session:R,user:A,isNewUser:P,account:o}}const o=p.email?await w(p.email):null;if(o){throw new et("Another account already exists with the same e-mail address",{provider:h.provider})}else{A=await y({...p})}await l.createUser?.({user:A});await _({...h,userId:A.id});await l.linkAccount?.({user:A,account:h,profile:p});R=k?{}:await v({sessionToken:f(),userId:A.id,expires:o0(s.session.maxAge)});const i={...h,userId:A.id};return{session:R,user:A,isNewUser:true,account:i}}}const O=await b({providerAccountId:h.providerAccountId,provider:h.provider});if(O){if(A){if(O.id===A.id){return{session:R,user:A,isNewUser:P}}throw new M("The account is already associated with another user",{provider:h.provider})}R=k?{}:await v({sessionToken:f(),userId:O.id,expires:o0(s.session.maxAge)});return{session:R,user:O,isNewUser:P}}else{const{provider:o}=s;const{type:i,provider:a,providerAccountId:c,userId:u,...d}=h;const g={providerAccountId:c,provider:a,type:i,userId:u};h=Object.assign(o.account(d)??{},g);if(A){await _({...h,userId:A.id});await l.linkAccount?.({user:A,account:h,profile:p});return{session:R,user:A,isNewUser:P}}const m=p.email?await w(p.email):null;if(m){const o=s.provider;if(o?.allowDangerousEmailAccountLinking){A=m;P=false}else{throw new M("Another account already exists with the same e-mail address",{provider:h.provider})}}else{A=await y({...p,emailVerified:null});P=true}await l.createUser?.({user:A});await _({...h,userId:A.id});await l.linkAccount?.({user:A,account:h,profile:p});R=k?{}:await v({sessionToken:f(),userId:A.id,expires:o0(s.session.maxAge)});return{session:R,user:A,isNewUser:P}}};let o2;if(typeof navigator==="undefined"||!navigator.userAgent?.startsWith?.("Mozilla/5.0 ")){const o="oauth4webapi";const i="v3.6.0";o2=`${o}/${i}`}function o3(o,i){if(o==null){return false}try{return o instanceof i||Object.getPrototypeOf(o)[Symbol.toStringTag]===i.prototype[Symbol.toStringTag]}catch{return false}}const o6="ERR_INVALID_ARG_VALUE";const o8="ERR_INVALID_ARG_TYPE";function o5(o,i,a){const s=new TypeError(o,{cause:a});Object.assign(s,{code:i});return s}const o4=Symbol();const o9=Symbol();const o7=Symbol();const ie=Symbol();const it=Symbol();const ir=Symbol();const io=Symbol();const ii=new TextEncoder;const ia=new TextDecoder;function is(o){if(typeof o==="string"){return ii.encode(o)}return ia.decode(o)}let ic;if(Uint8Array.prototype.toBase64){ic=o=>{if(o instanceof ArrayBuffer){o=new Uint8Array(o)}return o.toBase64({alphabet:"base64url",omitPadding:true})}}else{const o=32768;ic=i=>{if(i instanceof ArrayBuffer){i=new Uint8Array(i)}const a=[];for(let s=0;s<i.byteLength;s+=o){a.push(String.fromCharCode.apply(null,i.subarray(s,s+o)))}return btoa(a.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}}let iu;if(Uint8Array.fromBase64){iu=o=>{try{return Uint8Array.fromBase64(o,{alphabet:"base64url"})}catch(o){throw o5("The input to be decoded is not correctly encoded.",o6,o)}}}else{iu=o=>{try{const i=atob(o.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,""));const a=new Uint8Array(i.length);for(let o=0;o<i.length;o++){a[o]=i.charCodeAt(o)}return a}catch(o){throw o5("The input to be decoded is not correctly encoded.",o6,o)}}}function il(o){if(typeof o==="string"){return iu(o)}return ic(o)}class id extends Error{code;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=a4;Error.captureStackTrace?.(this,this.constructor)}}class ip extends Error{code;constructor(o,i){super(o,i);this.name=this.constructor.name;if(i?.code){this.code=i?.code}Error.captureStackTrace?.(this,this.constructor)}}function ih(o,i,a){return new ip(o,{code:i,cause:a})}function iy(o,i){if(!(o instanceof CryptoKey)){throw o5(`${i} must be a CryptoKey`,o8)}}function ig(o,i){iy(o,i);if(o.type!=="private"){throw o5(`${i} must be a private CryptoKey`,o6)}}function im(o,i){iy(o,i);if(o.type!=="public"){throw o5(`${i} must be a public CryptoKey`,o6)}}function ib(o){return o.toLowerCase().replace(/^application\//,"")}function iw(o){if(o===null||typeof o!=="object"||Array.isArray(o)){return false}return true}function i_(o){if(o3(o,Headers)){o=Object.fromEntries(o.entries())}const i=new Headers(o??{});if(o2&&!i.has("user-agent")){i.set("user-agent",o2)}if(i.has("authorization")){throw o5('"options.headers" must not include the "authorization" header name',o6)}return i}function iv(o,i){if(i!==undefined){if(typeof i==="function"){i=i(o.href)}if(!(i instanceof AbortSignal)){throw o5('"options.signal" must return or be an instance of AbortSignal',o8)}return i}return undefined}function iE(o){if(o.includes("//")){return o.replace("//","/")}return o}function iS(o,i,a=false){if(o.pathname==="/"){o.pathname=i}else{o.pathname=iE(`${i}/${a?o.pathname:o.pathname.replace(/(\/)$/,"")}`)}return o}function iR(o,i){o.pathname=iE(`${o.pathname}/${i}`);return o}async function iA(o,i,a,s){if(!(o instanceof URL)){throw o5(`"${i}" must be an instance of URL`,o8)}i7(o,s?.[o4]!==true);const c=a(new URL(o.href));const u=i_(s?.headers);u.set("accept","application/json");return(s?.[ie]||fetch)(c.href,{body:undefined,headers:Object.fromEntries(u.entries()),method:"GET",redirect:"manual",signal:iv(c,s?.signal)})}async function iP(o,i){return iA(o,"issuerIdentifier",o=>{switch(i?.algorithm){case undefined:case"oidc":iR(o,".well-known/openid-configuration");break;case"oauth2":iS(o,".well-known/oauth-authorization-server");break;default:throw o5('"options.algorithm" must be "oidc" (default), or "oauth2"',o6)}return o},i)}function ik(o,i,a,s,c){try{if(typeof o!=="number"||!Number.isFinite(o)){throw o5(`${a} must be a number`,o8,c)}if(o>0)return;if(i){if(o!==0){throw o5(`${a} must be a non-negative number`,o6,c)}return}throw o5(`${a} must be a positive number`,o6,c)}catch(o){if(s){throw ih(o.message,s,c)}throw o}}function iO(o,i,a,s){try{if(typeof o!=="string"){throw o5(`${i} must be a string`,o8,s)}if(o.length===0){throw o5(`${i} must not be empty`,o6,s)}}catch(o){if(a){throw ih(o.message,a,s)}throw o}}async function iT(o,i){const a=o;if(!(a instanceof URL)&&a!==cn){throw o5('"expectedIssuerIdentifier" must be an instance of URL',o8)}if(!o3(i,Response)){throw o5('"response" must be an instance of Response',o8)}if(i.status!==200){throw ih('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',so,i)}s_(i);const s=await ce(i);iO(s.issuer,'"response" body "issuer" property',st,{body:s});if(a!==cn&&new URL(s.issuer).href!==a.href){throw ih('"response" body "issuer" property does not match the expected value',su,{expected:a.href,body:s,attribute:"issuer"})}return s}function ix(o){iN(o,"application/json")}function iC(o,...i){let a='"response" content-type must be ';if(i.length>2){const o=i.pop();a+=`${i.join(", ")}, or ${o}`}else if(i.length===2){a+=`${i[0]} or ${i[1]}`}else{a+=i[0]}return ih(a,sr,o)}function ij(o,...i){if(!i.includes(aj(o))){throw iC(o,...i)}}function iN(o,i){if(aj(o)!==i){throw iC(o,i)}}function iI(){return il(crypto.getRandomValues(new Uint8Array(32)))}function iD(){return iI()}function iU(){return iI()}function iM(){return iI()}async function iL(o){iO(o,"codeVerifier");return il(await crypto.subtle.digest("SHA-256",is(o)))}function i$(o){if(o instanceof CryptoKey){return{key:o}}if(!(o?.key instanceof CryptoKey)){return{}}if(o.kid!==undefined){iO(o.kid,'"kid"')}return{key:o.key,kid:o.kid}}function iH(o){switch(o.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new id("unsupported RsaHashedKeyAlgorithm hash name",{cause:o})}}function iW(o){switch(o.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new id("unsupported RsaHashedKeyAlgorithm hash name",{cause:o})}}function iK(o){switch(o.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new id("unsupported EcKeyAlgorithm namedCurve",{cause:o})}}function iB(o){switch(o.algorithm.name){case"RSA-PSS":return iH(o);case"RSASSA-PKCS1-v1_5":return iW(o);case"ECDSA":return iK(o);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new id("unsupported CryptoKey algorithm name",{cause:o})}}function iq(o){const i=o?.[o9];return typeof i==="number"&&Number.isFinite(i)?i:0}function iG(o){const i=o?.[o7];return typeof i==="number"&&Number.isFinite(i)&&Math.sign(i)!==-1?i:30}function iF(){return Math.floor(Date.now()/1e3)}function iJ(o){if(typeof o!=="object"||o===null){throw o5('"as" must be an object',o8)}iO(o.issuer,'"as.issuer"')}function iV(o){if(typeof o!=="object"||o===null){throw o5('"client" must be an object',o8)}iO(o.client_id,'"client.client_id"')}function iz(o){return encodeURIComponent(o).replace(/(?:[-_.!~*'()]|%20)/g,o=>{switch(o){case"-":case"_":case".":case"!":case"~":case"*":case"'":case"(":case")":return`%${o.charCodeAt(0).toString(16).toUpperCase()}`;case"%20":return"+";default:throw new Error}})}function iX(o){iO(o,'"clientSecret"');return(i,a,s,c)=>{s.set("client_id",a.client_id);s.set("client_secret",o)}}function iY(o){iO(o,'"clientSecret"');return(i,a,s,c)=>{const u=iz(a.client_id);const l=iz(o);const d=btoa(`${u}:${l}`);c.set("authorization",`Basic ${d}`)}}function iZ(o,i){const a=iF()+iq(i);return{jti:iI(),aud:o.issuer,exp:a+60,iat:a,nbf:a,iss:i.client_id,sub:i.client_id}}function iQ(o,i){const{key:a,kid:s}=i$(o);ig(a,'"clientPrivateKey.key"');return async(o,c,u,l)=>{const d={alg:iB(a),kid:s};const f=iZ(o,c);i?.[it]?.(d,f);u.set("client_id",c.client_id);u.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer");u.set("client_assertion",await i3(d,f,a))}}function i0(o,i){iO(o,'"clientSecret"');const a=i?.[it];let s;return async(i,c,u,l)=>{s||=await crypto.subtle.importKey("raw",is(o),{hash:"SHA-256",name:"HMAC"},false,["sign"]);const d={alg:"HS256"};const f=iZ(i,c);a?.(d,f);const p=`${il(is(JSON.stringify(d)))}.${il(is(JSON.stringify(f)))}`;const h=await crypto.subtle.sign(s.algorithm,s,is(p));u.set("client_id",c.client_id);u.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer");u.set("client_assertion",`${p}.${il(new Uint8Array(h))}`)}}function i1(){return(o,i,a,s)=>{a.set("client_id",i.client_id)}}function i2(){return i1()}async function i3(o,i,a){if(!a.usages.includes("sign")){throw o5('CryptoKey instances used for signing assertions must include "sign" in their "usages"',o6)}const s=`${il(is(JSON.stringify(o)))}.${il(is(JSON.stringify(i)))}`;const c=il(await crypto.subtle.sign(sT(a),a,is(s)));return`${s}.${c}`}async function i6(o,i,a,s,c){iJ(o);iV(i);a=new URLSearchParams(a);const{key:u,kid:l}=i$(s);ig(u,'"privateKey.key"');a.set("client_id",i.client_id);const d=iF()+iq(i);const f={...Object.fromEntries(a.entries()),jti:iI(),aud:o.issuer,exp:d+60,iat:d,nbf:d,iss:i.client_id};let p;if(a.has("resource")&&(p=a.getAll("resource"))&&p.length>1){f.resource=p}{let o=a.get("max_age");if(o!==null){f.max_age=parseInt(o,10);ik(f.max_age,true,'"max_age" parameter')}}{let o=a.get("claims");if(o!==null){try{f.claims=JSON.parse(o)}catch(o){throw ih('failed to parse the "claims" parameter as JSON',se,o)}if(!iw(f.claims)){throw o5('"claims" parameter must be a JSON with a top level object',o6)}}}{let o=a.get("authorization_details");if(o!==null){try{f.authorization_details=JSON.parse(o)}catch(o){throw ih('failed to parse the "authorization_details" parameter as JSON',se,o)}if(!Array.isArray(f.authorization_details)){throw o5('"authorization_details" parameter must be a JSON with a top level array',o6)}}}const h={alg:iB(u),typ:"oauth-authz-req+jwt",kid:l};c?.[it]?.(h,f);return i3(h,f,u)}let i8;async function i5(o){const{kty:i,e:a,n:s,x:c,y:u,crv:l}=await crypto.subtle.exportKey("jwk",o);const d={kty:i,e:a,n:s,x:c,y:u,crv:l};i8.set(o,d);return d}async function i4(o){i8||=new WeakMap;return i8.get(o)||i5(o)}const i9=URL.parse?(o,i)=>URL.parse(o,i):(o,i)=>{try{return new URL(o,i)}catch{return null}};function i7(o,i){if(i&&o.protocol!=="https:"){throw ih("only requests to HTTPS are allowed",si,o)}if(o.protocol!=="https:"&&o.protocol!=="http:"){throw ih("only HTTP and HTTPS requests are allowed",sa,o)}}function ae(o,i,a,s){let c;if(typeof o!=="string"||!(c=i9(o))){throw ih(`authorization server metadata does not contain a valid ${a?`"as.mtls_endpoint_aliases.${i}"`:`"as.${i}"`}`,o===undefined?sd:sf,{attribute:a?`mtls_endpoint_aliases.${i}`:i})}i7(c,s);return c}function at(o,i,a,s){if(a&&o.mtls_endpoint_aliases&&i in o.mtls_endpoint_aliases){return ae(o.mtls_endpoint_aliases[i],i,a,s)}return ae(o[i],i,a,s)}async function an(o,i,a,s,c){iJ(o);iV(i);const u=at(o,"pushed_authorization_request_endpoint",i.use_mtls_endpoint_aliases,c?.[o4]!==true);const l=new URLSearchParams(s);l.set("client_id",i.client_id);const d=i_(c?.headers);d.set("accept","application/json");if(c?.DPoP!==undefined){aE(c.DPoP);await c.DPoP.addProof(u,d,"POST")}const f=await aI(o,i,a,u,l,d,c);c?.DPoP?.cacheNonce(f);return f}class ar{#h;#y;#g;#m;#b;#w;#_;constructor(o,i,a){ig(i?.privateKey,'"DPoP.privateKey"');im(i?.publicKey,'"DPoP.publicKey"');if(!i.publicKey.extractable){throw o5('"DPoP.publicKey.extractable" must be true',o6)}this.#b=a?.[it];this.#m=iq(o);this.#y=i.privateKey;this.#g=i.publicKey;aV.add(this)}#v(o){this.#w||=new Map;let i=this.#w.get(o);if(i){this.#w.delete(o);this.#w.set(o,i)}return i}#E(o,i){this.#w||=new Map;this.#w.delete(o);if(this.#w.size===100){this.#w.delete(this.#w.keys().next().value)}this.#w.set(o,i)}async calculateThumbprint(){if(!this.#_){const o=await crypto.subtle.exportKey("jwk",this.#g);let i;switch(o.kty){case"EC":i={crv:o.crv,kty:o.kty,x:o.x,y:o.y};break;case"OKP":i={crv:o.crv,kty:o.kty,x:o.x};break;case"RSA":i={e:o.e,kty:o.kty,n:o.n};break;default:throw new id("unsupported JWK",{cause:{jwk:o}})}this.#_||=il(await crypto.subtle.digest({name:"SHA-256"},is(JSON.stringify(i))))}return this.#_}async addProof(o,i,a,s){this.#h||={alg:iB(this.#y),typ:"dpop+jwt",jwk:await i4(this.#g)};const c=this.#v(o.origin);const u=iF()+this.#m;const l={iat:u,jti:iI(),htm:a,nonce:c,htu:`${o.origin}${o.pathname}`,ath:s?il(await crypto.subtle.digest("SHA-256",is(s))):undefined};this.#b?.(this.#h,l);i.set("dpop",await i3(this.#h,l,this.#y))}cacheNonce(o){try{const i=o.headers.get("dpop-nonce");if(i){this.#E(new URL(o.url).origin,i)}}catch{}}}function ao(o){if(o instanceof ac){const{0:i,length:a}=o.cause;return a===1&&i.scheme==="dpop"&&i.parameters.error==="use_dpop_nonce"}if(o instanceof aa){return o.error==="use_dpop_nonce"}return false}function ai(o,i,a){return new ar(o,i,a)}class aa extends Error{cause;code;error;status;error_description;response;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=a5;this.cause=i.cause;this.error=i.cause.error;this.status=i.response.status;this.error_description=i.cause.error_description;Object.defineProperty(this,"response",{enumerable:false,value:i.response});Error.captureStackTrace?.(this,this.constructor)}}class as extends Error{cause;code;error;error_description;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=a9;this.cause=i.cause;this.error=i.cause.get("error");this.error_description=i.cause.get("error_description")??undefined;Error.captureStackTrace?.(this,this.constructor)}}class ac extends Error{cause;code;response;status;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=a8;this.cause=i.cause;this.status=i.response.status;this.response=i.response;Object.defineProperty(this,"response",{enumerable:false});Error.captureStackTrace?.(this,this.constructor)}}const au="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+";const al="[a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2}";const ad='"((?:[^"\\\\]|\\\\.)*)"';const af="("+au+")\\s*=\\s*"+ad;const ap="("+au+")\\s*=\\s*("+au+")";const ah=new RegExp("^[,\\s]*("+au+")\\s(.*)");const ay=new RegExp("^[,\\s]*"+af+"[,\\s]*(.*)");const ag=new RegExp("^[,\\s]*"+ap+"[,\\s]*(.*)");const am=new RegExp("^("+al+")(?:$|[,\\s])(.*)");function ab(o){if(!o3(o,Response)){throw o5('"response" must be an instance of Response',o8)}const i=o.headers.get("www-authenticate");if(i===null){return undefined}const a=[];let s=i;while(s){let o=s.match(ah);const i=o?.["1"].toLowerCase();s=o?.["2"];if(!i){return undefined}const c={};let u;while(s){let i;let a;if(o=s.match(ay)){;[,i,a,s]=o;if(a.includes("\\")){try{a=JSON.parse(`"${a}"`)}catch{}}c[i.toLowerCase()]=a;continue}if(o=s.match(ag)){;[,i,a,s]=o;c[i.toLowerCase()]=a;continue}if(o=s.match(am)){if(Object.keys(c).length){break};[,u,s]=o;break}return undefined}const l={scheme:i,parameters:c};if(u){l.token68=u}a.push(l)}if(!a.length){return undefined}return a}async function aw(o,i,a){iJ(o);iV(i);if(!o3(a,Response)){throw o5('"response" must be an instance of Response',o8)}aK(a);await av(a,201,"Pushed Authorization Request Endpoint");s_(a);const s=await ce(a);iO(s.request_uri,'"response" body "request_uri" property',st,{body:s});let c=typeof s.expires_in!=="number"?parseFloat(s.expires_in):s.expires_in;ik(c,true,'"response" body "expires_in" property',st,{body:s});s.expires_in=c;return s}async function a_(o){if(o.status>399&&o.status<500){s_(o);ix(o);try{const i=await o.clone().json();if(iw(i)&&typeof i.error==="string"&&i.error.length){return i}}catch{}}return undefined}async function av(o,i,a){if(o.status!==i){let i;if(i=await a_(o)){await o.body?.cancel();throw new aa("server responded with an error in the response body",{cause:i,response:o})}throw ih(`"response" is not a conform ${a} response (unexpected HTTP status code)`,so,o)}}function aE(o){if(!aV.has(o)){throw o5('"options.DPoP" is not a valid DPoPHandle',o6)}}async function aS(o,i,a,s,c,u){iO(o,'"accessToken"');if(!(a instanceof URL)){throw o5('"url" must be an instance of URL',o8)}i7(a,u?.[o4]!==true);s=i_(s);if(u?.DPoP){aE(u.DPoP);await u.DPoP.addProof(a,s,i.toUpperCase(),o)}s.set("authorization",`${s.has("dpop")?"DPoP":"Bearer"} ${o}`);const l=await (u?.[ie]||fetch)(a.href,{body:c,headers:Object.fromEntries(s.entries()),method:i,redirect:"manual",signal:iv(a,u?.signal)});u?.DPoP?.cacheNonce(l);return l}async function aR(o,i,a,s,c,u){const l=await aS(o,i,a,s,c,u);aK(l);return l}async function aA(o,i,a,s){iJ(o);iV(i);const c=at(o,"userinfo_endpoint",i.use_mtls_endpoint_aliases,s?.[o4]!==true);const u=i_(s?.headers);if(i.userinfo_signed_response_alg){u.set("accept","application/jwt")}else{u.set("accept","application/json");u.append("accept","application/jwt")}return aS(a,"GET",c,u,null,{...s,[o9]:iq(i)})}let aP;function ak(o,i,a,s){aP||=new WeakMap;aP.set(o,{jwks:i,uat:a,get age(){return iF()-this.uat}});if(s){Object.assign(s,{jwks:structuredClone(i),uat:a})}}function aO(o){if(typeof o!=="object"||o===null){return false}if(!("uat"in o)||typeof o.uat!=="number"||iF()-o.uat>=300){return false}if(!("jwks"in o)||!iw(o.jwks)||!Array.isArray(o.jwks.keys)||!Array.prototype.every.call(o.jwks.keys,iw)){return false}return true}function aT(o,i){aP?.delete(o);delete i?.jwks;delete i?.uat}async function ax(o,i,a){const{alg:s,kid:c}=a;sP(a);if(!aP?.has(o)&&aO(i?.[io])){ak(o,i?.[io].jwks,i?.[io].uat)}let u;let l;if(aP?.has(o)){;({jwks:u,age:l}=aP.get(o));if(l>=300){aT(o,i?.[io]);return ax(o,i,a)}}else{u=await sS(o,i).then(sR);l=0;ak(o,u,iF(),i?.[io])}let d;switch(s.slice(0,2)){case"RS":case"PS":d="RSA";break;case"ES":d="EC";break;case"Ed":d="OKP";break;default:throw new id("unsupported JWS algorithm",{cause:{alg:s}})}const f=u.keys.filter(o=>{if(o.kty!==d){return false}if(c!==undefined&&c!==o.kid){return false}if(o.alg!==undefined&&s!==o.alg){return false}if(o.use!==undefined&&o.use!=="sig"){return false}if(o.key_ops?.includes("verify")===false){return false}switch(true){case s==="ES256"&&o.crv!=="P-256":case s==="ES384"&&o.crv!=="P-384":case s==="ES512"&&o.crv!=="P-521":case s==="Ed25519"&&o.crv!=="Ed25519":case s==="EdDSA"&&o.crv!=="Ed25519":return false}return true});const{0:p,length:h}=f;if(!h){if(l>=60){aT(o,i?.[io]);return ax(o,i,a)}throw ih("error when selecting a JWT verification key, no applicable keys found",sl,{header:a,candidates:f,jwks_uri:new URL(o.jwks_uri)})}if(h!==1){throw ih('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',sl,{header:a,candidates:f,jwks_uri:new URL(o.jwks_uri)})}return sF(s,p)}const aC=Symbol();function aj(o){return o.headers.get("content-type")?.split(";")[0]}async function aN(o,i,a,s,c){iJ(o);iV(i);if(!o3(s,Response)){throw o5('"response" must be an instance of Response',o8)}aK(s);if(s.status!==200){throw ih('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',so,s)}s_(s);let u;if(aj(s)==="application/jwt"){const{claims:a,jwt:l}=await sC(await s.text(),sH.bind(undefined,i.userinfo_signed_response_alg,o.userinfo_signing_alg_values_supported,undefined),iq(i),iG(i),c?.[ir]).then(aq.bind(undefined,i.client_id)).then(aF.bind(undefined,o));aL.set(s,l);u=a}else{if(i.userinfo_signed_response_alg){throw ih("JWT UserInfo Response expected",a7,s)}u=await ce(s)}iO(u.sub,'"response" body "sub" property',st,{body:u});switch(a){case aC:break;default:iO(a,'"expectedSubject"');if(u.sub!==a){throw ih('unexpected "response" body "sub" property value',su,{expected:a,body:u,attribute:"sub"})}}return u}async function aI(o,i,a,s,c,u,l){await a(o,i,c,u);u.set("content-type","application/x-www-form-urlencoded;charset=UTF-8");return(l?.[ie]||fetch)(s.href,{body:c,headers:Object.fromEntries(u.entries()),method:"POST",redirect:"manual",signal:iv(s,l?.signal)})}async function aD(o,i,a,s,c,u){const l=at(o,"token_endpoint",i.use_mtls_endpoint_aliases,u?.[o4]!==true);c.set("grant_type",s);const d=i_(u?.headers);d.set("accept","application/json");if(u?.DPoP!==undefined){aE(u.DPoP);await u.DPoP.addProof(l,d,"POST")}const f=await aI(o,i,a,l,c,d,u);u?.DPoP?.cacheNonce(f);return f}async function aU(o,i,a,s,c){iJ(o);iV(i);iO(s,'"refreshToken"');const u=new URLSearchParams(c?.additionalParameters);u.set("refresh_token",s);return aD(o,i,a,"refresh_token",u,c)}const aM=new WeakMap;const aL=new WeakMap;function a$(o){if(!o.id_token){return undefined}const i=aM.get(o);if(!i){throw o5('"ref" was already garbage collected or did not resolve from the proper sources',o6)}return i}async function aH(o,i,a){iJ(o);if(!aL.has(i)){throw o5('"ref" does not contain a processed JWT Response to verify the signature of',o6)}const{0:s,1:c,2:u}=aL.get(i).split(".");const l=JSON.parse(is(il(s)));if(l.alg.startsWith("HS")){throw new id("unsupported JWS algorithm",{cause:{alg:l.alg}})}let d;d=await ax(o,a,l);await sx(s,c,d,il(u))}async function aW(o,i,a,s,c){iJ(o);iV(i);if(!o3(a,Response)){throw o5('"response" must be an instance of Response',o8)}aK(a);await av(a,200,"Token Endpoint");s_(a);const u=await ce(a);iO(u.access_token,'"response" body "access_token" property',st,{body:u});iO(u.token_type,'"response" body "token_type" property',st,{body:u});u.token_type=u.token_type.toLowerCase();if(u.token_type!=="dpop"&&u.token_type!=="bearer"){throw new id("unsupported `token_type` value",{cause:{body:u}})}if(u.expires_in!==undefined){let o=typeof u.expires_in!=="number"?parseFloat(u.expires_in):u.expires_in;ik(o,true,'"response" body "expires_in" property',st,{body:u});u.expires_in=o}if(u.refresh_token!==undefined){iO(u.refresh_token,'"response" body "refresh_token" property',st,{body:u})}if(u.scope!==undefined&&typeof u.scope!=="string"){throw ih('"response" body "scope" property must be a string',st,{body:u})}if(u.id_token!==undefined){iO(u.id_token,'"response" body "id_token" property',st,{body:u});const l=["aud","exp","iat","iss","sub"];if(i.require_auth_time===true){l.push("auth_time")}if(i.default_max_age!==undefined){ik(i.default_max_age,true,'"client.default_max_age"');l.push("auth_time")}if(s?.length){l.push(...s)}const{claims:d,jwt:f}=await sC(u.id_token,sH.bind(undefined,i.id_token_signed_response_alg,o.id_token_signing_alg_values_supported,"RS256"),iq(i),iG(i),c?.[ir]).then(aQ.bind(undefined,l)).then(aJ.bind(undefined,o)).then(aG.bind(undefined,i.client_id));if(Array.isArray(d.aud)&&d.aud.length!==1){if(d.azp===undefined){throw ih('ID Token "aud" (audience) claim includes additional untrusted audiences',sc,{claims:d,claim:"aud"})}if(d.azp!==i.client_id){throw ih('unexpected ID Token "azp" (authorized party) claim value',sc,{expected:i.client_id,claims:d,claim:"azp"})}}if(d.auth_time!==undefined){ik(d.auth_time,false,'ID Token "auth_time" (authentication time)',st,{claims:d})}aL.set(a,f);aM.set(u,d)}return u}function aK(o){let i;if(i=ab(o)){throw new ac("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:i,response:o})}}async function aB(o,i,a,s){return aW(o,i,a,undefined,s)}function aq(o,i){if(i.claims.aud!==undefined){return aG(o,i)}return i}function aG(o,i){if(Array.isArray(i.claims.aud)){if(!i.claims.aud.includes(o)){throw ih('unexpected JWT "aud" (audience) claim value',sc,{expected:o,claims:i.claims,claim:"aud"})}}else if(i.claims.aud!==o){throw ih('unexpected JWT "aud" (audience) claim value',sc,{expected:o,claims:i.claims,claim:"aud"})}return i}function aF(o,i){if(i.claims.iss!==undefined){return aJ(o,i)}return i}function aJ(o,i){const a=o[cr]?.(i)??o.issuer;if(i.claims.iss!==a){throw ih('unexpected JWT "iss" (issuer) claim value',sc,{expected:a,claims:i.claims,claim:"iss"})}return i}const aV=new WeakSet;function az(o){aV.add(o);return o}const aX=Symbol();async function aY(o,i,a,s,c,u,l){iJ(o);iV(i);if(!aV.has(s)){throw o5('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',o6)}iO(c,'"redirectUri"');const d=sW(s,"code");if(!d){throw ih('no authorization code in "callbackParameters"',st)}const f=new URLSearchParams(l?.additionalParameters);f.set("redirect_uri",c);f.set("code",d);if(u!==aX){iO(u,'"codeVerifier"');f.set("code_verifier",u)}return aD(o,i,a,"authorization_code",f,l)}const aZ={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function aQ(o,i){for(const a of o){if(i.claims[a]===undefined){throw ih(`JWT "${a}" (${aZ[a]}) claim missing`,st,{claims:i.claims})}}return i}const a0=Symbol();const a1=Symbol();async function a2(o,i,a,s){if(typeof s?.expectedNonce==="string"||typeof s?.maxAge==="number"||s?.requireIdToken){return a3(o,i,a,s.expectedNonce,s.maxAge,{[ir]:s[ir]})}return a6(o,i,a,s)}async function a3(o,i,a,s,c,u){const l=[];switch(s){case undefined:s=a0;break;case a0:break;default:iO(s,'"expectedNonce" argument');l.push("nonce")}c??=i.default_max_age;switch(c){case undefined:c=a1;break;case a1:break;default:ik(c,true,'"maxAge" argument');l.push("auth_time")}const d=await aW(o,i,a,l,u);iO(d.id_token,'"response" body "id_token" property',st,{body:d});const f=a$(d);if(c!==a1){const o=iF()+iq(i);const a=iG(i);if(f.auth_time+c<o-a){throw ih("too much time has elapsed since the last End-User authentication",ss,{claims:f,now:o,tolerance:a,claim:"auth_time"})}}if(s===a0){if(f.nonce!==undefined){throw ih('unexpected ID Token "nonce" claim value',sc,{expected:undefined,claims:f,claim:"nonce"})}}else if(f.nonce!==s){throw ih('unexpected ID Token "nonce" claim value',sc,{expected:s,claims:f,claim:"nonce"})}return d}async function a6(o,i,a,s){const c=await aW(o,i,a,undefined,s);const u=a$(c);if(u){if(i.default_max_age!==undefined){ik(i.default_max_age,true,'"client.default_max_age"');const o=iF()+iq(i);const a=iG(i);if(u.auth_time+i.default_max_age<o-a){throw ih("too much time has elapsed since the last End-User authentication",ss,{claims:u,now:o,tolerance:a,claim:"auth_time"})}}if(u.nonce!==undefined){throw ih('unexpected ID Token "nonce" claim value',sc,{expected:undefined,claims:u,claim:"nonce"})}}return c}const a8="OAUTH_WWW_AUTHENTICATE_CHALLENGE";const a5="OAUTH_RESPONSE_BODY_ERROR";const a4="OAUTH_UNSUPPORTED_OPERATION";const a9="OAUTH_AUTHORIZATION_RESPONSE_ERROR";const a7="OAUTH_JWT_USERINFO_EXPECTED";const se="OAUTH_PARSE_ERROR";const st="OAUTH_INVALID_RESPONSE";const sn="OAUTH_INVALID_REQUEST";const sr="OAUTH_RESPONSE_IS_NOT_JSON";const so="OAUTH_RESPONSE_IS_NOT_CONFORM";const si="OAUTH_HTTP_REQUEST_FORBIDDEN";const sa="OAUTH_REQUEST_PROTOCOL_FORBIDDEN";const ss="OAUTH_JWT_TIMESTAMP_CHECK_FAILED";const sc="OAUTH_JWT_CLAIM_COMPARISON_FAILED";const su="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED";const sl="OAUTH_KEY_SELECTION_FAILED";const sd="OAUTH_MISSING_SERVER_METADATA";const sf="OAUTH_INVALID_SERVER_METADATA";function sp(o,i){if(typeof i.header.typ!=="string"||ib(i.header.typ)!==o){throw ih('unexpected JWT "typ" header parameter value',st,{header:i.header})}return i}async function sh(o,i,a,s,c){iJ(o);iV(i);return aD(o,i,a,"client_credentials",new URLSearchParams(s),c)}async function sy(o,i,a,s,c,u){iJ(o);iV(i);iO(s,'"grantType"');return aD(o,i,a,s,new URLSearchParams(c),u)}async function sg(o,i,a,s){return aW(o,i,a,undefined,s)}async function sm(o,i,a,s){return aW(o,i,a,undefined,s)}async function sb(o,i,a,s,c){iJ(o);iV(i);iO(s,'"token"');const u=at(o,"revocation_endpoint",i.use_mtls_endpoint_aliases,c?.[o4]!==true);const l=new URLSearchParams(c?.additionalParameters);l.set("token",s);const d=i_(c?.headers);d.delete("accept");return aI(o,i,a,u,l,d,c)}async function sw(o){if(!o3(o,Response)){throw o5('"response" must be an instance of Response',o8)}aK(o);await av(o,200,"Revocation Endpoint");return undefined}function s_(o){if(o.bodyUsed){throw o5('"response" body has been used already',o6)}}async function sv(o,i,a,s,c){iJ(o);iV(i);iO(s,'"token"');const u=at(o,"introspection_endpoint",i.use_mtls_endpoint_aliases,c?.[o4]!==true);const l=new URLSearchParams(c?.additionalParameters);l.set("token",s);const d=i_(c?.headers);if(c?.requestJwtResponse??i.introspection_signed_response_alg){d.set("accept","application/token-introspection+jwt")}else{d.set("accept","application/json")}return aI(o,i,a,u,l,d,c)}async function sE(o,i,a,s){iJ(o);iV(i);if(!o3(a,Response)){throw o5('"response" must be an instance of Response',o8)}aK(a);await av(a,200,"Introspection Endpoint");let c;if(aj(a)==="application/token-introspection+jwt"){s_(a);const{claims:u,jwt:l}=await sC(await a.text(),sH.bind(undefined,i.introspection_signed_response_alg,o.introspection_signing_alg_values_supported,"RS256"),iq(i),iG(i),s?.[ir]).then(sp.bind(undefined,"token-introspection+jwt")).then(aQ.bind(undefined,["aud","iat","iss"])).then(aJ.bind(undefined,o)).then(aG.bind(undefined,i.client_id));aL.set(a,l);if(!iw(u.token_introspection)){throw ih('JWT "token_introspection" claim must be a JSON object',st,{claims:u})}c=u.token_introspection}else{s_(a);c=await ce(a)}if(typeof c.active!=="boolean"){throw ih('"response" body "active" property must be a boolean',st,{body:c})}return c}async function sS(o,i){iJ(o);const a=at(o,"jwks_uri",false,i?.[o4]!==true);const s=i_(i?.headers);s.set("accept","application/json");s.append("accept","application/jwk-set+json");return(i?.[ie]||fetch)(a.href,{body:undefined,headers:Object.fromEntries(s.entries()),method:"GET",redirect:"manual",signal:iv(a,i?.signal)})}async function sR(o){if(!o3(o,Response)){throw o5('"response" must be an instance of Response',o8)}if(o.status!==200){throw ih('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',so,o)}s_(o);const i=await ce(o,o=>ij(o,"application/json","application/jwk-set+json"));if(!Array.isArray(i.keys)){throw ih('"response" body "keys" property must be an array',st,{body:i})}if(!Array.prototype.every.call(i.keys,iw)){throw ih('"response" body "keys" property members must be JWK formatted objects',st,{body:i})}return i}function sA(o){switch(o){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return true;default:return false}}function sP(o){if(!sA(o.alg)){throw new id('unsupported JWS "alg" identifier',{cause:{alg:o.alg}})}}function sk(o){const{algorithm:i}=o;if(typeof i.modulusLength!=="number"||i.modulusLength<2048){throw new id(`unsupported ${i.name} modulusLength`,{cause:o})}}function sO(o){const{algorithm:i}=o;switch(i.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new id("unsupported ECDSA namedCurve",{cause:o})}}function sT(o){switch(o.algorithm.name){case"ECDSA":return{name:o.algorithm.name,hash:sO(o)};case"RSA-PSS":{sk(o);switch(o.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:o.algorithm.name,saltLength:parseInt(o.algorithm.hash.name.slice(-3),10)>>3};default:throw new id("unsupported RSA-PSS hash name",{cause:o})}}case"RSASSA-PKCS1-v1_5":sk(o);return o.algorithm.name;case"Ed25519":return o.algorithm.name}throw new id("unsupported CryptoKey algorithm name",{cause:o})}async function sx(o,i,a,s){const c=is(`${o}.${i}`);const u=sT(a);const l=await crypto.subtle.verify(u,a,s,c);if(!l){throw ih("JWT signature verification failed",st,{key:a,data:c,signature:s,algorithm:u})}}async function sC(o,i,a,s,c){let{0:u,1:l,length:d}=o.split(".");if(d===5){if(c!==undefined){o=await c(o);({0:u,1:l,length:d}=o.split("."))}else{throw new id("JWE decryption is not configured",{cause:o})}}if(d!==3){throw ih("Invalid JWT",st,o)}let f;try{f=JSON.parse(is(il(u)))}catch(o){throw ih("failed to parse JWT Header body as base64url encoded JSON",se,o)}if(!iw(f)){throw ih("JWT Header must be a top level object",st,o)}i(f);if(f.crit!==undefined){throw new id('no JWT "crit" header parameter extensions are supported',{cause:{header:f}})}let p;try{p=JSON.parse(is(il(l)))}catch(o){throw ih("failed to parse JWT Payload body as base64url encoded JSON",se,o)}if(!iw(p)){throw ih("JWT Payload must be a top level object",st,o)}const h=iF()+a;if(p.exp!==undefined){if(typeof p.exp!=="number"){throw ih('unexpected JWT "exp" (expiration time) claim type',st,{claims:p})}if(p.exp<=h-s){throw ih('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',ss,{claims:p,now:h,tolerance:s,claim:"exp"})}}if(p.iat!==undefined){if(typeof p.iat!=="number"){throw ih('unexpected JWT "iat" (issued at) claim type',st,{claims:p})}}if(p.iss!==undefined){if(typeof p.iss!=="string"){throw ih('unexpected JWT "iss" (issuer) claim type',st,{claims:p})}}if(p.nbf!==undefined){if(typeof p.nbf!=="number"){throw ih('unexpected JWT "nbf" (not before) claim type',st,{claims:p})}if(p.nbf>h+s){throw ih('unexpected JWT "nbf" (not before) claim value',ss,{claims:p,now:h,tolerance:s,claim:"nbf"})}}if(p.aud!==undefined){if(typeof p.aud!=="string"&&!Array.isArray(p.aud)){throw ih('unexpected JWT "aud" (audience) claim type',st,{claims:p})}}return{header:f,claims:p,jwt:o}}async function sj(o,i,a,s,c){iJ(o);iV(i);if(a instanceof URL){a=a.searchParams}if(!(a instanceof URLSearchParams)){throw o5('"parameters" must be an instance of URLSearchParams, or URL',o8)}const u=sW(a,"response");if(!u){throw ih('"parameters" does not contain a JARM response',st)}const{claims:l,header:d,jwt:f}=await sC(u,sH.bind(undefined,i.authorization_signed_response_alg,o.authorization_signing_alg_values_supported,"RS256"),iq(i),iG(i),c?.[ir]).then(aQ.bind(undefined,["aud","exp","iss"])).then(aJ.bind(undefined,o)).then(aG.bind(undefined,i.client_id));const{0:p,1:h,2:y}=f.split(".");const g=il(y);const m=await ax(o,c,d);await sx(p,h,m,g);const b=new URLSearchParams;for(const[o,i]of Object.entries(l)){if(typeof i==="string"&&o!=="aud"){b.set(o,i)}}return sq(o,i,b,s)}async function sN(o,i,a){let s;switch(i.alg){case"RS256":case"PS256":case"ES256":s="SHA-256";break;case"RS384":case"PS384":case"ES384":s="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":s="SHA-512";break;default:throw new id(`unsupported JWS algorithm for ${a} calculation`,{cause:{alg:i.alg}})}const c=await crypto.subtle.digest(s,is(o));return il(c.slice(0,c.byteLength/2))}async function sI(o,i,a,s){const c=await sN(o,a,s);return i===c}async function sD(o,i,a,s,c,u,l){return s$(o,i,a,s,c,u,l,true)}async function sU(o,i,a,s,c,u,l){return s$(o,i,a,s,c,u,l,false)}async function sM(o){if(o.bodyUsed){throw o5("form_post Request instances must contain a readable body",o6,{cause:o})}return o.text()}async function sL(o){if(o.method!=="POST"){throw o5("form_post responses are expected to use the POST method",o6,{cause:o})}if(aj(o)!=="application/x-www-form-urlencoded"){throw o5("form_post responses are expected to use the application/x-www-form-urlencoded content-type",o6,{cause:o})}return sM(o)}async function s$(o,i,a,s,c,u,l,d){iJ(o);iV(i);if(a instanceof URL){if(!a.hash.length){throw o5('"parameters" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters',o6)}a=new URLSearchParams(a.hash.slice(1))}else if(o3(a,Request)){a=new URLSearchParams(await sL(a))}else if(a instanceof URLSearchParams){a=new URLSearchParams(a)}else{throw o5('"parameters" must be an instance of URLSearchParams, URL, or Response',o8)}const f=sW(a,"id_token");a.delete("id_token");switch(c){case undefined:case sB:break;default:iO(c,'"expectedState" argument')}const p=sq({...o,authorization_response_iss_parameter_supported:false},i,a,c);if(!f){throw ih('"parameters" does not contain an ID Token',st)}const h=sW(a,"code");if(!h){throw ih('"parameters" does not contain an Authorization Code',st)}const y=["aud","exp","iat","iss","sub","nonce","c_hash"];const g=a.get("state");if(d&&(typeof c==="string"||g!==null)){y.push("s_hash")}if(u!==undefined){ik(u,true,'"maxAge" argument')}else if(i.default_max_age!==undefined){ik(i.default_max_age,true,'"client.default_max_age"')}u??=i.default_max_age??a1;if(i.require_auth_time||u!==a1){y.push("auth_time")}const{claims:m,header:b,jwt:w}=await sC(f,sH.bind(undefined,i.id_token_signed_response_alg,o.id_token_signing_alg_values_supported,"RS256"),iq(i),iG(i),l?.[ir]).then(aQ.bind(undefined,y)).then(aJ.bind(undefined,o)).then(aG.bind(undefined,i.client_id));const _=iq(i);const v=iF()+_;if(m.iat<v-3600){throw ih('unexpected JWT "iat" (issued at) claim value, it is too far in the past',ss,{now:v,claims:m,claim:"iat"})}iO(m.c_hash,'ID Token "c_hash" (code hash) claim value',st,{claims:m});if(m.auth_time!==undefined){ik(m.auth_time,false,'ID Token "auth_time" (authentication time)',st,{claims:m})}if(u!==a1){const o=iF()+iq(i);const a=iG(i);if(m.auth_time+u<o-a){throw ih("too much time has elapsed since the last End-User authentication",ss,{claims:m,now:o,tolerance:a,claim:"auth_time"})}}iO(s,'"expectedNonce" argument');if(m.nonce!==s){throw ih('unexpected ID Token "nonce" claim value',sc,{expected:s,claims:m,claim:"nonce"})}if(Array.isArray(m.aud)&&m.aud.length!==1){if(m.azp===undefined){throw ih('ID Token "aud" (audience) claim includes additional untrusted audiences',sc,{claims:m,claim:"aud"})}if(m.azp!==i.client_id){throw ih('unexpected ID Token "azp" (authorized party) claim value',sc,{expected:i.client_id,claims:m,claim:"azp"})}}const{0:E,1:S,2:R}=w.split(".");const A=il(R);const P=await ax(o,l,b);await sx(E,S,P,A);if(await sI(h,m.c_hash,b,"c_hash")!==true){throw ih('invalid ID Token "c_hash" (code hash) claim value',sc,{code:h,alg:b.alg,claim:"c_hash",claims:m})}if(d&&g!==null||m.s_hash!==undefined){iO(m.s_hash,'ID Token "s_hash" (state hash) claim value',st,{claims:m});iO(g,'"state" response parameter',st,{parameters:a});if(await sI(g,m.s_hash,b,"s_hash")!==true){throw ih('invalid ID Token "s_hash" (state hash) claim value',sc,{state:g,alg:b.alg,claim:"s_hash",claims:m})}}return p}function sH(o,i,a,s){if(o!==undefined){if(typeof o==="string"?s.alg!==o:!o.includes(s.alg)){throw ih('unexpected JWT "alg" header parameter',st,{header:s,expected:o,reason:"client configuration"})}return}if(Array.isArray(i)){if(!i.includes(s.alg)){throw ih('unexpected JWT "alg" header parameter',st,{header:s,expected:i,reason:"authorization server metadata"})}return}if(a!==undefined){if(typeof a==="string"?s.alg!==a:typeof a==="function"?!a(s.alg):!a.includes(s.alg)){throw ih('unexpected JWT "alg" header parameter',st,{header:s,expected:a,reason:"default value"})}return}throw ih('missing client or server configuration to verify used JWT "alg" header parameter',undefined,{client:o,issuer:i,fallback:a})}function sW(o,i){const{0:a,length:s}=o.getAll(i);if(s>1){throw ih(`"${i}" parameter must be provided only once`,st)}return a}const sK=Symbol();const sB=Symbol();function sq(o,i,a,s){iJ(o);iV(i);if(a instanceof URL){a=a.searchParams}if(!(a instanceof URLSearchParams)){throw o5('"parameters" must be an instance of URLSearchParams, or URL',o8)}if(sW(a,"response")){throw ih('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',st,{parameters:a})}const c=sW(a,"iss");const u=sW(a,"state");if(!c&&o.authorization_response_iss_parameter_supported){throw ih('response parameter "iss" (issuer) missing',st,{parameters:a})}if(c&&c!==o.issuer){throw ih('unexpected "iss" (issuer) response parameter value',st,{expected:o.issuer,parameters:a})}switch(s){case undefined:case sB:if(u!==undefined){throw ih('unexpected "state" response parameter encountered',st,{expected:undefined,parameters:a})}break;case sK:break;default:iO(s,'"expectedState" argument');if(u!==s){throw ih(u===undefined?'response parameter "state" missing':'unexpected "state" response parameter value',st,{expected:s,parameters:a})}}const l=sW(a,"error");if(l){throw new as("authorization response from the server is an error",{cause:a})}const d=sW(a,"id_token");const f=sW(a,"token");if(d!==undefined||f!==undefined){throw new id("implicit and hybrid flows are not supported")}return az(new URLSearchParams(a))}function sG(o){switch(o){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${o.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${o.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${o.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new id("unsupported JWS algorithm",{cause:{alg:o}})}}async function sF(o,i){const{ext:a,key_ops:s,use:c,...u}=i;return crypto.subtle.importKey("jwk",u,sG(o),true,["verify"])}async function sJ(o,i,a,s,c){iJ(o);iV(i);const u=at(o,"device_authorization_endpoint",i.use_mtls_endpoint_aliases,c?.[o4]!==true);const l=new URLSearchParams(s);l.set("client_id",i.client_id);const d=i_(c?.headers);d.set("accept","application/json");return aI(o,i,a,u,l,d,c)}async function sV(o,i,a){iJ(o);iV(i);if(!o3(a,Response)){throw o5('"response" must be an instance of Response',o8)}aK(a);await av(a,200,"Device Authorization Endpoint");s_(a);const s=await ce(a);iO(s.device_code,'"response" body "device_code" property',st,{body:s});iO(s.user_code,'"response" body "user_code" property',st,{body:s});iO(s.verification_uri,'"response" body "verification_uri" property',st,{body:s});let c=typeof s.expires_in!=="number"?parseFloat(s.expires_in):s.expires_in;ik(c,true,'"response" body "expires_in" property',st,{body:s});s.expires_in=c;if(s.verification_uri_complete!==undefined){iO(s.verification_uri_complete,'"response" body "verification_uri_complete" property',st,{body:s})}if(s.interval!==undefined){ik(s.interval,false,'"response" body "interval" property',st,{body:s})}return s}async function sz(o,i,a,s,c){iJ(o);iV(i);iO(s,'"deviceCode"');const u=new URLSearchParams(c?.additionalParameters);u.set("device_code",s);return aD(o,i,a,"urn:ietf:params:oauth:grant-type:device_code",u,c)}async function sX(o,i,a,s){return aW(o,i,a,undefined,s)}async function sY(o,i){iO(o,'"alg"');const a=sG(o);if(o.startsWith("PS")||o.startsWith("RS")){Object.assign(a,{modulusLength:i?.modulusLength??2048,publicExponent:new Uint8Array([1,0,1])})}return crypto.subtle.generateKey(a,i?.extractable??false,["sign","verify"])}function sZ(o){const i=new URL(o);i.search="";i.hash="";return i.href}async function sQ(o,i,a,s){const c=o.headers.get("dpop");if(c===null){throw ih("operation indicated DPoP use but the request has no DPoP HTTP Header",sn,{headers:o.headers})}if(o.headers.get("authorization")?.toLowerCase().startsWith("dpop ")===false){throw ih(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`,sn,{headers:o.headers})}if(typeof a.cnf?.jkt!=="string"){throw ih("operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim",sn,{claims:a})}const u=iq(s);const l=await sC(c,sH.bind(undefined,s?.signingAlgorithms,undefined,sA),u,iG(s),undefined).then(sp.bind(undefined,"dpop+jwt")).then(aQ.bind(undefined,["iat","jti","ath","htm","htu"]));const d=iF()+u;const f=Math.abs(d-l.claims.iat);if(f>300){throw ih("DPoP Proof iat is not recent enough",ss,{now:d,claims:l.claims,claim:"iat"})}if(l.claims.htm!==o.method){throw ih("DPoP Proof htm mismatch",sc,{expected:o.method,claims:l.claims,claim:"htm"})}if(typeof l.claims.htu!=="string"||sZ(l.claims.htu)!==sZ(o.url)){throw ih("DPoP Proof htu mismatch",sc,{expected:sZ(o.url),claims:l.claims,claim:"htu"})}{const o=il(await crypto.subtle.digest("SHA-256",is(i)));if(l.claims.ath!==o){throw ih("DPoP Proof ath mismatch",sc,{expected:o,claims:l.claims,claim:"ath"})}}{let o;switch(l.header.jwk.kty){case"EC":o={crv:l.header.jwk.crv,kty:l.header.jwk.kty,x:l.header.jwk.x,y:l.header.jwk.y};break;case"OKP":o={crv:l.header.jwk.crv,kty:l.header.jwk.kty,x:l.header.jwk.x};break;case"RSA":o={e:l.header.jwk.e,kty:l.header.jwk.kty,n:l.header.jwk.n};break;default:throw new id("unsupported JWK key type",{cause:l.header.jwk})}const i=il(await crypto.subtle.digest("SHA-256",is(JSON.stringify(o))));if(a.cnf.jkt!==i){throw ih("JWT Access Token confirmation mismatch",sc,{expected:i,claims:a,claim:"cnf.jkt"})}}const{0:p,1:h,2:y}=c.split(".");const g=il(y);const{jwk:m,alg:b}=l.header;if(!m){throw ih("DPoP Proof is missing the jwk header parameter",sn,{header:l.header})}const w=await sF(b,m);if(w.type!=="public"){throw ih("DPoP Proof jwk header parameter must contain a public key",sn,{header:l.header})}await sx(p,h,w,g)}async function s0(o,i,a,s){iJ(o);if(!o3(i,Request)){throw o5('"request" must be an instance of Request',o8)}iO(a,'"expectedAudience"');const c=i.headers.get("authorization");if(c===null){throw ih('"request" is missing an Authorization HTTP Header',sn,{headers:i.headers})}let{0:u,1:l,length:d}=c.split(" ");u=u.toLowerCase();switch(u){case"dpop":case"bearer":break;default:throw new id("unsupported Authorization HTTP Header scheme",{cause:{headers:i.headers}})}if(d!==2){throw ih("invalid Authorization HTTP Header format",sn,{headers:i.headers})}const f=["iss","exp","aud","sub","iat","jti","client_id"];if(s?.requireDPoP||u==="dpop"||i.headers.has("dpop")){f.push("cnf")}const{claims:p,header:h}=await sC(l,sH.bind(undefined,s?.signingAlgorithms,undefined,sA),iq(s),iG(s),undefined).then(sp.bind(undefined,"at+jwt")).then(aQ.bind(undefined,f)).then(aJ.bind(undefined,o)).then(aG.bind(undefined,a)).catch(s1);for(const o of["client_id","jti","sub"]){if(typeof p[o]!=="string"){throw ih(`unexpected JWT "${o}" claim type`,sn,{claims:p})}}if("cnf"in p){if(!iw(p.cnf)){throw ih('unexpected JWT "cnf" (confirmation) claim value',sn,{claims:p})}const{0:o,length:i}=Object.keys(p.cnf);if(i){if(i!==1){throw new id("multiple confirmation claims are not supported",{cause:{claims:p}})}if(o!=="jkt"){throw new id("unsupported JWT Confirmation method",{cause:{claims:p}})}}}const{0:y,1:g,2:m}=l.split(".");const b=il(m);const w=await ax(o,s,h);await sx(y,g,w,b);if(s?.requireDPoP||u==="dpop"||p.cnf?.jkt!==undefined||i.headers.has("dpop")){await sQ(i,l,p,s).catch(s1)}return p}function s1(o){if(o instanceof ip&&o?.code===sn){o.code=st}throw o}async function s2(o,i,a,s,c){iJ(o);iV(i);const u=at(o,"backchannel_authentication_endpoint",i.use_mtls_endpoint_aliases,c?.[o4]!==true);const l=new URLSearchParams(s);l.set("client_id",i.client_id);const d=i_(c?.headers);d.set("accept","application/json");return aI(o,i,a,u,l,d,c)}async function s3(o,i,a){iJ(o);iV(i);if(!o3(a,Response)){throw o5('"response" must be an instance of Response',o8)}aK(a);await av(a,200,"Backchannel Authentication Endpoint");s_(a);const s=await ce(a);iO(s.auth_req_id,'"response" body "auth_req_id" property',st,{body:s});let c=typeof s.expires_in!=="number"?parseFloat(s.expires_in):s.expires_in;ik(c,true,'"response" body "expires_in" property',st,{body:s});s.expires_in=c;if(s.interval!==undefined){ik(s.interval,false,'"response" body "interval" property',st,{body:s})}return s}async function s6(o,i,a,s,c){iJ(o);iV(i);iO(s,'"authReqId"');const u=new URLSearchParams(c?.additionalParameters);u.set("auth_req_id",s);return aD(o,i,a,"urn:openid:params:grant-type:ciba",u,c)}async function s8(o,i,a,s){return aW(o,i,a,undefined,s)}async function s5(o,i,a){iJ(o);const s=at(o,"registration_endpoint",i.use_mtls_endpoint_aliases,a?.[o4]!==true);const c=i_(a?.headers);c.set("accept","application/json");c.set("content-type","application/json");const u="POST";if(a?.DPoP){aE(a.DPoP);await a.DPoP.addProof(s,c,u,a.initialAccessToken)}if(a?.initialAccessToken){c.set("authorization",`${c.has("dpop")?"DPoP":"Bearer"} ${a.initialAccessToken}`)}const l=await (a?.[ie]||fetch)(s.href,{body:JSON.stringify(i),headers:Object.fromEntries(c.entries()),method:u,redirect:"manual",signal:iv(s,a?.signal)});a?.DPoP?.cacheNonce(l);return l}async function s4(o){if(!o3(o,Response)){throw o5('"response" must be an instance of Response',o8)}aK(o);await av(o,201,"Dynamic Client Registration Endpoint");s_(o);const i=await ce(o);iO(i.client_id,'"response" body "client_id" property',st,{body:i});if(i.client_secret!==undefined){iO(i.client_secret,'"response" body "client_secret" property',st,{body:i})}if(i.client_secret){ik(i.client_secret_expires_at,true,'"response" body "client_secret_expires_at" property',st,{body:i})}return i}async function s9(o,i){return iA(o,"resourceIdentifier",o=>{iS(o,".well-known/oauth-protected-resource",true);return o},i)}async function s7(o,i){const a=o;if(!(a instanceof URL)&&a!==cn){throw o5('"expectedResourceIdentifier" must be an instance of URL',o8)}if(!o3(i,Response)){throw o5('"response" must be an instance of Response',o8)}if(i.status!==200){throw ih('"response" is not a conform Resource Server Metadata response (unexpected HTTP status code)',so,i)}s_(i);const s=await ce(i);iO(s.resource,'"response" body "resource" property',st,{body:s});if(a!==cn&&new URL(s.resource).href!==a.href){throw ih('"response" body "resource" property does not match the expected value',su,{expected:a.href,body:s,attribute:"resource"})}return s}async function ce(o,i=ix){let a;try{a=await o.json()}catch(a){i(o);throw ih('failed to parse "response" body as JSON',se,a)}if(!iw(a)){throw ih('"response" body must be a top level object',st,{body:a})}return a}const ct=null&&aX;const cn=Symbol();const cr=Symbol();const co=60*15;async function ci(o,i,a){const{cookies:s,logger:c}=a;const u=s[o];const l=new Date;l.setTime(l.getTime()+co*1e3);c.debug(`CREATE_${o.toUpperCase()}`,{name:u.name,payload:i,COOKIE_TTL:co,expires:l});const d=await nL({...a.jwt,maxAge:co,token:{value:i},salt:u.name});const f={...u.options,expires:l};return{name:u.name,value:d,options:f}}async function ca(o,i,a){try{const{logger:s,cookies:c,jwt:u}=a;s.debug(`PARSE_${o.toUpperCase()}`,{cookie:i});if(!i)throw new C(`${o} cookie was missing`);const l=await n$({...u,token:i,salt:c[o].name});if(l?.value)return l.value;throw new Error("Invalid cookie")}catch(i){throw new C(`${o} value could not be parsed`,{cause:i})}}function cs(o,i,a){const{logger:s,cookies:c}=i;const u=c[o];s.debug(`CLEAR_${o.toUpperCase()}`,{cookie:u});a.push({name:u.name,value:"",options:{...c[o].options,maxAge:0}})}function cc(o,i){return async function(a,s,c){const{provider:u,logger:l}=c;if(!u?.checks?.includes(o))return;const d=a?.[c.cookies[i].name];l.debug(`USE_${i.toUpperCase()}`,{value:d});const f=await ca(i,d,c);cs(i,c,s);return f}}const cu={async create(o){const i=iD();const a=await iL(i);const s=await ci("pkceCodeVerifier",i,o);return{cookie:s,value:a}},use:cc("pkce","pkceCodeVerifier")};const cl=60*15;const cd="encodedState";const cf={async create(o,i){const{provider:a}=o;if(!a.checks.includes("state")){if(i){throw new C("State data was provided but the provider is not configured to use state")}return}const s={origin:i,random:iU()};const c=await nL({secret:o.jwt.secret,token:s,salt:cd,maxAge:cl});const u=await ci("state",c,o);return{cookie:u,value:c}},use:cc("state","state"),async decode(o,i){try{i.logger.debug("DECODE_STATE",{state:o});const a=await n$({secret:i.jwt.secret,token:o,salt:cd});if(a)return a;throw new Error("Invalid state")}catch(o){throw new C("State could not be decoded",{cause:o})}}};const cp={async create(o){if(!o.provider.checks.includes("nonce"))return;const i=iM();const a=await ci("nonce",i,o);return{cookie:a,value:i}},use:cc("nonce","nonce")};const ch=60*15;const cy="encodedWebauthnChallenge";const cg={async create(o,i,a){return{cookie:await ci("webauthnChallenge",await nL({secret:o.jwt.secret,token:{challenge:i,registerData:a},salt:cy,maxAge:ch}),o)}},async use(o,i,a){const s=i?.[o.cookies.webauthnChallenge.name];const c=await ca("webauthnChallenge",s,o);const u=await n$({secret:o.jwt.secret,token:c,salt:cy});cs("webauthnChallenge",o,a);if(!u)throw new C("WebAuthn challenge was missing");return u}};function cm(o){if(typeof o!=="string")throw new eB("JWTs must use Compact JWS serialization, JWT must be a string");const{1:i,length:a}=o.split(".");if(a===5)throw new eB("Only JWTs using Compact JWS serialization can be decoded");if(a!==3)throw new eB("Invalid JWT");if(!i)throw new eB("JWTs must contain a payload");let s;try{s=eN(i)}catch{throw new eB("Failed to base64url decode the payload")}let c;try{c=JSON.parse(eA.decode(s))}catch{throw new eB("Failed to parse the decoded payload as JSON")}if(!e1(c))throw new eB("Invalid JWT Claims Set");return c};function cb(o){return encodeURIComponent(o).replace(/%20/g,"+")}function cw(o,i){const a=cb(o);const s=cb(i);const c=btoa(`${a}:${s}`);return`Basic ${c}`}async function c_(o,i,a){const{logger:s,provider:c}=a;let u;const{token:l,userinfo:d}=c;if((!l?.url||l.url.host==="authjs.dev")&&(!d?.url||d.url.host==="authjs.dev")){const o=new URL(c.issuer);const i=await iP(o,{[o4]:true,[ie]:c[rn]});u=await iT(o,i);if(!u.token_endpoint)throw new TypeError("TODO: Authorization server did not provide a token endpoint.");if(!u.userinfo_endpoint)throw new TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}else{u={issuer:c.issuer??"https://authjs.dev",token_endpoint:l?.url.toString(),userinfo_endpoint:d?.url.toString()}}const f={client_id:c.clientId,...c.client};let p;switch(f.token_endpoint_auth_method){case undefined:case"client_secret_basic":p=(o,i,a,s)=>{s.set("authorization",cw(c.clientId,c.clientSecret))};break;case"client_secret_post":p=iX(c.clientSecret);break;case"client_secret_jwt":p=i0(c.clientSecret);break;case"private_key_jwt":p=iQ(c.token.clientPrivateKey,{[it](o,i){i.aud=[u.issuer,u.token_endpoint]}});break;case"none":p=i1();break;default:throw new Error("unsupported client authentication method")}const h=[];const y=await cf.use(i,h,a);let g;try{g=sq(u,f,new URLSearchParams(o),c.checks.includes("state")?y:sK)}catch(o){if(o instanceof as){const i={providerId:c.id,...Object.fromEntries(o.cause.entries())};s.debug("OAuthCallbackError",i);throw new L("OAuth Provider returned an error",i)}throw o}const m=await cu.use(i,h,a);let b=c.callbackUrl;if(!a.isOnRedirectProxy&&c.redirectProxyUrl){b=c.redirectProxyUrl}let w=await aY(u,f,p,g,b,m??"decoy",{[o4]:true,[ie]:(...o)=>{if(!c.checks.includes("pkce")){o[1].body.delete("code_verifier")}return(c[rn]??fetch)(...o)}});if(c.token?.conform){w=await c.token.conform(w.clone())??w}let _={};const v=rl(c);if(c[rr]){switch(c.id){case"microsoft-entra-id":case"azure-ad":{const o=await w.clone().json();if(o.error){const i={providerId:c.id,...o};throw new L(`OAuth Provider returned an error: ${o.error}`,i)}const{tid:i}=cm(o.id_token);if(typeof i==="string"){const o=/microsoftonline\.com\/(\w+)\/v2\.0/;const a=u.issuer?.match(o)?.[1]??"common";const s=new URL(u.issuer.replace(a,i));const l=await iP(s,{[ie]:c[rn]});u=await iT(s,l)}break}default:break}}const E=await a2(u,f,w,{expectedNonce:await cp.use(i,h,a),requireIdToken:v});const S=E;if(v){const i=a$(E);_=i;if(c[rr]&&c.id==="apple"){try{_.user=JSON.parse(o?.user)}catch{}}if(c.idToken===false){const o=await aA(u,f,E.access_token,{[ie]:c[rn],[o4]:true});_=await aN(u,f,i.sub,o)}}else{if(d?.request){const o=await d.request({tokens:S,provider:c});if(o instanceof Object)_=o}else if(d?.url){const o=await aA(u,f,E.access_token,{[ie]:c[rn],[o4]:true});_=await o.json()}else{throw new TypeError("No userinfo endpoint configured")}}if(S.expires_in){S.expires_at=Math.floor(Date.now()/1e3)+Number(S.expires_in)}const R=await cv(_,c,S,s);return{...R,profile:_,cookies:h}}async function cv(o,i,a,s){try{const s=await i.profile(o,a);const c={...s,id:crypto.randomUUID(),email:s.email?.toLowerCase()};return{user:c,account:{...a,provider:i.id,type:i.type,providerAccountId:s.id??crypto.randomUUID()}}}catch(a){s.debug("getProfile error details",o);s.error(new $(a,{provider:i.id}))}};function cE(o,i,a){const{user:s,exists:c=false}=a??{};switch(o){case"authenticate":{return"authenticate"}case"register":{if(s&&i===c)return"register";break}case undefined:{if(!i){if(s){if(c){return"authenticate"}else{return"register"}}else{return"authenticate"}}break}}return null}async function cS(o,i,a,s){const c=await cO(o,i,a);const{cookie:u}=await cg.create(o,c.challenge,a);return{status:200,cookies:[...s??[],u],body:{action:"register",options:c},headers:{"Content-Type":"application/json"}}}async function cR(o,i,a,s){const c=await ck(o,i,a);const{cookie:u}=await cg.create(o,c.challenge);return{status:200,cookies:[...s??[],u],body:{action:"authenticate",options:c},headers:{"Content-Type":"application/json"}}}async function cA(o,i,a){const{adapter:s,provider:c}=o;const u=i.body&&typeof i.body.data==="string"?JSON.parse(i.body.data):undefined;if(!u||typeof u!=="object"||!("id"in u)||typeof u.id!=="string"){throw new v("Invalid WebAuthn Authentication response")}const l=cj(cC(u.id));const d=await s.getAuthenticator(l);if(!d){throw new v(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:l})}`)}const{challenge:f}=await cg.use(o,i.cookies,a);let p;try{const a=c.getRelayingParty(o,i);p=await c.simpleWebAuthn.verifyAuthenticationResponse({...c.verifyAuthenticationOptions,expectedChallenge:f,response:u,authenticator:cx(d),expectedOrigin:a.origin,expectedRPID:a.id})}catch(o){throw new ee(o)}const{verified:h,authenticationInfo:y}=p;if(!h){throw new ee("WebAuthn authentication response could not be verified")}try{const{newCounter:o}=y;await s.updateAuthenticatorCounter(d.credentialID,o)}catch(o){throw new S(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:l,oldCounter:d.counter,newCounter:y.newCounter})}`,o)}const g=await s.getAccount(d.providerAccountId,c.id);if(!g){throw new v(`WebAuthn account not found in database: ${JSON.stringify({credentialID:l,providerAccountId:d.providerAccountId})}`)}const m=await s.getUser(g.userId);if(!m){throw new v(`WebAuthn user not found in database: ${JSON.stringify({credentialID:l,providerAccountId:d.providerAccountId,userID:g.userId})}`)}return{account:g,user:m}}async function cP(o,i,a){const{provider:s}=o;const c=i.body&&typeof i.body.data==="string"?JSON.parse(i.body.data):undefined;if(!c||typeof c!=="object"||!("id"in c)||typeof c.id!=="string"){throw new v("Invalid WebAuthn Registration response")}const{challenge:u,registerData:l}=await cg.use(o,i.cookies,a);if(!l){throw new v("Missing user registration data in WebAuthn challenge cookie")}let d;try{const a=s.getRelayingParty(o,i);d=await s.simpleWebAuthn.verifyRegistrationResponse({...s.verifyRegistrationOptions,expectedChallenge:u,response:c,expectedOrigin:a.origin,expectedRPID:a.id})}catch(o){throw new ee(o)}if(!d.verified||!d.registrationInfo){throw new ee("WebAuthn registration response could not be verified")}const f={providerAccountId:cj(d.registrationInfo.credentialID),provider:o.provider.id,type:s.type};const p={providerAccountId:f.providerAccountId,counter:d.registrationInfo.counter,credentialID:cj(d.registrationInfo.credentialID),credentialPublicKey:cj(d.registrationInfo.credentialPublicKey),credentialBackedUp:d.registrationInfo.credentialBackedUp,credentialDeviceType:d.registrationInfo.credentialDeviceType,transports:cN(c.response.transports)};return{user:l,account:f,authenticator:p}}async function ck(o,i,a){const{provider:s,adapter:c}=o;const u=a&&a["id"]?await c.listAuthenticatorsByUserId(a.id):null;const l=s.getRelayingParty(o,i);return await s.simpleWebAuthn.generateAuthenticationOptions({...s.authenticationOptions,rpID:l.id,allowCredentials:u?.map(o=>({id:cC(o.credentialID),type:"public-key",transports:cI(o.transports)}))})}async function cO(o,i,a){const{provider:s,adapter:c}=o;const u=a["id"]?await c.listAuthenticatorsByUserId(a.id):null;const l=n6(32);const d=s.getRelayingParty(o,i);return await s.simpleWebAuthn.generateRegistrationOptions({...s.registrationOptions,userID:l,userName:a.email,userDisplayName:a.name??undefined,rpID:d.id,rpName:d.name,excludeCredentials:u?.map(o=>({id:cC(o.credentialID),type:"public-key",transports:cI(o.transports)}))})}function cT(o){const{provider:i,adapter:a}=o;if(!a)throw new N("An adapter is required for the WebAuthn provider");if(!i||i.type!=="webauthn"){throw new F("Provider must be WebAuthn")}return{...o,provider:i,adapter:a}}function cx(o){return{...o,credentialDeviceType:o.credentialDeviceType,transports:cI(o.transports),credentialID:cC(o.credentialID),credentialPublicKey:cC(o.credentialPublicKey)}}function cC(o){return new Uint8Array(Buffer.from(o,"base64"))}function cj(o){return Buffer.from(o).toString("base64")}function cN(o){return o?.join(",")}function cI(o){return o?o.split(","):undefined};async function cD(o,i,a,s){if(!i.provider)throw new F("Callback route called without provider");const{query:c,body:u,method:l,headers:d}=o;const{provider:f,adapter:p,url:h,callbackUrl:y,pages:g,jwt:m,events:b,callbacks:w,session:{strategy:_,maxAge:E},logger:S}=i;const R=_==="jwt";try{if(f.type==="oauth"||f.type==="oidc"){const l=f.authorization?.url.searchParams.get("response_mode")==="form_post"?u:c;if(i.isOnRedirectProxy&&l?.state){const o=await cf.decode(l.state,i);const a=o?.origin&&new URL(o.origin).origin!==i.url.origin;if(a){const i=`${o.origin}?${new URLSearchParams(l)}`;S.debug("Proxy redirecting to",i);return{redirect:i,cookies:s}}}const d=await c_(l,o.cookies,i);if(d.cookies.length){s.push(...d.cookies)}S.debug("authorization result",d);const{user:_,account:v,profile:A}=d;if(!_||!v||!A){return{redirect:`${h}/signin`,cookies:s}}let P;if(p){const{getUserByAccount:o}=p;P=await o({providerAccountId:v.providerAccountId,provider:f.id})}const k=await cU({user:P??_,account:v,profile:A},i);if(k)return{redirect:k,cookies:s};const{user:O,session:T,isNewUser:x}=await o1(a.value,_,v,i);if(R){const o={name:O.name,email:O.email,picture:O.image,sub:O.id?.toString()};const c=await w.jwt({token:o,user:O,account:v,profile:A,isNewUser:x,trigger:x?"signUp":"signIn"});if(c===null){s.push(...a.clean())}else{const o=i.cookies.sessionToken.name;const u=await m.encode({...m,token:c,salt:o});const l=new Date;l.setTime(l.getTime()+E*1e3);const d=a.chunk(u,{expires:l});s.push(...d)}}else{s.push({name:i.cookies.sessionToken.name,value:T.sessionToken,options:{...i.cookies.sessionToken.options,expires:T.expires}})}await b.signIn?.({user:O,account:v,profile:A,isNewUser:x});if(x&&g.newUser){return{redirect:`${g.newUser}${g.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:y})}`,cookies:s}}return{redirect:y,cookies:s}}else if(f.type==="email"){const o=c?.token;const u=c?.email;if(!o){const i=new TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!o}});i.name="Configuration";throw i}const l=f.secret??i.secret;const d=await p.useVerificationToken({identifier:u,token:await n3(`${o}${l}`)});const h=!!d;const _=h&&d.expires.valueOf()<Date.now();const v=!h||_||u&&d.identifier!==u;if(v)throw new V({hasInvite:h,expired:_});const{identifier:S}=d;const A=await p.getUserByEmail(S)??{id:crypto.randomUUID(),email:S,emailVerified:null};const P={providerAccountId:A.email,userId:A.id,type:"email",provider:f.id};const k=await cU({user:A,account:P},i);if(k)return{redirect:k,cookies:s};const{user:O,session:T,isNewUser:x}=await o1(a.value,A,P,i);if(R){const o={name:O.name,email:O.email,picture:O.image,sub:O.id?.toString()};const c=await w.jwt({token:o,user:O,account:P,isNewUser:x,trigger:x?"signUp":"signIn"});if(c===null){s.push(...a.clean())}else{const o=i.cookies.sessionToken.name;const u=await m.encode({...m,token:c,salt:o});const l=new Date;l.setTime(l.getTime()+E*1e3);const d=a.chunk(u,{expires:l});s.push(...d)}}else{s.push({name:i.cookies.sessionToken.name,value:T.sessionToken,options:{...i.cookies.sessionToken.options,expires:T.expires}})}await b.signIn?.({user:O,account:P,isNewUser:x});if(x&&g.newUser){return{redirect:`${g.newUser}${g.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:y})}`,cookies:s}}return{redirect:y,cookies:s}}else if(f.type==="credentials"&&l==="POST"){const o=u??{};Object.entries(c??{}).forEach(([o,i])=>h.searchParams.set(o,i));const p=await f.authorize(o,new Request(h,{headers:d,method:l,body:JSON.stringify(u)}));const g=p;if(!g)throw new T;else g.id=g.id?.toString()??crypto.randomUUID();const _={providerAccountId:g.id,type:"credentials",provider:f.id};const v=await cU({user:g,account:_,credentials:o},i);if(v)return{redirect:v,cookies:s};const S={name:g.name,email:g.email,picture:g.image,sub:g.id};const R=await w.jwt({token:S,user:g,account:_,isNewUser:false,trigger:"signIn"});if(R===null){s.push(...a.clean())}else{const o=i.cookies.sessionToken.name;const c=await m.encode({...m,token:R,salt:o});const u=new Date;u.setTime(u.getTime()+E*1e3);const l=a.chunk(c,{expires:u});s.push(...l)}await b.signIn?.({user:g,account:_});return{redirect:y,cookies:s}}else if(f.type==="webauthn"&&l==="POST"){const c=o.body?.action;if(typeof c!=="string"||c!=="authenticate"&&c!=="register"){throw new v("Invalid action parameter")}const u=cT(i);let l;let d;let f;switch(c){case"authenticate":{const i=await cA(u,o,s);l=i.user;d=i.account;break}case"register":{const a=await cP(i,o,s);l=a.user;d=a.account;f=a.authenticator;break}}await cU({user:l,account:d},i);const{user:p,isNewUser:h,session:_,account:S}=await o1(a.value,l,d,i);if(!S){throw new v("Error creating or finding account")}if(f&&p.id){await u.adapter.createAuthenticator({...f,userId:p.id})}if(R){const o={name:p.name,email:p.email,picture:p.image,sub:p.id?.toString()};const c=await w.jwt({token:o,user:p,account:S,isNewUser:h,trigger:h?"signUp":"signIn"});if(c===null){s.push(...a.clean())}else{const o=i.cookies.sessionToken.name;const u=await m.encode({...m,token:c,salt:o});const l=new Date;l.setTime(l.getTime()+E*1e3);const d=a.chunk(u,{expires:l});s.push(...d)}}else{s.push({name:i.cookies.sessionToken.name,value:_.sessionToken,options:{...i.cookies.sessionToken.options,expires:_.expires}})}await b.signIn?.({user:p,account:S,isNewUser:h});if(h&&g.newUser){return{redirect:`${g.newUser}${g.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:y})}`,cookies:s}}return{redirect:y,cookies:s}}throw new F(`Callback for provider type (${f.type}) is not supported`)}catch(i){if(i instanceof v)throw i;const o=new A(i,{provider:f.id});S.debug("callback route error details",{method:l,query:c,body:u});throw o}}async function cU(o,i){let a;const{signIn:s,redirect:c}=i.callbacks;try{a=await s(o)}catch(o){if(o instanceof v)throw o;throw new R(o)}if(!a)throw new R("AccessDenied");if(typeof a!=="string")return;return await c({url:a,baseUrl:i.url.origin})};async function cM(o,i,a,s,c){const{adapter:u,jwt:l,events:d,callbacks:f,logger:p,session:{strategy:h,maxAge:y}}=o;const g={body:null,headers:{"Content-Type":"application/json",...!s&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:a};const m=i.value;if(!m)return g;if(h==="jwt"){try{const a=o.cookies.sessionToken.name;const u=await l.decode({...l,token:m,salt:a});if(!u)throw new Error("Invalid JWT");const p=await f.jwt({token:u,...s&&{trigger:"update"},session:c});const h=o0(y);if(p!==null){const o={user:{name:p.name,email:p.email,image:p.picture},expires:h.toISOString()};const s=await f.session({session:o,token:p});g.body=s;const c=await l.encode({...l,token:p,salt:a});const u=i.chunk(c,{expires:h});g.cookies?.push(...u);await d.session?.({session:s,token:p})}else{g.cookies?.push(...i.clean())}}catch(o){p.error(new j(o));g.cookies?.push(...i.clean())}return g}try{const{getSessionAndUser:a,deleteSession:l,updateSession:p}=u;let h=await a(m);if(h&&h.session.expires.valueOf()<Date.now()){await l(m);h=null}if(h){const{user:i,session:a}=h;const u=o.session.updateAge;const l=a.expires.valueOf()-y*1e3+u*1e3;const b=o0(y);if(l<=Date.now()){await p({sessionToken:m,expires:b})}const w=await f.session({session:{...a,user:i},user:i,newSession:c,...s?{trigger:"update"}:{}});g.body=w;g.cookies?.push({name:o.cookies.sessionToken.name,value:m,options:{...o.cookies.sessionToken.options,expires:b}});await d.session?.({session:w})}else if(m){g.cookies?.push(...i.clean())}}catch(o){p.error(new H(o))}return g};async function cL(o,i){const{logger:a,provider:s}=i;let c=s.authorization?.url;let u;if(!c||c.host==="authjs.dev"){const o=new URL(s.issuer);const i=await iP(o,{[ie]:s[rn],[o4]:true});const a=await iT(o,i).catch(i=>{if(!(i instanceof TypeError)||i.message!=="Invalid URL")throw i;throw new TypeError(`Discovery request responded with an invalid issuer. expected: ${o}`)});if(!a.authorization_endpoint){throw new TypeError("Authorization server did not provide an authorization endpoint.")}c=new URL(a.authorization_endpoint)}const l=c.searchParams;let d=s.callbackUrl;let f;if(!i.isOnRedirectProxy&&s.redirectProxyUrl){d=s.redirectProxyUrl;f=s.callbackUrl;a.debug("using redirect proxy",{redirect_uri:d,data:f})}const p=Object.assign({response_type:"code",client_id:s.clientId,redirect_uri:d,...s.authorization?.params},Object.fromEntries(s.authorization?.url.searchParams??[]),o);for(const o in p)l.set(o,p[o]);const h=[];if(s.authorization?.url.searchParams.get("response_mode")==="form_post"){i.cookies.state.options.sameSite="none";i.cookies.state.options.secure=true;i.cookies.nonce.options.sameSite="none";i.cookies.nonce.options.secure=true}const y=await cf.create(i,f);if(y){l.set("state",y.value);h.push(y.cookie)}if(s.checks?.includes("pkce")){if(u&&!u.code_challenge_methods_supported?.includes("S256")){if(s.type==="oidc")s.checks=["nonce"]}else{const{value:o,cookie:a}=await cu.create(i);l.set("code_challenge",o);l.set("code_challenge_method","S256");h.push(a)}}const g=await cp.create(i);if(g){l.set("nonce",g.value);h.push(g.cookie)}if(s.type==="oidc"&&!c.searchParams.has("scope")){c.searchParams.set("scope","openid profile email")}a.debug("authorization url is ready",{url:c,cookies:h,provider:s});return{redirect:c.toString(),cookies:h}};async function c$(o,i){const{body:a}=o;const{provider:s,callbacks:c,adapter:u}=i;const l=s.normalizeIdentifier??cH;const d=l(a?.email);const f={id:crypto.randomUUID(),email:d,emailVerified:null};const p=await u.getUserByEmail(d)??f;const h={providerAccountId:d,userId:p.id,type:"email",provider:s.id};let y;try{y=await c.signIn({user:p,account:h,email:{verificationRequest:true}})}catch(o){throw new R(o)}if(!y)throw new R("AccessDenied");if(typeof y==="string"){return{redirect:await c.redirect({url:y,baseUrl:i.url.origin})}}const{callbackUrl:g,theme:m}=i;const b=await s.generateVerificationToken?.()??n6(32);const w=86400;const _=new Date(Date.now()+(s.maxAge??w)*1e3);const v=s.secret??i.secret;const E=new URL(i.basePath,i.url.origin);const S=s.sendVerificationRequest({identifier:d,token:b,expires:_,url:`${E}/callback/${s.id}?${new URLSearchParams({callbackUrl:g,token:b,email:d})}`,provider:s,theme:m,request:n1(o)});const A=u.createVerificationToken?.({identifier:d,token:await n3(`${b}${v}`),expires:_});await Promise.all([S,A]);return{redirect:`${E}/verify-request?${new URLSearchParams({provider:s.id,type:s.type})}`}}function cH(o){if(!o)throw new Error("Missing email from request body.");let[i,a]=o.toLowerCase().trim().split("@");a=a.split(",")[0];return`${i}@${a}`};async function cW(o,i,a){const s=`${a.url.origin}${a.basePath}/signin`;if(!a.provider)return{redirect:s,cookies:i};switch(a.provider.type){case"oauth":case"oidc":{const{redirect:s,cookies:c}=await cL(o.query,a);if(c)i.push(...c);return{redirect:s,cookies:i}}case"email":{const s=await c$(o,a);return{...s,cookies:i}}default:return{redirect:s,cookies:i}}};async function cK(o,i,a){const{jwt:s,events:c,callbackUrl:u,logger:l,session:d}=a;const f=i.value;if(!f)return{redirect:u,cookies:o};try{if(d.strategy==="jwt"){const o=a.cookies.sessionToken.name;const i=await s.decode({...s,token:f,salt:o});await c.signOut?.({token:i})}else{const o=await a.adapter?.deleteSession(f);await c.signOut?.({session:o})}}catch(o){l.error(new B(o))}o.push(...i.clean());return{redirect:u,cookies:o}};async function cB(o,i){const{adapter:a,jwt:s,session:{strategy:c}}=o;const u=i.value;if(!u)return null;if(c==="jwt"){const i=o.cookies.sessionToken.name;const a=await s.decode({...s,token:u,salt:i});if(a&&a.sub){return{id:a.sub,name:a.name,email:a.email,image:a.picture}}}else{const o=await a?.getSessionAndUser(u);if(o){return o.user}}return null};async function cq(o,i,a,s){const c=cT(i);const{provider:u}=c;const{action:l}=o.query??{};if(l!=="register"&&l!=="authenticate"&&typeof l!=="undefined"){return{status:400,body:{error:"Invalid action"},cookies:s,headers:{"Content-Type":"application/json"}}}const d=await cB(i,a);const f=d?{user:d,exists:true}:await u.getUserInfo(i,o);const p=f?.user;const h=cE(l,!!d,f);switch(h){case"authenticate":return cR(c,o,p,s);case"register":if(typeof p?.email==="string"){return cS(c,o,p,s)}break;default:return{status:400,body:{error:"Invalid request"},cookies:s,headers:{"Content-Type":"application/json"}}}};;async function cG(o,i){const{action:a,providerId:s,error:c,method:u}=o;const l=i.skipCSRFCheck===re;const{options:d,cookies:f}=await rh({authOptions:i,action:a,providerId:s,url:o.url,callbackUrl:o.body?.callbackUrl??o.query?.callbackUrl,csrfToken:o.body?.csrfToken,cookies:o.cookies,isPost:u==="POST",csrfDisabled:l});const p=new _(d.cookies.sessionToken,o.cookies,d.logger);if(u==="GET"){const i=oQ({...d,query:o.query,cookies:f});switch(a){case"callback":return await cD(o,d,p,f);case"csrf":return i.csrf(l,d,f);case"error":return i.error(c);case"providers":return i.providers(d.providers);case"session":return await cM(d,p,f);case"signin":return i.signin(s,c);case"signout":return i.signout();case"verify-request":return i.verifyRequest();case"webauthn-options":return await cq(o,d,p,f);default:}}else{const{csrfTokenVerified:i}=d;switch(a){case"callback":if(d.provider.type==="credentials")n4(a,i);return await cD(o,d,p,f);case"session":n4(a,i);return await cM(d,p,f,true,o.body?.data);case"signin":n4(a,i);return await cW(o,f,d);case"signout":n4(a,i);return await cK(f,p,d);default:}}throw new q(`Cannot handle action: ${a}`)};function cF(o,i,a=false){try{const s=o.AUTH_URL;if(s){if(i.basePath){if(!a){const o=nV(i);o.warn("env-url-basepath-redundant")}}else{i.basePath=new URL(s).pathname}}}catch{}finally{i.basePath??(i.basePath=`/auth`)}if(!i.secret?.length){i.secret=[];const a=o.AUTH_SECRET;if(a)i.secret.push(a);for(const a of[1,2,3]){const s=o[`AUTH_SECRET_${a}`];if(s)i.secret.unshift(s)}}i.redirectProxyUrl??(i.redirectProxyUrl=o.AUTH_REDIRECT_PROXY_URL);i.trustHost??(i.trustHost=!!(o.AUTH_URL??o.AUTH_TRUST_HOST??o.VERCEL??o.CF_PAGES??o.NODE_ENV!=="production"));i.providers=i.providers.map(i=>{const{id:a}=typeof i==="function"?i({}):i;const s=a.toUpperCase().replace(/-/g,"_");const c=o[`AUTH_${s}_ID`];const u=o[`AUTH_${s}_SECRET`];const l=o[`AUTH_${s}_ISSUER`];const d=o[`AUTH_${s}_KEY`];const f=typeof i==="function"?i({clientId:c,clientSecret:u,issuer:l,apiKey:d}):i;if(f.type==="oauth"||f.type==="oidc"){f.clientId??(f.clientId=c);f.clientSecret??(f.clientSecret=u);f.issuer??(f.issuer=l)}else if(f.type==="email"){f.apiKey??(f.apiKey=d)}return f})}function cJ(o,i,a,s,c){const u=c?.basePath;const l=s.AUTH_URL??s.NEXTAUTH_URL;let d;if(l){d=new URL(l);if(u&&u!=="/"&&d.pathname!=="/"){if(d.pathname!==u){const o=nV(c);o.warn("env-url-basepath-mismatch")}d.pathname="/"}}else{const o=a.get("x-forwarded-host")??a.get("host");const s=a.get("x-forwarded-proto")??i??"https";const c=s.endsWith(":")?s:s+":";d=new URL(`${c}//${o}`)}const f=d.toString().replace(/\/$/,"");if(u){const i=u?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${f}/${i}/${o}`)}return new URL(`${f}/${o}`)};async function cV(o,i){const a=nV(i);const s=await n0(o,i);if(!s)return Response.json(`Bad request.`,{status:400});const c=ef(s,i);if(Array.isArray(c)){c.forEach(a.warn)}else if(c){a.error(c);const o=new Set(["signin","signout","error","verify-request"]);if(!o.has(s.action)||s.method!=="GET"){const o="There was a problem with the server configuration. Check the server logs for more information.";return Response.json({message:o},{status:500})}const{pages:u,theme:l}=i;const d=u?.error&&s.url.searchParams.get("callbackUrl")?.startsWith(u.error);if(!u?.error||d){if(d){a.error(new P(`The error page ${u?.error} should not require authentication`))}const o=oQ({theme:l}).error("Configuration");return n2(o)}const f=`${s.url.origin}${u.error}?error=Configuration`;return Response.redirect(f)}const u=o.headers?.has("X-Auth-Return-Redirect");const l=i.raw===rt;try{const o=await cG(s,i);if(l)return o;const a=n2(o);const c=a.headers.get("Location");if(!u||!c)return a;return Response.json({url:c},{headers:a.headers})}catch(b){const c=b;a.error(c);const d=c instanceof v;if(d&&l&&!u)throw c;if(o.method==="POST"&&s.action==="session")return Response.json(null,{status:400});const f=Y(c);const p=f?c.type:"Configuration";const h=new URLSearchParams({error:p});if(c instanceof T)h.set("code",c.code);const y=d&&c.kind||"error";const g=i.pages?.[y]??`${i.basePath}/${y.toLowerCase()}`;const m=`${s.url.origin}${g}?${h}`;if(u)return Response.json({url:m});return Response.redirect(m)}}var cz=a(4525);;function cX(o){const i=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!i)return o;const{origin:a}=new URL(i);const{href:s,origin:c}=o.nextUrl;return new cz.NextRequest(s.replace(c,a),o)}function cY(o){try{o.secret??(o.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);const i=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!i)return;const{pathname:a}=new URL(i);if(a==="/")return;o.basePath||(o.basePath=a)}catch{}finally{o.basePath||(o.basePath="/api/auth");cF(process.env,o,true)}}var cZ=a(9933);var cQ=a(6280);var c0=a(3913);;async function c1(o,i){const a=cJ("session",o.get("x-forwarded-proto"),o,process.env,i);const s=new Request(a,{headers:{cookie:o.get("cookie")??""}});return cV(s,{...i,callbacks:{...i.callbacks,async session(...o){const a=await i.callbacks?.session?.(...o)??{...o[0].session,expires:o[0].session.expires?.toISOString?.()??o[0].session.expires};const s=o[0].user??o[0].token;return{user:s,...a}}}})}function c2(o){return typeof o==="function"}function c3(o,i){if(typeof o==="function"){return async(...a)=>{if(!a.length){const a=await (0,cQ.b)();const s=await o(undefined);i?.(s);return c1(a,s).then(o=>o.json())}if(a[0]instanceof Request){const s=a[0];const c=a[1];const u=await o(s);i?.(u);return c6([s,c],u)}if(c2(a[0])){const s=a[0];return async(...a)=>{const c=await o(a[0]);i?.(c);return c6(a,c,s)}}const s="req"in a[0]?a[0].req:a[0];const c="res"in a[0]?a[0].res:a[1];const u=await o(s);i?.(u);return c1(new Headers(s.headers),u).then(async o=>{const i=await o.json();for(const i of o.headers.getSetCookie())if("headers"in c)c.headers.append("set-cookie",i);else c.appendHeader("set-cookie",i);return i})}}return(...i)=>{if(!i.length){return Promise.resolve((0,cQ.b)()).then(i=>c1(i,o).then(o=>o.json()))}if(i[0]instanceof Request){const a=i[0];const s=i[1];return c6([a,s],o)}if(c2(i[0])){const a=i[0];return async(...i)=>{return c6(i,o,a).then(o=>{return o})}}const a="req"in i[0]?i[0].req:i[0];const s="res"in i[0]?i[0].res:i[1];return c1(new Headers(a.headers),o).then(async o=>{const i=await o.json();for(const i of o.headers.getSetCookie())if("headers"in s)s.headers.append("set-cookie",i);else s.appendHeader("set-cookie",i);return i})}}async function c6(o,i,a){const s=cX(o[0]);const c=await c1(s.headers,i);const u=await c.json();let l=true;if(i.callbacks?.authorized){l=await i.callbacks.authorized({request:s,auth:u})}let d=cz.NextResponse.next?.();if(l instanceof Response){d=l;const o=l.headers.get("Location");const{pathname:a}=s.nextUrl;if(o&&c8(a,new URL(o).pathname,i)){l=true}}else if(a){const i=s;i.auth=u;d=await a(i,o[1])??cz.NextResponse.next()}else if(!l){const o=i.pages?.signIn??`${i.basePath}/signin`;if(s.nextUrl.pathname!==o){const i=s.nextUrl.clone();i.pathname=o;i.searchParams.set("callbackUrl",s.nextUrl.href);d=cz.NextResponse.redirect(i)}}const f=new Response(d?.body,d);for(const o of c.headers.getSetCookie())f.headers.append("set-cookie",o);return f}function c8(o,i,a){const s=i.replace(`${o}/`,"");const c=Object.values(a.pages??{});return(c5.has(s)||c.includes(i))&&i===o}const c5=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var c4=a(7576);;async function c9(o,i={},a,s){const c=new Headers(await (0,cQ.b)());const{redirect:u=true,redirectTo:l,...d}=i instanceof FormData?Object.fromEntries(i):i;const f=l?.toString()??c.get("Referer")??"/";const p=cJ("signin",c.get("x-forwarded-proto"),c,process.env,s);if(!o){p.searchParams.append("callbackUrl",f);if(u)(0,c4.redirect)(p.toString());return p.toString()}let h=`${p}/${o}?${new URLSearchParams(a)}`;let y={};for(const i of s.providers){const{options:a,...s}=typeof i==="function"?i():i;const c=a?.id??s.id;if(c===o){y={id:c,type:a?.type??s.type};break}}if(!y.id){const o=`${p}?${new URLSearchParams({callbackUrl:f})}`;if(u)(0,c4.redirect)(o);return o}if(y.type==="credentials"){h=h.replace("signin","callback")}c.set("Content-Type","application/x-www-form-urlencoded");const g=new URLSearchParams({...d,callbackUrl:f});const m=new Request(h,{method:"POST",headers:c,body:g});const b=await cV(m,{...s,raw:rt,skipCSRFCheck:re});const w=await (0,cZ.U)();for(const o of b?.cookies??[])w.set(o.name,o.value,o.options);const _=b instanceof Response?b.headers.get("Location"):b.redirect;const v=_??h;if(u)return(0,c4.redirect)(v);return v}async function c7(o,i){const a=new Headers(await (0,cQ.b)());a.set("Content-Type","application/x-www-form-urlencoded");const s=cJ("signout",a.get("x-forwarded-proto"),a,process.env,i);const c=o?.redirectTo??a.get("Referer")??"/";const u=new URLSearchParams({callbackUrl:c});const l=new Request(s,{method:"POST",headers:a,body:u});const d=await cV(l,{...i,raw:rt,skipCSRFCheck:re});const f=await (0,cZ.U)();for(const o of d?.cookies??[])f.set(o.name,o.value,o.options);if(o?.redirect??true)return(0,c4.redirect)(d.redirect);return d}async function ue(o,i){const a=new Headers(await (0,cQ.b)());a.set("Content-Type","application/json");const s=cJ("session",a.get("x-forwarded-proto"),a,process.env,i);const c=JSON.stringify({data:o});const u=new Request(s,{method:"POST",headers:a,body:c});const l=await cV(u,{...i,raw:rt,skipCSRFCheck:re});const d=await (0,cZ.U)();for(const o of l?.cookies??[])d.set(o.name,o.value,o.options);return l.body};function ut(o){if(typeof o==="function"){const i=async i=>{const a=await o(i);cY(a);return cV(cX(i),a)};return{handlers:{GET:i,POST:i},auth:c3(o,o=>cY(o)),signIn:async(i,a,s)=>{const c=await o(undefined);cY(c);return c9(i,a,s,c)},signOut:async i=>{const a=await o(undefined);cY(a);return c7(i,a)},unstable_update:async i=>{const a=await o(undefined);cY(a);return ue(i,a)}}}cY(o);const i=i=>cV(cX(i),o);return{handlers:{GET:i,POST:i},auth:c3(o),signIn:(i,a,s)=>{return c9(i,a,s,o)},signOut:i=>{return c7(i,o)},unstable_update:i=>{return ue(i,o)}}}},9893:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function s(o,i){for(var a in i)Object.defineProperty(o,a,{enumerable:true,get:i[a]})}s(i,{NextRequestAdapter:function(){return g},ResponseAborted:function(){return p},ResponseAbortedName:function(){return f},createAbortController:function(){return h},signalFromNodeResponse:function(){return y}});const c=a(6191);const u=a(7912);const l=a(6268);const d=a(898);const f="ResponseAborted";class p extends Error{constructor(...o){super(...o),this.name=f}}function h(o){const i=new AbortController;o.once("close",()=>{if(o.writableFinished)return;i.abort(new p)});return i}function y(o){const{errored:i,destroyed:a}=o;if(i||a){return AbortSignal.abort(i??new p)}const{signal:s}=h(o);return s}class g{static fromBaseNextRequest(o,i){if(false){}else if(true&&(0,d.isNodeNextRequest)(o)){return g.fromNodeNextRequest(o,i)}else{throw Object.defineProperty(new Error("Invariant: Unsupported NextRequest type"),"__NEXT_ERROR_CODE",{value:"E345",enumerable:false,configurable:true})}}static fromNodeNextRequest(o,i){let a=null;if(o.method!=="GET"&&o.method!=="HEAD"&&o.body){a=o.body}let s;if(o.url.startsWith("http")){s=new URL(o.url)}else{const i=(0,c.getRequestMeta)(o,"initURL");if(!i||!i.startsWith("http")){s=new URL(o.url,"http://n")}else{s=new URL(o.url,i)}}return new l.NextRequest(s,{method:o.method,headers:(0,u.fromNodeOutgoingHttpHeaders)(o.headers),duplex:"half",signal:i,...i.aborted?{}:{body:a}})}static fromWebNextRequest(o){let i=null;if(o.method!=="GET"&&o.method!=="HEAD"){i=o.body}return new l.NextRequest(o.url,{method:o.method,headers:(0,u.fromNodeOutgoingHttpHeaders)(o.headers),duplex:"half",signal:o.request.signal,...o.request.signal.aborted?{}:{body:i}})}}},9933:(o,i,a)=>{"use strict";var s;s={value:true};Object.defineProperty(i,"U",{enumerable:true,get:function(){return b}});const c=a(4069);const u=a(3158);const l=a(9294);const d=a(3033);const f=a(4971);const p=a(23);const h=a(8388);const y=a(6926);const g=a(4523);const m=a(8719);function b(){const o="cookies";const i=l.workAsyncStorage.getStore();const a=d.workUnitAsyncStorage.getStore();if(i){if(a&&a.phase==="after"&&!(0,m.isRequestAPICallableInsideAfter)()){throw Object.defineProperty(new Error(`Route ${i.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:false,configurable:true})}if(i.forceStatic){const o=w();return E(o)}if(a){if(a.type==="cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:false,configurable:true})}else if(a.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:false,configurable:true})}}if(i.dynamicShouldError){throw Object.defineProperty(new p.StaticGenBailoutError(`Route ${i.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:false,configurable:true})}if(a){if(a.type==="prerender"){return v(i.route,a)}else if(a.type==="prerender-ppr"){(0,f.postponeWithTracking)(i.route,o,a.dynamicTracking)}else if(a.type==="prerender-legacy"){(0,f.throwToInterruptStaticGeneration)(o,i,a)}}(0,f.trackDynamicDataInDynamicRender)(i,a)}const s=(0,d.getExpectedRequestStore)(o);let u;if((0,c.areCookiesMutableInCurrentPhase)(s)){u=s.userspaceMutableCookies}else{u=s.cookies}if(false){}else{return E(u)}}function w(){return c.RequestCookiesAdapter.seal(new u.RequestCookies(new Headers({})))}const _=new WeakMap;function v(o,i){const a=_.get(i);if(a){return a}const s=(0,h.makeHangingPromise)(i.renderSignal,"`cookies()`");_.set(i,s);Object.defineProperties(s,{[Symbol.iterator]:{value:function(){const a="`cookies()[Symbol.iterator]()`";const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},size:{get(){const a="`cookies().size`";const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},get:{value:function a(){let a;if(arguments.length===0){a="`cookies().get()`"}else{a=`\`cookies().get(${R(arguments[0])})\``}const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},getAll:{value:function a(){let a;if(arguments.length===0){a="`cookies().getAll()`"}else{a=`\`cookies().getAll(${R(arguments[0])})\``}const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},has:{value:function a(){let a;if(arguments.length===0){a="`cookies().has()`"}else{a=`\`cookies().has(${R(arguments[0])})\``}const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},set:{value:function a(){let a;if(arguments.length===0){a="`cookies().set()`"}else{const o=arguments[0];if(o){a=`\`cookies().set(${R(o)}, ...)\``}else{a="`cookies().set(...)`"}}const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},delete:{value:function(){let a;if(arguments.length===0){a="`cookies().delete()`"}else if(arguments.length===1){a=`\`cookies().delete(${R(arguments[0])})\``}else{a=`\`cookies().delete(${R(arguments[0])}, ...)\``}const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},clear:{value:function a(){const a="`cookies().clear()`";const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}},toString:{value:function a(){const a="`cookies().toString()`";const s=k(o,a);(0,f.abortAndThrowOnSynchronousRequestDataAccess)(o,a,s,i)}}});return s}function E(o){const i=_.get(o);if(i){return i}const a=Promise.resolve(o);_.set(o,a);Object.defineProperties(a,{[Symbol.iterator]:{value:o[Symbol.iterator]?o[Symbol.iterator].bind(o):O.bind(o)},size:{get(){return o.size}},get:{value:o.get.bind(o)},getAll:{value:o.getAll.bind(o)},has:{value:o.has.bind(o)},set:{value:o.set.bind(o)},delete:{value:o.delete.bind(o)},clear:{value:typeof o.clear==="function"?o.clear.bind(o):T.bind(o,a)},toString:{value:o.toString.bind(o)}});return a}function S(o,i){const a=_.get(o);if(a){return a}const s=new Promise(i=>(0,g.scheduleImmediate)(()=>i(o)));_.set(o,s);Object.defineProperties(s,{[Symbol.iterator]:{value:function(){const a="`...cookies()` or similar iteration";A(i,a);return o[Symbol.iterator]?o[Symbol.iterator].apply(o,arguments):O.call(o)},writable:false},size:{get(){const a="`cookies().size`";A(i,a);return o.size}},get:{value:function a(){let a;if(arguments.length===0){a="`cookies().get()`"}else{a=`\`cookies().get(${R(arguments[0])})\``}A(i,a);return o.get.apply(o,arguments)},writable:false},getAll:{value:function a(){let a;if(arguments.length===0){a="`cookies().getAll()`"}else{a=`\`cookies().getAll(${R(arguments[0])})\``}A(i,a);return o.getAll.apply(o,arguments)},writable:false},has:{value:function a(){let a;if(arguments.length===0){a="`cookies().has()`"}else{a=`\`cookies().has(${R(arguments[0])})\``}A(i,a);return o.has.apply(o,arguments)},writable:false},set:{value:function a(){let a;if(arguments.length===0){a="`cookies().set()`"}else{const o=arguments[0];if(o){a=`\`cookies().set(${R(o)}, ...)\``}else{a="`cookies().set(...)`"}}A(i,a);return o.set.apply(o,arguments)},writable:false},delete:{value:function(){let a;if(arguments.length===0){a="`cookies().delete()`"}else if(arguments.length===1){a=`\`cookies().delete(${R(arguments[0])})\``}else{a=`\`cookies().delete(${R(arguments[0])}, ...)\``}A(i,a);return o.delete.apply(o,arguments)},writable:false},clear:{value:function a(){const a="`cookies().clear()`";A(i,a);return typeof o.clear==="function"?o.clear.apply(o,arguments):T.call(o,s)},writable:false},toString:{value:function a(){const a="`cookies().toString()` or implicit casting";A(i,a);return o.toString.apply(o,arguments)},writable:false}});return s}function R(o){return typeof o==="object"&&o!==null&&typeof o.name==="string"?`'${o.name}'`:typeof o==="string"?`'${o}'`:"..."}function A(o,i){const a=d.workUnitAsyncStorage.getStore();if(a&&a.type==="request"&&a.prerenderPhase===true){const o=a;(0,f.trackSynchronousRequestDataAccessInDev)(o)}P(o,i)}const P=(0,y.createDedupedByCallsiteServerErrorLoggerDev)(k);function k(o,i){const a=o?`Route "${o}" `:"This route ";return Object.defineProperty(new Error(`${a}used ${i}. `+`\`cookies()\` should be awaited before using its value. `+`Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:false,configurable:true})}function O(){return this.getAll().map(o=>[o.name,o]).values()}function T(o){for(const o of this.getAll()){this.delete(o.name)}return o}},9938:(o,i,a)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});Object.defineProperty(i,"getNextPathnameInfo",{enumerable:true,get:function(){return l}});const s=a(1959);const c=a(9229);const u=a(2829);function l(o,i){var a;const{basePath:l,i18n:d,trailingSlash:f}=(a=i.nextConfig)!=null?a:{};const p={pathname:o,trailingSlash:o!=="/"?o.endsWith("/"):f};if(l&&(0,u.pathHasPrefix)(p.pathname,l)){p.pathname=(0,c.removePathPrefix)(p.pathname,l);p.basePath=l}let h=p.pathname;if(p.pathname.startsWith("/_next/data/")&&p.pathname.endsWith(".json")){const o=p.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");const a=o[0];p.buildId=a;h=o[1]!=="index"?"/"+o.slice(1).join("/"):"/";if(i.parseData===true){p.pathname=h}}if(d){let o=i.i18nProvider?i.i18nProvider.analyze(p.pathname):(0,s.normalizeLocalePath)(p.pathname,d.locales);p.locale=o.detectedLocale;var y;p.pathname=(y=o.pathname)!=null?y:p.pathname;if(!o.detectedLocale&&p.buildId){o=i.i18nProvider?i.i18nProvider.analyze(h):(0,s.normalizeLocalePath)(h,d.locales);if(o.detectedLocale){p.locale=o.detectedLocale}}}return p}}};