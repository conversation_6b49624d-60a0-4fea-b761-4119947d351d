(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[177],{1362:(e,t,n)=>{"use strict";n.d(t,{D:()=>d,N:()=>u});var s=n(2115);var r=(e,t,n,s,r,o,a,i)=>{let c=document.documentElement,l=["light","dark"];function d(t){(Array.isArray(e)?e:[e]).forEach(e=>{let n=e==="class",s=n&&o?r.map(e=>o[e]||e):r;n?(c.classList.remove(...s),c.classList.add(o&&o[t]?o[t]:t)):c.setAttribute(e,t)}),u(t)}function u(e){i&&l.includes(e)&&(c.style.colorScheme=e)}function f(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(s)d(s);else try{let e=localStorage.getItem(t)||n,s=a&&e==="system"?f():e;d(s)}catch(e){}};var o=["light","dark"],a="(prefers-color-scheme: dark)",i="object"=="undefined",c=s.createContext(void 0),l={setTheme:e=>{},themes:[]},d=()=>{var e;return(e=s.useContext(c))!=null?e:l},u=e=>s.useContext(c)?s.createElement(s.Fragment,null,e.children):s.createElement(h,{...e}),f=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:r=!0,enableColorScheme:i=!0,storageKey:l="theme",themes:d=f,defaultTheme:u=r?"system":"light",attribute:h="data-theme",value:g,children:w,nonce:b,scriptProps:E}=e;let[S,x]=s.useState(()=>m(l,u)),[_,k]=s.useState(()=>S==="system"?y():S),L=g?Object.values(g):d,A=s.useCallback(e=>{let t=e;if(!t)return;e==="system"&&r&&(t=y());let s=g?g[t]:t,a=n?p(b):null,c=document.documentElement,l=e=>{e==="class"?(c.classList.remove(...L),s&&c.classList.add(s)):e.startsWith("data-")&&(s?c.setAttribute(e,s):c.removeAttribute(e))};if(Array.isArray(h)?h.forEach(l):l(h),i){let e=o.includes(u)?u:null,n=o.includes(t)?t:e;c.style.colorScheme=n}a==null||a()},[b]),C=s.useCallback(e=>{let t=typeof e=="function"?e(S):e;x(t);try{localStorage.setItem(l,t)}catch(e){}},[S]),T=s.useCallback(e=>{let n=y(e);k(n),S==="system"&&r&&!t&&A("system")},[S,t]);s.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(T),T(e),()=>e.removeListener(T)},[T]),s.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?x(e.newValue):C(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),s.useEffect(()=>{A(t!=null?t:S)},[t,S]);let U=s.useMemo(()=>({theme:S,setTheme:C,forcedTheme:t,resolvedTheme:S==="system"?_:S,themes:r?[...d,"system"]:d,systemTheme:r?_:void 0}),[S,C,t,_,r,d]);return s.createElement(c.Provider,{value:U},s.createElement(v,{forcedTheme:t,storageKey:l,attribute:h,enableSystem:r,enableColorScheme:i,defaultTheme:u,value:g,themes:d,nonce:b,scriptProps:E}),w)},v=s.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:o,enableSystem:a,enableColorScheme:i,defaultTheme:c,value:l,themes:d,nonce:u,scriptProps:f}=e;let h=JSON.stringify([o,n,c,t,d,l,a,i]).slice(1,-1);return s.createElement("script",{...f,suppressHydrationWarning:!0,nonce:false?0:"",dangerouslySetInnerHTML:{__html:"(".concat(r.toString(),")(").concat(h,")")}})}),m=(e,t)=>{if(i)return;let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},p=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},4993:e=>{e.exports={"style":{"fontFamily":"'spaceGrotesk', 'spaceGrotesk Fallback'"},"className":"__className_fc28aa","variable":"__variable_fc28aa"}},5493:(e,t,n)=>{"use strict";n.d(t,{SessionProvider:()=>eh});var s=n(5155);var r=n(2115);var o=n.t(r,2);class a extends Error{constructor(e,t){if(e instanceof Error){super(undefined,{cause:{err:e,...e.cause,...t}})}else if(typeof e==="string"){if(t instanceof Error){t={err:t,...t.cause}}super(e,t)}else{super(undefined,e)}this.name=this.constructor.name;this.type=this.constructor.type??"AuthError";this.kind=this.constructor.kind??"error";Error.captureStackTrace?.(this,this.constructor);const n=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${n}`}}class i extends a{}i.kind="signIn";class c extends a{}c.type="AdapterError";class l extends a{}l.type="AccessDenied";class d extends a{}d.type="CallbackRouteError";class u extends a{}u.type="ErrorPageLoop";class f extends a{}f.type="EventError";class h extends a{}h.type="InvalidCallbackUrl";class v extends i{constructor(){super(...arguments);this.code="credentials"}}v.type="CredentialsSignin";class m extends a{}m.type="InvalidEndpoints";class p extends a{}p.type="InvalidCheck";class y extends a{}y.type="JWTSessionError";class g extends a{}g.type="MissingAdapter";class w extends a{}w.type="MissingAdapterMethods";class b extends a{}b.type="MissingAuthorize";class E extends a{}E.type="MissingSecret";class S extends i{}S.type="OAuthAccountNotLinked";class x extends i{}x.type="OAuthCallbackError";class _ extends a{}_.type="OAuthProfileParseError";class k extends a{}k.type="SessionTokenError";class L extends i{}L.type="OAuthSignInError";class A extends i{}A.type="EmailSignInError";class C extends a{}C.type="SignOutError";class T extends a{}T.type="UnknownAction";class U extends a{}U.type="UnsupportedStrategy";class R extends a{}R.type="InvalidProvider";class P extends a{}P.type="UntrustedHost";class N extends a{}N.type="Verification";class M extends i{}M.type="MissingCSRF";const I=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function O(e){if(e instanceof a)return I.has(e.type);return false}class j extends a{}j.type="DuplicateConditionalUI";class H extends a{}H.type="MissingWebAuthnAutocomplete";class F extends a{}F.type="WebAuthnVerificationError";class W extends i{}W.type="AccountNotLinked";class V extends a{}V.type="ExperimentalFeatureNotEnabled";class X extends a{}class D extends a{}async function $(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const r="".concat(B(t),"/").concat(e);try{var o;const e={headers:{"Content-Type":"application/json",...(s===null||s===void 0?void 0:(o=s.headers)===null||o===void 0?void 0:o.cookie)?{cookie:s.headers.cookie}:{}}};if(s===null||s===void 0?void 0:s.body){e.body=JSON.stringify(s.body);e.method="POST"}const t=await fetch(r,e);const n=await t.json();if(!t.ok)throw n;return n}catch(e){n.error(new X(e.message,e));return null}}function B(e){if(false){}return e.basePath}function J(){const[e,t]=r.useState(typeof navigator!=="undefined"?navigator.onLine:false);const n=()=>t(true);const s=()=>t(false);r.useEffect(()=>{window.addEventListener("online",n);window.addEventListener("offline",s);return()=>{window.removeEventListener("online",n);window.removeEventListener("offline",s)}},[]);return e}function K(){return Math.floor(Date.now()/1e3)}function z(e){const t=new URL("http://localhost:3000/api/auth");if(e&&!e.startsWith("http")){e="https://".concat(e)}const n=new URL(e||t);const s=(n.pathname==="/"?t.pathname:n.pathname).replace(/\/$/,"");const r="".concat(n.origin).concat(s);return{origin:n.origin,host:n.host,path:s,base:r,toString:()=>r}};var G=n(9509);var q;var Q,Y,Z,ee;const et={baseUrl:z((Q=G.env.NEXTAUTH_URL)!==null&&Q!==void 0?Q:G.env.VERCEL_URL).origin,basePath:z(G.env.NEXTAUTH_URL).path,baseUrlServer:z((Z=(Y=G.env.NEXTAUTH_URL_INTERNAL)!==null&&Y!==void 0?Y:G.env.NEXTAUTH_URL)!==null&&Z!==void 0?Z:G.env.VERCEL_URL).origin,basePathServer:z((ee=G.env.NEXTAUTH_URL_INTERNAL)!==null&&ee!==void 0?ee:G.env.NEXTAUTH_URL).path,_lastSync:0,_session:undefined,_getSession:()=>{}};let en=null;function es(){if(typeof BroadcastChannel==="undefined"){return{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{},name:"next-auth",onmessage:null,onmessageerror:null,close:()=>{},dispatchEvent:()=>false}}return new BroadcastChannel("next-auth")}function er(){if(en===null){en=es()}return en}const eo={debug:console.debug,error:console.error,warn:console.warn};const ea=(q=r.createContext)===null||q===void 0?void 0:q.call(o,undefined);function ei(e){if(!ea){throw new Error("React Context is unavailable in Server Components")}const t=React.useContext(ea);if(!t&&"production"!=="production"){}const{required:n,onUnauthenticated:s}=e!==null&&e!==void 0?e:{};const r=n&&t.status==="unauthenticated";React.useEffect(()=>{if(r){const e="".concat(et.basePath,"/signin?").concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));if(s)s();else window.location.href=e}},[r,s]);if(r){return{data:t.data,update:t.update,status:"loading"}}return t}async function ec(e){const t=await $("session",et,eo,e);var n;if((n=e===null||e===void 0?void 0:e.broadcast)!==null&&n!==void 0?n:true){es().postMessage({event:"session",data:{trigger:"getSession"}})}return t}async function el(){const e=await $("csrf",et,eo);var t;return(t=e===null||e===void 0?void 0:e.csrfToken)!==null&&t!==void 0?t:""}async function ed(){return fetchData("providers",et,eo)}async function eu(e,t,n){const{callbackUrl:s,...r}=t!==null&&t!==void 0?t:{};const{redirect:o=true,redirectTo:a=s!==null&&s!==void 0?s:window.location.href,...i}=r;const c=apiBaseUrl(et);const l=await ed();if(!l){const e="".concat(c,"/error");window.location.href=e;return}if(!e||!l[e]){const e="".concat(c,"/signin?").concat(new URLSearchParams({callbackUrl:a}));window.location.href=e;return}const d=l[e].type;if(d==="webauthn"){throw new TypeError(['Provider id "'.concat(e,'" refers to a WebAuthn provider.'),'Please use `import { signIn } from "next-auth/webauthn"` instead.'].join("\n"))}const u="".concat(c,"/").concat(d==="credentials"?"callback":"signin","/").concat(e);const f=await el();const h=await fetch("".concat(u,"?").concat(new URLSearchParams(n)),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...i,csrfToken:f,callbackUrl:a})});const v=await h.json();if(o){var m;const e=(m=v.url)!==null&&m!==void 0?m:a;window.location.href=e;if(e.includes("#"))window.location.reload();return}var p;const y=(p=new URL(v.url).searchParams.get("error"))!==null&&p!==void 0?p:undefined;var g;const w=(g=new URL(v.url).searchParams.get("code"))!==null&&g!==void 0?g:undefined;if(h.ok){await et._getSession({event:"storage"})}return{error:y,code:w,status:h.status,ok:h.ok,url:y?null:v.url}}async function ef(e){var t;const{redirect:n=true,redirectTo:s=(t=e===null||e===void 0?void 0:e.callbackUrl)!==null&&t!==void 0?t:window.location.href}=e!==null&&e!==void 0?e:{};const r=apiBaseUrl(et);const o=await el();const a=await fetch("".concat(r,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:o,callbackUrl:s})});const i=await a.json();er().postMessage({event:"session",data:{trigger:"signout"}});if(n){var c;const e=(c=i.url)!==null&&c!==void 0?c:s;window.location.href=e;if(e.includes("#"))window.location.reload();return}await et._getSession({event:"storage"});return i}function eh(e){if(!ea){throw new Error("React Context is unavailable in Server Components")}const{children:t,basePath:n,refetchInterval:o,refetchWhenOffline:a}=e;if(n)et.basePath=n;const i=e.session!==undefined;et._lastSync=i?K():0;const[c,l]=r.useState(()=>{if(i)et._session=e.session;return e.session});const[d,u]=r.useState(!i);r.useEffect(()=>{et._getSession=async function(){let{event:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};try{const t=e==="storage";if(t||et._session===undefined){et._lastSync=K();et._session=await ec({broadcast:!t});l(et._session);return}if(!e||et._session===null||K()<et._lastSync){return}et._lastSync=K();et._session=await ec();l(et._session)}catch(e){eo.error(new D(e.message,e))}finally{u(false)}};et._getSession();return()=>{et._lastSync=0;et._session=undefined;et._getSession=()=>{}}},[]);r.useEffect(()=>{const e=()=>et._getSession({event:"storage"});er().addEventListener("message",e);return()=>er().removeEventListener("message",e)},[]);r.useEffect(()=>{const{refetchOnWindowFocus:t=true}=e;const n=()=>{if(t&&document.visibilityState==="visible")et._getSession({event:"visibilitychange"})};document.addEventListener("visibilitychange",n,false);return()=>document.removeEventListener("visibilitychange",n,false)},[e.refetchOnWindowFocus]);const f=J();const h=a!==false||f;r.useEffect(()=>{if(o&&h){const e=setInterval(()=>{if(et._session){et._getSession({event:"poll"})}},o*1e3);return()=>clearInterval(e)}},[o,h]);const v=r.useMemo(()=>({data:c,status:d?"loading":c?"authenticated":"unauthenticated",async update(e){if(d)return;u(true);const t=await $("session",et,eo,typeof e==="undefined"?undefined:{body:{csrfToken:await el(),data:e}});u(false);if(t){l(t);er().postMessage({event:"session",data:{trigger:"getSession"}})}return t}}),[c,d]);return(0,s.jsx)(ea.Provider,{value:v,children:t})}},5851:(e,t,n)=>{"use strict";n.d(t,{Toaster:()=>a});var s=n(5155);var r=n(1362);var o=n(6671);const a=e=>{let{...t}=e;const{theme:n="system"}=(0,r.D)();return(0,s.jsx)(o.l$,{theme:n,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}},8322:(e,t,n)=>{Promise.resolve().then(n.bind(n,5851));Promise.resolve().then(n.bind(n,8603));Promise.resolve().then(n.bind(n,5493));Promise.resolve().then(n.t.bind(n,9623,23));Promise.resolve().then(n.t.bind(n,4993,23));Promise.resolve().then(n.t.bind(n,9324,23))},8603:(e,t,n)=>{"use strict";n.d(t,{"default":()=>i});var s=n(5155);var r=n(2115);var o=n(1362);const a=e=>{let{children:t,...n}=e;return(0,s.jsx)(o.N,{...n,children:t})};const i=a},9324:()=>{},9623:e=>{e.exports={"style":{"fontFamily":"'Poppins', 'Poppins Fallback', Helvetica, Arial, sans-serif","fontStyle":"normal"},"className":"__className_9b9fd1","variable":"__variable_9b9fd1"}}},e=>{var t=t=>e(e.s=t);e.O(0,[399,671,441,684,358],()=>t(8322));var n=e.O();_N_E=n}]);