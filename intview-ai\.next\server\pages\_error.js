"use strict";(()=>{var e={};e.id=731;e.ids=[731,839];e.modules={237:(e,t)=>{var n;n={value:true};Object.defineProperty(t,"A",{enumerable:true,get:function(){return r}});var r=function(e){e["PAGES"]="PAGES";e["PAGES_API"]="PAGES_API";e["APP_PAGE"]="APP_PAGE";e["APP_ROUTE"]="APP_ROUTE";e["IMAGE"]="IMAGE";return e}({})},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},413:(e,t,n)=>{n.r(t);n.d(t,{config:()=>b,"default":()=>p,getServerSideProps:()=>y,getStaticPaths:()=>g,getStaticProps:()=>h,reportWebVitals:()=>m,routeModule:()=>M,unstable_getServerProps:()=>x,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>v});var r=n(3885);var o=n.n(r);var s=n(237);var i=n(1413);var a=n(8548);var u=n.n(a);var l=n(9380);var c=n.n(l);var d=n(6631);var f=n.n(d);const p=(0,i.M)(d,"default");const h=(0,i.M)(d,"getStaticProps");const g=(0,i.M)(d,"getStaticPaths");const y=(0,i.M)(d,"getServerSideProps");const b=(0,i.M)(d,"config");const m=(0,i.M)(d,"reportWebVitals");const v=(0,i.M)(d,"unstable_getStaticProps");const _=(0,i.M)(d,"unstable_getStaticPaths");const P=(0,i.M)(d,"unstable_getStaticParams");const x=(0,i.M)(d,"unstable_getServerProps");const j=(0,i.M)(d,"unstable_getServerSideProps");const M=new r.PagesRouteModule({definition:{kind:s.A.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:c(),Document:u()},userland:d})},1413:(e,t)=>{var n;n={value:true};Object.defineProperty(t,"M",{enumerable:true,get:function(){return r}});function r(e,t){if(t in e){return e[t]}if("then"in e&&typeof e.then==="function"){return e.then(e=>r(e,t))}if(typeof e==="function"&&t==="default"){return e}return undefined}},1523:(e,t,n)=>{e.exports=n(3885).vendored.contexts.HeadManagerContext},2015:e=>{e.exports=require("react")},3147:(e,t)=>{function n(e){if(typeof WeakMap!=="function")return null;var t=new WeakMap;var r=new WeakMap;return(n=function(e){return e?r:t})(e)}function r(e,t){if(!t&&e&&e.__esModule)return e;if(e===null||typeof e!=="object"&&typeof e!=="function")return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var o={__proto__:null};var s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e){if(i!=="default"&&Object.prototype.hasOwnProperty.call(e,i)){var a=s?Object.getOwnPropertyDescriptor(e,i):null;if(a&&(a.get||a.set))Object.defineProperty(o,i,a);else o[i]=e[i]}}o.default=e;if(r)r.set(e,o);return o}t._=r},3873:e=>{e.exports=require("path")},3901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"warnOnce",{enumerable:true,get:function(){return n}});let n=e=>{};if(false){}},5124:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function n(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}n(t,{NEXT_REQUEST_META:function(){return r},addRequestMeta:function(){return i},getRequestMeta:function(){return o},removeRequestMeta:function(){return a},setRequestMeta:function(){return s}});const r=Symbol.for("NextInternalRequestMeta");function o(e,t){const n=e[r]||{};return typeof t==="string"?n[t]:n}function s(e,t){e[r]=t;return t}function i(e,t,n){const r=o(e);r[t]=n;return s(e,r)}function a(e,t){const n=o(e);delete n[t];return s(e,n)}},5996:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return a}});const r=n(2015);const o="undefined"==="undefined";const s=o?()=>{}:r.useLayoutEffect;const i=o?()=>{}:r.useEffect;function a(e){const{headManager:t,reduceComponentsToState:n}=e;function a(){if(t&&t.mountedInstances){const o=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(o,e))}}if(o){var u;t==null?void 0:(u=t.mountedInstances)==null?void 0:u.add(e.children);a()}s(()=>{var n;t==null?void 0:(n=t.mountedInstances)==null?void 0:n.add(e.children);return()=>{var n;t==null?void 0:(n=t.mountedInstances)==null?void 0:n.delete(e.children)}});s(()=>{if(t){t._pendingUpdate=a}return()=>{if(t){t._pendingUpdate=a}}});i(()=>{if(t&&t._pendingUpdate){t._pendingUpdate();t._pendingUpdate=null}return()=>{if(t&&t._pendingUpdate){t._pendingUpdate();t._pendingUpdate=null}}});return null}},6631:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return c}});const r=n(7020);const o=n(8732);const s=r._(n(2015));const i=r._(n(9722));const a={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function u(e){let{req:t,res:r,err:o}=e;const s=r&&r.statusCode?r.statusCode:o?o.statusCode:404;let i;if(false){}else if(t){const{getRequestMeta:e}=n(5124);const r=e(t,"initURL");if(r){const e=new URL(r);i=e.hostname}}return{statusCode:s,hostname:i}}const l={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class c extends s.default.Component{render(){const{statusCode:e,withDarkMode:t=true}=this.props;const n=this.props.title||a[e]||"An unexpected error has occurred";return(0,o.jsxs)("div",{style:l.error,children:[(0,o.jsx)(i.default,{children:(0,o.jsx)("title",{children:e?e+": "+n:"Application error: a client-side exception has occurred"})}),(0,o.jsxs)("div",{style:l.desc,children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,o.jsx)("h1",{className:"next-error-h1",style:l.h1,children:e}):null,(0,o.jsx)("div",{style:l.wrap,children:(0,o.jsxs)("h2",{style:l.h2,children:[this.props.title||e?n:(0,o.jsxs)(o.Fragment,{children:["Application error: a client-side exception has occurred"," ",Boolean(this.props.hostname)&&(0,o.jsxs)(o.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}c.displayName="ErrorPage";c.getInitialProps=u;c.origGetInitialProps=u;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},7043:(e,t,n)=>{e.exports=n(3885).vendored.contexts.AmpContext},8725:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"isInAmpMode",{enumerable:true,get:function(){return n}});function n(e){let{ampFirst:t=false,hybrid:n=false,hasQuery:r=false}=e===void 0?{}:e;return t||n&&r}},8732:e=>{e.exports=require("react/jsx-runtime")},9380:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return u}});const r=n(7020);const o=n(8732);const s=r._(n(2015));const i=n(6370);async function a(e){let{Component:t,ctx:n}=e;const r=await (0,i.loadGetInitialProps)(t,n);return{pageProps:r}}class u extends s.default.Component{render(){const{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}u.origGetInitialProps=a;u.getInitialProps=a;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},9722:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function r(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}r(t,{default:function(){return v},defaultHead:function(){return p}});const o=n(7020);const s=n(3147);const i=n(8732);const a=s._(n(2015));const u=o._(n(5996));const l=n(7043);const c=n(1523);const d=n(8725);const f=n(3901);function p(e){if(e===void 0)e=false;const t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];if(!e){t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport"))}return t}function h(e,t){if(typeof t==="string"||typeof t==="number"){return e}if(t.type===a.default.Fragment){return e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>{if(typeof t==="string"||typeof t==="number"){return e}return e.concat(t)},[]))}return e.concat(t)}const g=["name","httpEquiv","charSet","itemProp"];function y(){const e=new Set;const t=new Set;const n=new Set;const r={};return o=>{let s=true;let i=false;if(o.key&&typeof o.key!=="number"&&o.key.indexOf("$")>0){i=true;const t=o.key.slice(o.key.indexOf("$")+1);if(e.has(t)){s=false}else{e.add(t)}}switch(o.type){case"title":case"base":if(t.has(o.type)){s=false}else{t.add(o.type)}break;case"meta":for(let e=0,t=g.length;e<t;e++){const t=g[e];if(!o.props.hasOwnProperty(t))continue;if(t==="charSet"){if(n.has(t)){s=false}else{n.add(t)}}else{const e=o.props[t];const n=r[t]||new Set;if((t!=="name"||!i)&&n.has(e)){s=false}else{n.add(e);r[t]=n}}}break}return s}}function b(e,t){const{inAmpMode:n}=t;return e.reduce(h,[]).reverse().concat(p(n).reverse()).filter(y()).reverse().map((e,t)=>{const r=e.key||t;if(true&&process.env.__NEXT_OPTIMIZE_FONTS&&!n){if(e.type==="link"&&e.props["href"]&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props["href"].startsWith(t))){const t={...e.props||{}};t["data-href"]=t["href"];t["href"]=undefined;t["data-optimized-fonts"]=true;return a.default.cloneElement(e,t)}}if(false){}return a.default.cloneElement(e,{key:r})})}function m(e){let{children:t}=e;const n=(0,a.useContext)(l.AmpStateContext);const r=(0,a.useContext)(c.HeadManagerContext);return(0,i.jsx)(u.default,{reduceComponentsToState:b,headManager:r,inAmpMode:(0,d.isInAmpMode)(n),children:t})}const v=m;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e);var r=t.X(0,[548],()=>n(413));module.exports=r})();