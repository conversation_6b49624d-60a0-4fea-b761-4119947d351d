"use strict";(()=>{var e={};e.id=636;e.ids=[636];e.modules={2015:e=>{e.exports=require("react")},6370:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function n(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}n(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return P},NormalizeError:function(){return E},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return g},WEB_VITALS:function(){return r},execOnce:function(){return o},getDisplayName:function(){return c},getLocationOrigin:function(){return i},getURL:function(){return a},isAbsoluteUrl:function(){return u},isResSent:function(){return f},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return _}});const r=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t=false;let n;return function(){for(var r=arguments.length,o=new Array(r),s=0;s<r;s++){o[s]=arguments[s]}if(!t){t=true;n=e(...o)}return n}}const s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/;const u=e=>s.test(e);function i(){const{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){const{href:e}=window.location;const t=i();return e.substring(t.length)}function c(e){return typeof e==="string"?e:e.displayName||e.name||"Unknown"}function f(e){return e.finished||e.headersSent}function l(e){const t=e.split("?");const n=t[0];return n.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){if(false){var n}const r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps){if(t.ctx&&t.Component){return{pageProps:await d(t.Component,t.ctx)}}return{}}const o=await e.getInitialProps(t);if(r&&f(r)){return o}if(!o){const t='"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.';throw Object.defineProperty(new Error(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}if(false){}return o}const p=typeof performance!=="undefined";const g=p&&["mark","measure","getEntriesByName"].every(e=>typeof performance[e]==="function");class m extends Error{}class E extends Error{}class y extends Error{constructor(e){super();this.code="ENOENT";this.name="PageNotFoundError";this.message="Cannot find module for page: "+e}}class P extends Error{constructor(e,t){super();this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super();this.code="ENOENT";this.message="Cannot find the middleware module"}}function _(e){return JSON.stringify({message:e.message,stack:e.stack})}},7020:(e,t)=>{function n(e){return e&&e.__esModule?e:{default:e}}t._=n},8732:e=>{e.exports=require("react/jsx-runtime")},9380:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return a}});const r=n(7020);const o=n(8732);const s=r._(n(2015));const u=n(6370);async function i(e){let{Component:t,ctx:n}=e;const r=await (0,u.loadGetInitialProps)(t,n);return{pageProps:r}}class a extends s.default.Component{render(){const{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}a.origGetInitialProps=i;a.getInitialProps=i;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e);var r=n(9380);module.exports=r})();