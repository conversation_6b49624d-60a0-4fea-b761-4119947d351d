exports.id=762;exports.ids=[762];exports.modules={25:(e,t,s)=>{"use strict";s.d(t,{"default":()=>n});var r=s(2907);var a=s.n(r);const n=(0,r.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>n});var r=s(2907);var a=s.n(r);const n=(0,r.registerClientReference)(function(){throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},507:(e,t,s)=>{"use strict";s.r(t);s.d(t,{"default":()=>S});var r=s(687);var a=s(3210);var n=s(2688);const o=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]];const l=(0,n.A)("menu",o);const i=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M13.916 2.314A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.74 7.327A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673 9 9 0 0 1-.585-.665",key:"1tip0g"}],["circle",{cx:"18",cy:"8",r:"3",key:"1g0gzu"}]];const c=(0,n.A)("bell-dot",i);const d=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]];const m=(0,n.A)("globe",d);const h=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]];const x=(0,n.A)("chevron-down",h);const p=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]];const u=(0,n.A)("x",p);const f=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]];const v=(0,n.A)("circle-user",f);const b=({onToggleSidebar:e})=>{const[t,s]=(0,a.useState)(false);const n=()=>{s(!t)};return(0,r.jsxs)("header",{className:"border-b bg-white px-4 sm:px-6 py-4 sm:py-5 shrink-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("button",{onClick:e,className:"lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"Toggle sidebar",children:(0,r.jsx)(l,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsx)("div",{className:"text-lg sm:text-xl font-semibold text-gray-900",children:"AI Interview"})]}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center gap-4 xl:gap-6",children:[(0,r.jsx)(c,{className:"h-6 w-6 sm:h-8 sm:w-7 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-2 sm:py-4 px-4 sm:px-6 rounded-full transition-colors",children:[(0,r.jsx)(m,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"font-bold hidden sm:inline",children:"English"}),(0,r.jsx)(x,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-4 sm:px-8 py-2 sm:py-3 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-gray-500 p-1"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xs sm:text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]}),(0,r.jsx)(x,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex lg:hidden items-center gap-3",children:[(0,r.jsx)(c,{className:"h-6 w-6 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 cursor-pointer bg-gray-50 rounded-full px-3 py-2 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs font-medium text-gray-500",children:"H"})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad"}),(0,r.jsx)(x,{className:"h-4 w-4 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsx)("div",{className:"sm:hidden",children:(0,r.jsx)("button",{onClick:n,className:"p-2",children:t?(0,r.jsx)(u,{className:"h-6 w-6"}):(0,r.jsx)(v,{className:"h-6 w-6 text-gray-700"})})})]}),t&&(0,r.jsxs)("div",{className:"mt-4 pb-4 flex flex-col gap-4 sm:hidden border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"H"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(c,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(m,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"English"})]})]})]})};const g=b;var y=s(474);var j=s(5814);var w=s.n(j);var N=s(6189);const k={"src":"/_next/static/media/logo-light.a0bdc026.svg","height":62,"width":177,"blurWidth":0,"blurHeight":0};const A=[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]];const P=(0,n.A)("layout-dashboard",A);var C=s(659);const E=[{label:"Dashboard",href:"/",icon:P},{label:"Job Posts",href:"/interview",icon:C.A}];const M=({isOpen:e,onClose:t})=>{const s=(0,N.usePathname)();const n=(0,a.useRef)(s);(0,a.useEffect)(()=>{if(n.current!==s&&e&&t){t()}n.current=s},[s,e,t]);(0,a.useEffect)(()=>{const s=e=>{const s=document.getElementById("mobile-sidebar");const r=document.getElementById("sidebar-overlay");if(s&&!s.contains(e.target)&&r?.contains(e.target)){if(t)t()}};if(e){document.addEventListener("mousedown",s);document.body.style.overflow="hidden"}else{document.body.style.overflow="unset"}return()=>{document.removeEventListener("mousedown",s);document.body.style.overflow="unset"}},[e,t]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("aside",{className:"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-10",children:(0,r.jsx)(y["default"],{src:k,alt:"Logo"})}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:E.map(e=>{const t=s===e.href;const a=e.icon;return(0,r.jsxs)(w(),{href:e.href,className:`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                  ${t?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"}`,children:[(0,r.jsx)(a,{className:`w-5 h-5 ${t?"text-purple-700":"text-gray-400"}`}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]}),(0,r.jsx)("div",{id:"sidebar-overlay",className:`fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ${e?"opacity-100":"opacity-0 pointer-events-none"}`,children:(0,r.jsxs)("aside",{id:"mobile-sidebar",className:`fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-10",children:[(0,r.jsx)(y["default"],{src:k,alt:"Logo"}),(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(u,{className:"h-5 w-5 text-gray-500"})})]}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:E.map(e=>{const a=s===e.href;const n=e.icon;return(0,r.jsxs)(w(),{href:e.href,className:`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                      ${a?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"}`,onClick:t,children:[(0,r.jsx)(n,{className:`w-5 h-5 ${a?"text-purple-700":"text-gray-400"}`}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]})})]})};const T=M;const $=({children:e})=>{const[t,s]=(0,a.useState)(false);const n=(0,a.useCallback)(()=>{s(e=>!e)},[]);const o=(0,a.useCallback)(()=>{s(false)},[]);return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(T,{isOpen:t,onClose:o}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden min-w-0",children:[(0,r.jsx)(g,{onToggleSidebar:n}),(0,r.jsx)("main",{className:"flex-1 overflow-auto p-4 sm:p-6",children:e})]})]})};const S=$},659:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(2688);const a=[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]];const n=(0,r.A)("briefcase-business",a)},1279:(e,t,s)=>{"use strict";s.d(t,{"default":()=>c});var r=s(687);var a=s.n(r);var n=s(3210);var o=s.n(n);var l=s(218);const i=({children:e,...t})=>{return(0,r.jsx)(l.N,{...t,children:e})};const c=i},2528:(e,t,s)=>{"use strict";s.r(t);s.d(t,{"default":()=>n});var r=s(2907);var a=s.n(r);const n=(0,r.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(root)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx","default")},2569:(e,t,s)=>{Promise.resolve().then(s.bind(s,2528))},2688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(3210);const a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();const n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase());const o=e=>{const t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)};const l=(...e)=>e.filter((e,t,s)=>{return Boolean(e)&&e.trim()!==""&&s.indexOf(e)===t}).join(" ").trim();const i=e=>{for(const t in e){if(t.startsWith("aria-")||t==="role"||t==="title"){return true}}};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:n="",children:o,iconNode:d,...m},h)=>(0,r.createElement)("svg",{ref:h,...c,width:t,height:t,stroke:e,strokeWidth:a?Number(s)*24/Number(t):s,className:l("lucide",n),...!o&&!i(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]]));const m=(e,t)=>{const s=(0,r.forwardRef)(({className:s,...n},i)=>(0,r.createElement)(d,{ref:i,iconNode:t,className:l(`lucide-${a(o(e))}`,`lucide-${e}`,s),...n}));s.displayName=o(e);return s}},2704:()=>{},2910:(e,t,s)=>{Promise.resolve().then(s.bind(s,363));Promise.resolve().then(s.bind(s,25));Promise.resolve().then(s.bind(s,2175))},4013:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23));Promise.resolve().then(s.t.bind(s,7924,23));Promise.resolve().then(s.t.bind(s,5656,23));Promise.resolve().then(s.t.bind(s,99,23));Promise.resolve().then(s.t.bind(s,8243,23));Promise.resolve().then(s.t.bind(s,8827,23));Promise.resolve().then(s.t.bind(s,2763,23));Promise.resolve().then(s.t.bind(s,7173,23))},4593:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>l});var r=s(687);var a=s.n(r);var n=s(218);var o=s(2581);const l=({...e})=>{const{theme:t="system"}=(0,n.D)();return(0,r.jsx)(o.l$,{theme:t,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},5958:(e,t,s)=>{Promise.resolve().then(s.bind(s,4593));Promise.resolve().then(s.bind(s,1279));Promise.resolve().then(s.bind(s,9208))},6749:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23));Promise.resolve().then(s.t.bind(s,6042,23));Promise.resolve().then(s.t.bind(s,8170,23));Promise.resolve().then(s.t.bind(s,9477,23));Promise.resolve().then(s.t.bind(s,9345,23));Promise.resolve().then(s.t.bind(s,2089,23));Promise.resolve().then(s.t.bind(s,6577,23));Promise.resolve().then(s.t.bind(s,1307,23))},6814:(e,t,s)=>{"use strict";s.d(t,{Y9:()=>o,j2:()=>c});var r=s(9859);var a=s(3560);var n=s(6056);const{handlers:o,signIn:l,signOut:i,auth:c}=(0,r.Ay)({providers:[a.A,n.A],secret:process.env.AUTH_SECRET})},8014:(e,t,s)=>{"use strict";s.r(t);s.d(t,{"default":()=>v,metadata:()=>u});var r=s(7413);var a=s.n(r);var n=s(363);var o=s(6649);var l=s.n(o);var i=s(5843);var c=s.n(i);var d=s(2704);var m=s.n(d);var h=s(25);var x=s(2175);var p=s(6814);const u={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}};const f=async({children:e})=>{const t=await (0,p.j2)();return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:true,children:(0,r.jsx)(x.SessionProvider,{session:t,children:(0,r.jsxs)("body",{className:`${l().className} ${c().variable} antialiased`,children:[(0,r.jsx)(h["default"],{attribute:"class",defaultTheme:"light",children:e}),(0,r.jsx)(n.Toaster,{})]})})})};const v=f},9017:(e,t,s)=>{Promise.resolve().then(s.bind(s,507))}};