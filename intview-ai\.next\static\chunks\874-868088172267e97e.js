"use strict";(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[874],{2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"isLocalURL",{enumerable:true,get:function(){return u}});const r=n(9991);const o=n(7102);function u(e){if(!(0,r.isAbsoluteUrl)(e))return true;try{const t=(0,r.getLocationOrigin)();const n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return false}}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function r(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}r(t,{formatUrl:function(){return i},formatWithValidation:function(){return c},urlObjectKeys:function(){return f}});const o=n(6966);const u=o._(n(8859));const s=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:n}=e;let r=e.protocol||"";let o=e.pathname||"";let i=e.hash||"";let f=e.query||"";let c=false;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"";if(e.host){c=t+e.host}else if(n){c=t+(~n.indexOf(":")?"["+n+"]":n);if(e.port){c+=":"+e.port}}if(f&&typeof f==="object"){f=String(u.urlQueryToSearchParams(f))}let a=e.search||f&&"?"+f||"";if(r&&!r.endsWith(":"))r+=":";if(e.slashes||(!r||s.test(r))&&c!==false){c="//"+(c||"");if(o&&o[0]!=="/")o="/"+o}else if(!c){c=""}if(i&&i[0]!=="#")i="#"+i;if(a&&a[0]!=="?")a="?"+a;o=o.replace(/[?#]/g,encodeURIComponent);a=a.replace("#","%23");return""+r+c+o+a+i}const f=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function c(e){if(false){}return i(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"errorOnce",{enumerable:true,get:function(){return n}});let n=e=>{};if(false){}},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function r(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}r(t,{default:function(){return v},useLinkStatus:function(){return O}});const o=n(6966);const u=n(5155);const s=o._(n(2115));const i=n(2757);const f=n(5227);const c=n(9818);const a=n(6654);const l=n(9991);const p=n(5929);const d=n(3230);const h=n(4930);const y=n(2664);const g=n(6634);const m=n(3180);function b(e){const t=e.currentTarget;const n=t.getAttribute("target");return n&&n!=="_self"||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&e.nativeEvent.which===2}function P(e,t,n,r,o,u,i){const{nodeName:f}=e.currentTarget;const c=f.toUpperCase()==="A";if(c&&b(e)||e.currentTarget.hasAttribute("download")){return}if(!(0,y.isLocalURL)(t)){if(o){e.preventDefault();location.replace(t)}return}e.preventDefault();const a=()=>{if(i){let e=false;i({preventDefault:()=>{e=true}});if(e){return}}(0,g.dispatchNavigateAction)(n||t,o?"replace":"push",u!=null?u:true,r.current)};s.default.startTransition(a)}function E(e){if(typeof e==="string"){return e}return(0,i.formatUrl)(e)}function v(e){const[t,n]=(0,s.useOptimistic)(h.IDLE_LINK_STATUS);let r;const o=(0,s.useRef)(null);const{href:i,as:d,children:y,prefetch:g=null,passHref:m,replace:b,shallow:v,scroll:O,onClick:j,onMouseEnter:T,onTouchStart:N,legacyBehavior:C=false,onNavigate:S,ref:A,unstable_dynamicOnHover:L,...w}=e;r=y;if(C&&(typeof r==="string"||typeof r==="number")){r=(0,u.jsx)("a",{children:r})}const x=s.default.useContext(f.AppRouterContext);const U=g!==false;const I=g===null?c.PrefetchKind.AUTO:c.PrefetchKind.FULL;if(false){}if(false){}const{href:k,as:M}=s.default.useMemo(()=>{const e=E(i);return{href:e,as:d?E(d):e}},[i,d]);let R;if(C){if(false){}else{R=s.default.Children.only(r)}}else{if(false){}}const D=C?R&&typeof R==="object"&&R.ref:A;const F=s.default.useCallback(e=>{if(x!==null){o.current=(0,h.mountLinkInstance)(e,k,x,I,U,n)}return()=>{if(o.current){(0,h.unmountLinkForCurrentNavigation)(o.current);o.current=null}(0,h.unmountPrefetchableInstance)(e)}},[U,k,x,I,n]);const K=(0,a.useMergedRef)(F,D);const B={ref:K,onClick(e){if(false){}if(!C&&typeof j==="function"){j(e)}if(C&&R.props&&typeof R.props.onClick==="function"){R.props.onClick(e)}if(!x){return}if(e.defaultPrevented){return}P(e,k,M,o,b,O,S)},onMouseEnter(e){if(!C&&typeof T==="function"){T(e)}if(C&&R.props&&typeof R.props.onMouseEnter==="function"){R.props.onMouseEnter(e)}if(!x){return}if(!U||"production"==="development"){return}const t=L===true;(0,h.onNavigationIntent)(e.currentTarget,t)},onTouchStart:false?0:function e(e){if(!C&&typeof N==="function"){N(e)}if(C&&R.props&&typeof R.props.onTouchStart==="function"){R.props.onTouchStart(e)}if(!x){return}if(!U){return}const t=L===true;(0,h.onNavigationIntent)(e.currentTarget,t)}};if((0,l.isAbsoluteUrl)(M)){B.href=M}else if(!C||m||R.type==="a"&&!("href"in R.props)){B.href=(0,p.addBasePath)(M)}let z;if(C){if(false){}z=s.default.cloneElement(R,B)}else{z=(0,u.jsx)("a",{...w,...B,children:r})}return(0,u.jsx)(_.Provider,{value:t,children:z})}const _=(0,s.createContext)(h.IDLE_LINK_STATUS);const O=()=>{return(0,s.useContext)(_)};if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},8859:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function n(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}n(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return u}});function r(e){const t={};for(const[n,r]of e.entries()){const e=t[n];if(typeof e==="undefined"){t[n]=r}else if(Array.isArray(e)){e.push(r)}else{t[n]=[e,r]}}return t}function o(e){if(typeof e==="string"){return e}if(typeof e==="number"&&!isNaN(e)||typeof e==="boolean"){return String(e)}else{return""}}function u(e){const t=new URLSearchParams;for(const[n,r]of Object.entries(e)){if(Array.isArray(r)){for(const e of r){t.append(n,o(e))}}else{t.set(n,o(r))}}return t}function s(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++){n[r-1]=arguments[r]}for(const t of n){for(const n of t.keys()){e.delete(n)}for(const[n,r]of t.entries()){e.append(n,r)}}return e}},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function n(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}n(t,{DecodeError:function(){return y},MiddlewareNotFoundError:function(){return P},MissingStaticPage:function(){return b},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return d},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return o},getDisplayName:function(){return c},getLocationOrigin:function(){return i},getURL:function(){return f},isAbsoluteUrl:function(){return s},isResSent:function(){return a},loadGetInitialProps:function(){return p},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return E}});const r=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t=false;let n;return function(){for(var r=arguments.length,o=new Array(r),u=0;u<r;u++){o[u]=arguments[u]}if(!t){t=true;n=e(...o)}return n}}const u=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/;const s=e=>u.test(e);function i(){const{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function f(){const{href:e}=window.location;const t=i();return e.substring(t.length)}function c(e){return typeof e==="string"?e:e.displayName||e.name||"Unknown"}function a(e){return e.finished||e.headersSent}function l(e){const t=e.split("?");const n=t[0];return n.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function p(e,t){if(false){var n}const r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps){if(t.ctx&&t.Component){return{pageProps:await p(t.Component,t.ctx)}}return{}}const o=await e.getInitialProps(t);if(r&&a(r)){return o}if(!o){const t='"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.';throw Object.defineProperty(new Error(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}if(false){}return o}const d=typeof performance!=="undefined";const h=d&&["mark","measure","getEntriesByName"].every(e=>typeof performance[e]==="function");class y extends Error{}class g extends Error{}class m extends Error{constructor(e){super();this.code="ENOENT";this.name="PageNotFoundError";this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super();this.message="Failed to load static file for page: "+e+" "+t}}class P extends Error{constructor(){super();this.code="ENOENT";this.message="Cannot find the middleware module"}}function E(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);