"use strict";(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[905],{3582:(e,r,s)=>{s.d(r,{Ih:()=>o,Il:()=>a,mJ:()=>n,pi:()=>d,zL:()=>i});var t=s(8309);const a=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const n=t.Ik({confirmPassword:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:t.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const o=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})});const i=t.Ik({newPassword:t.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]});const d=t.Ik({otp1:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})},3999:(e,r,s)=>{s.d(r,{cn:()=>n});var t=s(2596);var a=s(9688);function n(){for(var e=arguments.length,r=new Array(e),s=0;s<e;s++){r[s]=arguments[s]}return(0,a.QP)((0,t.$)(r))}},5318:(e,r,s)=>{s.d(r,{A:()=>p});var t=s(5155);var a=s(2115);var n=s(2177);var o=s(221);var i=s(5695);var d=s(6766);var c=s(9540);var l=s(9852);var u=s(7168);const m=e=>{let{mode:r,schema:s,defaultValues:m,onSubmit:p}=e;const g=(0,i.useRouter)();const x=(0,n.mN)({resolver:(0,o.u)(s),defaultValues:m,mode:"onChange"});const f=(0,a.useRef)(null);const v=(0,a.useRef)(null);const h=(0,a.useRef)(null);const w=(0,a.useRef)(null);const b=[f,v,h,w];const[j,P]=(0,a.useState)(false);const y=async e=>{const s=await p(e);if(s.success){if(r==="forgot"){g.push("/sign-in/Forgotpassword/verifyotp")}else if(r==="otp"){g.push("/sign-in/Forgotpassword/verifyotp/Setnewpassword")}else if(r==="reset"){g.push("/sign-in")}}};const N=(e,r)=>{const s=r.target.value;x.setValue("otp".concat(e+1),s);if(s&&e<b.length-1){var t;(t=b[e+1].current)===null||t===void 0?void 0:t.focus()}};return(0,t.jsxs)("div",{className:"w-full px-4 py-6 lg:max-w-md lg:min-w-[28rem]",children:[(0,t.jsx)("h1",{className:"text-[28px] lg:text-[34px] leading-[36px] lg:leading-[41px] font-semibold font-poppins text-[#100F14] mb-2",children:r==="forgot"?"Forget Password":r==="reset"?"Reset Password":"Verification Code"}),(0,t.jsx)("p",{className:"text-gray-600 font-medium mb-8 text-poppins",children:r==="forgot"?"Enter your email id to request a password reset.":r==="reset"?"Please create a new password":"Enter OTP sent to your email for the verification process."}),(0,t.jsx)(c.lV,{...x,children:(0,t.jsxs)("form",{onSubmit:x.handleSubmit(y),className:"space-y-6",children:[r==="otp"?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("h4",{className:"text-lg font-medium mb-3",children:"OTP"}),(0,t.jsx)("div",{className:"flex gap-4 justify-between",children:b.map((e,r)=>(0,t.jsx)(c.zB,{control:x.control,name:"otp".concat(r+1),render:e=>{let{field:s}=e;const{ref:a,onChange:n,...o}=s;return(0,t.jsxs)(c.eI,{className:"flex-1",children:[(0,t.jsx)(c.MJ,{children:(0,t.jsx)(l.p,{ref:e=>{a(e);b[r].current=e},maxLength:1,className:"text-center font-bold text-3xl py-8 sm:py-7",type:"text",inputMode:"numeric",pattern:"[0-9]*",onChange:e=>{n(e);N(r,e)},...o})}),(0,t.jsx)(c.C5,{})]})}},r))})]}):Object.keys(m).map(e=>(0,t.jsx)(c.zB,{control:x.control,name:e,render:r=>{let{field:s}=r;return(0,t.jsxs)(c.eI,{children:[(0,t.jsx)(c.lR,{className:"text-[#2E2E2E]",children:e==="email"?"Email Address":e==="password"||e==="newPassword"?"Password":"Confirm Password"}),(0,t.jsx)(c.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(l.p,{type:e.toLowerCase().includes("password")&&!j?"password":"text",placeholder:e==="email"?"Enter Your Email address":e==="password"||e==="newPassword"?"Enter Your Password":"Confirm Your Password",className:"py-5 sm:py-6 pr-12",...s}),e.toLowerCase().includes("password")&&(0,t.jsx)("button",{type:"button",onClick:()=>P(e=>!e),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,t.jsx)(d["default"],{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,t.jsx)(c.C5,{})]})}},e)),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(u.$,{type:"submit",className:"lg:w-[360px] w-full  bg-[#6938EF] hover:bg-[#682ed6] hover:cursor-pointer text-white py-5 sm:py-6 px-4 rounded-full mt-5",disabled:x.formState.isSubmitting||!x.formState.isValid,children:x.formState.isSubmitting?r==="forgot"?"Sending...":r==="reset"?"Resetting...":"Verifying...":r==="forgot"?"Continue":r==="reset"?"Reset Password":"Continue"})})]})})]})};const p=m},7168:(e,r,s)=>{s.d(r,{$:()=>c});var t=s(5155);var a=s(2115);var n=s(4624);var o=s(2085);var i=s(3999);const d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:r,variant:s,size:a,asChild:o=false,...c}=e;const l=o?n.DX:"button";return(0,t.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:a,className:r})),...c})}},9540:(e,r,s)=>{s.d(r,{lV:()=>l,MJ:()=>v,zB:()=>m,eI:()=>x,lR:()=>f,C5:()=>w});var t=s(5155);var a=s(2115);var n=s(4624);var o=s(2177);var i=s(3999);var d=s(7073);function c(e){let{className:r,...s}=e;return(0,t.jsx)(d.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...s})};const l=o.Op;const u=a.createContext({});const m=e=>{let{...r}=e;return(0,t.jsx)(u.Provider,{value:{name:r.name},children:(0,t.jsx)(o.xI,{...r})})};const p=()=>{const e=a.useContext(u);const r=a.useContext(g);const{getFieldState:s}=(0,o.xW)();const t=(0,o.lN)({name:e.name});const n=s(e.name,t);if(!e){throw new Error("useFormField should be used within <FormField>")}const{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}};const g=a.createContext({});function x(e){let{className:r,...s}=e;const n=a.useId();return(0,t.jsx)(g.Provider,{value:{id:n},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",r),...s})})}function f(e){let{className:r,...s}=e;const{error:a,formItemId:n}=p();return(0,t.jsx)(c,{"data-slot":"form-label","data-error":!!a,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...s})}function v(e){let{...r}=e;const{error:s,formItemId:a,formDescriptionId:o,formMessageId:i}=p();return(0,t.jsx)(n.DX,{"data-slot":"form-control",id:a,"aria-describedby":!s?"".concat(o):"".concat(o," ").concat(i),"aria-invalid":!!s,...r})}function h(e){let{className:r,...s}=e;const{formDescriptionId:t}=p();return _jsx("p",{"data-slot":"form-description",id:t,className:cn("text-muted-foreground text-sm",r),...s})}function w(e){let{className:r,...s}=e;const{error:a,formMessageId:n}=p();var o;const d=a?String((o=a===null||a===void 0?void 0:a.message)!==null&&o!==void 0?o:""):s.children;if(!d)return null;return(0,t.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-sm !text-red-600",r),...s,children:d})}},9852:(e,r,s)=>{s.d(r,{p:()=>o});var t=s(5155);var a=s(2115);var n=s(3999);function o(e){let{className:r,type:s,...a}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}}}]);