exports.id=597;exports.ids=[597];exports.modules={25:(e,r,t)=>{"use strict";t.d(r,{"default":()=>n});var s=t(2907);var a=t.n(s);const n=(0,s.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>n});var s=t(2907);var a=t.n(s);const n=(0,s.registerClientReference)(function(){throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},1279:(e,r,t)=>{"use strict";t.d(r,{"default":()=>l});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(218);const d=({children:e,...r})=>{return(0,s.jsx)(i.N,{...r,children:e})};const l=d},2704:()=>{},2910:(e,r,t)=>{Promise.resolve().then(t.bind(t,363));Promise.resolve().then(t.bind(t,25));Promise.resolve().then(t.bind(t,2175))},2925:(e,r,t)=>{"use strict";t.d(r,{lV:()=>c,MJ:()=>h,zB:()=>u,eI:()=>x,lR:()=>g,C5:()=>b});var s=t(687);var a=t(3210);var n=t(1391);var o=t(7605);var i=t(6241);var d=t(9467);function l({className:e,...r}){return(0,s.jsx)(d.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})};const c=o.Op;const m=a.createContext({});const u=({...e})=>{return(0,s.jsx)(m.Provider,{value:{name:e.name},children:(0,s.jsx)(o.xI,{...e})})};const v=()=>{const e=a.useContext(m);const r=a.useContext(p);const{getFieldState:t}=(0,o.xW)();const s=(0,o.lN)({name:e.name});const n=t(e.name,s);if(!e){throw new Error("useFormField should be used within <FormField>")}const{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}};const p=a.createContext({});function x({className:e,...r}){const t=a.useId();return(0,s.jsx)(p.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",e),...r})})}function g({className:e,...r}){const{error:t,formItemId:a}=v();return(0,s.jsx)(l,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function h({...e}){const{error:r,formItemId:t,formDescriptionId:a,formMessageId:o}=v();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:t,"aria-describedby":!r?`${a}`:`${a} ${o}`,"aria-invalid":!!r,...e})}function f({className:e,...r}){const{formDescriptionId:t}=v();return _jsx("p",{"data-slot":"form-description",id:t,className:cn("text-muted-foreground text-sm",e),...r})}function b({className:e,...r}){const{error:t,formMessageId:a}=v();const n=t?String(t?.message??""):r.children;if(!n)return null;return(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,i.cn)("text-sm !text-red-600",e),...r,children:n})}},4013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23));Promise.resolve().then(t.t.bind(t,7924,23));Promise.resolve().then(t.t.bind(t,5656,23));Promise.resolve().then(t.t.bind(t,99,23));Promise.resolve().then(t.t.bind(t,8243,23));Promise.resolve().then(t.t.bind(t,8827,23));Promise.resolve().then(t.t.bind(t,2763,23));Promise.resolve().then(t.t.bind(t,7173,23))},4593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>i});var s=t(687);var a=t.n(s);var n=t(218);var o=t(2581);const i=({...e})=>{const{theme:r="system"}=(0,n.D)();return(0,s.jsx)(o.l$,{theme:r,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},4934:(e,r,t)=>{"use strict";t.d(r,{$:()=>m});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(1391);var d=t(4224);var l=t(6241);const c=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function m({className:e,variant:r,size:t,asChild:a=false,...n}){const o=a?i.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,l.cn)(c({variant:r,size:t,className:e})),...n})}},5958:(e,r,t)=>{Promise.resolve().then(t.bind(t,4593));Promise.resolve().then(t.bind(t,1279));Promise.resolve().then(t.bind(t,9208))},6241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(9384);var a=t(2348);function n(...e){return(0,a.QP)((0,s.$)(e))}},6487:()=>{},6749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23));Promise.resolve().then(t.t.bind(t,6042,23));Promise.resolve().then(t.t.bind(t,8170,23));Promise.resolve().then(t.t.bind(t,9477,23));Promise.resolve().then(t.t.bind(t,9345,23));Promise.resolve().then(t.t.bind(t,2089,23));Promise.resolve().then(t.t.bind(t,6577,23));Promise.resolve().then(t.t.bind(t,1307,23))},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>o,j2:()=>l});var s=t(9859);var a=t(3560);var n=t(6056);const{handlers:o,signIn:i,signOut:d,auth:l}=(0,s.Ay)({providers:[a.A,n.A],secret:process.env.AUTH_SECRET})},7470:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>o});var s=t(7413);var a=t.n(s);const n=({children:e})=>{return(0,s.jsx)("main",{children:e})};const o=n},8014:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>h,metadata:()=>x});var s=t(7413);var a=t.n(s);var n=t(363);var o=t(6649);var i=t.n(o);var d=t(5843);var l=t.n(d);var c=t(2704);var m=t.n(c);var u=t(25);var v=t(2175);var p=t(6814);const x={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}};const g=async({children:e})=>{const r=await (0,p.j2)();return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:true,children:(0,s.jsx)(v.SessionProvider,{session:r,children:(0,s.jsxs)("body",{className:`${i().className} ${l().variable} antialiased`,children:[(0,s.jsx)(u["default"],{attribute:"class",defaultTheme:"light",children:e}),(0,s.jsx)(n.Toaster,{})]})})})};const h=g},8335:()=>{},8988:(e,r,t)=>{"use strict";t.d(r,{p:()=>d});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(6241);function d({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},9104:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(7605);var d=t(3442);var l=t(6189);var c=t(474);var m=t(2925);var u=t(8988);var v=t(4934);const p=({mode:e,schema:r,defaultValues:t,onSubmit:a})=>{const o=(0,l.useRouter)();const p=(0,i.mN)({resolver:(0,d.u)(r),defaultValues:t,mode:"onChange"});const x=(0,n.useRef)(null);const g=(0,n.useRef)(null);const h=(0,n.useRef)(null);const f=(0,n.useRef)(null);const b=[x,g,h,f];const[w,P]=(0,n.useState)(false);const j=async r=>{const t=await a(r);if(t.success){if(e==="forgot"){o.push("/sign-in/Forgotpassword/verifyotp")}else if(e==="otp"){o.push("/sign-in/Forgotpassword/verifyotp/Setnewpassword")}else if(e==="reset"){o.push("/sign-in")}}};const y=(e,r)=>{const t=r.target.value;p.setValue(`otp${e+1}`,t);if(t&&e<b.length-1){b[e+1].current?.focus()}};return(0,s.jsxs)("div",{className:"w-full px-4 py-6 lg:max-w-md lg:min-w-[28rem]",children:[(0,s.jsx)("h1",{className:"text-[28px] lg:text-[34px] leading-[36px] lg:leading-[41px] font-semibold font-poppins text-[#100F14] mb-2",children:e==="forgot"?"Forget Password":e==="reset"?"Reset Password":"Verification Code"}),(0,s.jsx)("p",{className:"text-gray-600 font-medium mb-8 text-poppins",children:e==="forgot"?"Enter your email id to request a password reset.":e==="reset"?"Please create a new password":"Enter OTP sent to your email for the verification process."}),(0,s.jsx)(m.lV,{...p,children:(0,s.jsxs)("form",{onSubmit:p.handleSubmit(j),className:"space-y-6",children:[e==="otp"?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h4",{className:"text-lg font-medium mb-3",children:"OTP"}),(0,s.jsx)("div",{className:"flex gap-4 justify-between",children:b.map((e,r)=>(0,s.jsx)(m.zB,{control:p.control,name:`otp${r+1}`,render:({field:e})=>{const{ref:t,onChange:a,...n}=e;return(0,s.jsxs)(m.eI,{className:"flex-1",children:[(0,s.jsx)(m.MJ,{children:(0,s.jsx)(u.p,{ref:e=>{t(e);b[r].current=e},maxLength:1,className:"text-center font-bold text-3xl py-8 sm:py-7",type:"text",inputMode:"numeric",pattern:"[0-9]*",onChange:e=>{a(e);y(r,e)},...n})}),(0,s.jsx)(m.C5,{})]})}},r))})]}):Object.keys(t).map(e=>(0,s.jsx)(m.zB,{control:p.control,name:e,render:({field:r})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{className:"text-[#2E2E2E]",children:e==="email"?"Email Address":e==="password"||e==="newPassword"?"Password":"Confirm Password"}),(0,s.jsx)(m.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:e.toLowerCase().includes("password")&&!w?"password":"text",placeholder:e==="email"?"Enter Your Email address":e==="password"||e==="newPassword"?"Enter Your Password":"Confirm Your Password",className:"py-5 sm:py-6 pr-12",...r}),e.toLowerCase().includes("password")&&(0,s.jsx)("button",{type:"button",onClick:()=>P(e=>!e),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,s.jsx)(c["default"],{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,s.jsx)(m.C5,{})]})},e)),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(v.$,{type:"submit",className:"lg:w-[360px] w-full  bg-[#6938EF] hover:bg-[#682ed6] hover:cursor-pointer text-white py-5 sm:py-6 px-4 rounded-full mt-5",disabled:p.formState.isSubmitting||!p.formState.isValid,children:p.formState.isSubmitting?e==="forgot"?"Sending...":e==="reset"?"Resetting...":"Verifying...":e==="forgot"?"Continue":e==="reset"?"Reset Password":"Continue"})})]})})]})};const x=p},9360:(e,r,t)=>{"use strict";t.d(r,{Ih:()=>o,Il:()=>a,mJ:()=>n,pi:()=>d,zL:()=>i});var s=t(7566);const a=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const n=s.Ik({confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:s.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const o=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})});const i=s.Ik({newPassword:s.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]});const d=s.Ik({otp1:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})}};