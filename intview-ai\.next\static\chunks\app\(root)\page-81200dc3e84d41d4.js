(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[76],{1338:(e,s,r)=>{Promise.resolve().then(r.bind(r,6153))},6153:(e,s,r)=>{"use strict";r.r(s);r.d(s,{"default":()=>d});var a=r(5155);const l=()=>{return(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Dashboard"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Welcome to AI Interview"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Get started with your interview preparation."})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Job Posts"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage and view your job applications."})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track your interview performance."})]})]})]})};const d=l}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(1338));var r=e.O();_N_E=r}]);