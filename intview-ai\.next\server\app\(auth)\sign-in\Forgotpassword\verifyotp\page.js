(()=>{var e={};e.id=212;e.ids=[212];e.modules={497:(e,t,r)=>{"use strict";r.r(t);r.d(t,{"default":()=>p});var s=r(687);var o=r.n(s);var n=r(3210);var i=r.n(n);var a=r(9104);var l=r(9360);var d=r(474);const c=()=>{const e={otp1:"",otp2:"",otp3:"",otp4:""};const t=async e=>{console.log("OTP Submitted:",e);return{success:true}};return(0,s.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)(d["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,s.jsxs)("div",{className:"lg:hidden",children:[(0,s.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,s.jsx)(d["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,s.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,s.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,s.jsx)(a.A,{mode:"otp",schema:l.pi,defaultValues:e,onSubmit:t})})})]}),(0,s.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,s.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,s.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsx)(a.A,{mode:"otp",schema:l.pi,defaultValues:e,onSubmit:t})})})})})]})};const p=c},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2477:(e,t,r)=>{"use strict";r.r(t);r.d(t,{GlobalError:()=>a.a,__next_app__:()=>_,pages:()=>j,routeModule:()=>A,tree:()=>w});var s=r(5239);var o=r.n(s);var n=r(8088);var i=r(8170);var a=r.n(i);var l=r(893);var d=r.n(l);var c={};for(const e in l)if(["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)<0)c[e]=()=>l[e];r.d(t,c);const p=()=>Promise.resolve().then(r.bind(r,8014));const u=()=>Promise.resolve().then(r.t.bind(r,7398,23));const h=()=>Promise.resolve().then(r.t.bind(r,9999,23));const v=()=>Promise.resolve().then(r.t.bind(r,5284,23));const m=()=>Promise.resolve().then(r.bind(r,7470));const g=()=>Promise.resolve().then(r.t.bind(r,7398,23));const x=()=>Promise.resolve().then(r.t.bind(r,9999,23));const f=()=>Promise.resolve().then(r.t.bind(r,5284,23));const b=()=>Promise.resolve().then(r.bind(r,6007));const w={children:["",{children:["(auth)",{children:["sign-in",{children:["Forgotpassword",{children:["verifyotp",{children:["__PAGE__",{},{page:[b,"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\Forgotpassword\\verifyotp\\page.tsx"]}]},{}]},{}]},{}]},{"layout":[m,"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx"],"not-found":[g,"next/dist/client/components/not-found-error"],"forbidden":[x,"next/dist/client/components/forbidden-error"],"unauthorized":[f,"next/dist/client/components/unauthorized-error"]}]},{"layout":[p,"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[u,"next/dist/client/components/not-found-error"],"forbidden":[h,"next/dist/client/components/forbidden-error"],"unauthorized":[v,"next/dist/client/components/unauthorized-error"]}]}.children;const j=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\Forgotpassword\\verifyotp\\page.tsx"];const y=r;const P=()=>Promise.resolve();const _={require:y,loadChunk:P};const A=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/sign-in/Forgotpassword/verifyotp/page",pathname:"/sign-in/Forgotpassword/verifyotp",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:w}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3289:(e,t,r)=>{Promise.resolve().then(r.bind(r,6007))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5511:e=>{"use strict";e.exports=require("crypto")},6007:(e,t,r)=>{"use strict";r.r(t);r.d(t,{"default":()=>n});var s=r(2907);var o=r.n(s);const n=(0,s.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(auth)\\\\sign-in\\\\Forgotpassword\\\\verifyotp\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\Forgotpassword\\verifyotp\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9737:(e,t,r)=>{Promise.resolve().then(r.bind(r,497))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e);var s=t.X(0,[97,423,598,23,937,597],()=>r(2477));module.exports=s})();