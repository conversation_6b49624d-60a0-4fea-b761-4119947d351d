(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[812],{3999:(e,s,t)=>{"use strict";t.d(s,{cn:()=>a});var r=t(2596);var n=t(9688);function a(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++){s[t]=arguments[t]}return(0,n.QP)((0,r.$)(s))}},6149:(e,s,t)=>{Promise.resolve().then(t.bind(t,8475))},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>o});var r=t(5155);var n=t(2115);var a=t(4624);var i=t(2085);var l=t(3999);const c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:t,size:n,asChild:i=false,...o}=e;const d=i?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,l.cn)(c({variant:t,size:n,className:s})),...o})}},8475:(e,s,t)=>{"use strict";t.r(s);t.d(s,{"default":()=>es});var r=t(5155);var n=t(2115);var a=t(7168);var i=t(2138);const l=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."];const c=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."];const o=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."];const d=e=>{let{candidateName:s="Jonathan",jobTitle:t="Insurance Agent",languages:d=["English","Chinese"],instructions:x=l,environmentChecklist:m=c,disclaimers:u=o,onNext:h}=e;const[p,g]=(0,n.useState)(false);return(0,r.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,r.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,r.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",s,"!"]}),(0,r.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",t,"."]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:d.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:x.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:m.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:u.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,r.jsx)("input",{type:"checkbox",id:"terms",checked:p,onChange:e=>g(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,r.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)(a.$,{disabled:!p,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>h&&h(),children:["Proceed",(0,r.jsx)(i.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})};const x=d;var m=t(646);var u=t(9588);var h=t(4516);var p=t(6325);const g=()=>{return(0,r.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,r.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,r.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,r.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,r.jsx)(h.A,{className:"w-4 h-5"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,r.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,r.jsx)(p.A,{className:"w-4 h-5"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,r.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})})};const f=g;class v{async sendInterviewRequest(e){try{const s=await fetch("".concat(this.baseUrl,"/interview"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){const e=await s.text();throw new Error("Interview API Error: ".concat(s.status," ").concat(s.statusText," - ").concat(e))}const t=await s.json();return t}catch(e){console.error("Failed to send interview request:",e);throw e}}async startInterview(e,s,t){const r={position:e,name:s,experience:t,history:[]};const n=await this.sendInterviewRequest(r);return n}async continueInterview(e,s,t,r){const n={position:e,name:s,experience:t,history:r};const a=await this.sendInterviewRequest(n);return a}constructor(){this.baseUrl="https://interview-server-delta.vercel.app"}}const j=new v;const b=(0,n.createContext)(undefined);const w=e=>{let{children:s}=e;const[t,a]=(0,n.useState)("");const[i,l]=(0,n.useState)(false);const[c,o]=(0,n.useState)(false);const[d,x]=(0,n.useState)("Anas ALi");const[m,u]=(0,n.useState)("Software Engineer");const[h,p]=(0,n.useState)(3);const[g,f]=(0,n.useState)(0);const[v,w]=(0,n.useState)(0);const[y,N]=(0,n.useState)(0);const[A,S]=(0,n.useState)([]);const[k,I]=(0,n.useState)(false);const[C,E]=(0,n.useState)(null);const[F,R]=(0,n.useState)([]);const[q,T]=(0,n.useState)(null);const z=(0,n.useCallback)(e=>{R(s=>[...s,e])},[]);const P=(0,n.useCallback)(async()=>{o(true);T(null);try{const e=await j.startInterview(m,d,h);a(e.nextQuestion);f(e.currentQuestionScore);I(e.isInterviewCompleted);if(e.Summary){E(e.Summary)}z({role:"interviewer",content:e.nextQuestion});l(true)}catch(s){const e=s instanceof Error?s.message:"Failed to start interview";T(e);console.error("Failed to start interview:",s)}finally{o(false)}},[m,d,h,z]);const M=(0,n.useCallback)(async e=>{o(true);T(null);try{const s={role:"candidate",content:e};const t=[...F,s];z(s);const r=await j.continueInterview(m,d,h,t);a(r.nextQuestion);f(r.currentQuestionScore);I(r.isInterviewCompleted);if(r.currentQuestionScore>0){w(e=>e+r.currentQuestionScore);N(e=>e+1);S(e=>[...e,r.currentQuestionScore])}if(r.Summary){E(r.Summary)}if(!r.isInterviewCompleted&&r.nextQuestion){z({role:"interviewer",content:r.nextQuestion})}}catch(s){const e=s instanceof Error?s.message:"Failed to submit answer";T(e);console.error("Failed to submit answer:",s)}finally{o(false)}},[m,d,h,F,z]);const Q={currentQuestion:t,setCurrentQuestion:a,isInterviewStarted:i,setIsInterviewStarted:l,isLoading:c,setIsLoading:o,candidateName:d,setCandidateName:x,jobTitle:m,setJobTitle:u,experience:h,setExperience:p,currentQuestionScore:g,totalScore:v,questionCount:y,questionScores:A,isInterviewCompleted:k,interviewSummary:C,conversationHistory:F,addToHistory:z,startInterview:P,submitAnswer:M,error:q,setError:T};return(0,r.jsx)(b.Provider,{value:Q,children:s})};const y=()=>{const e=(0,n.useContext)(b);if(e===undefined){throw new Error("useInterview must be used within an InterviewProvider")}return e};const N=e=>{let{className:s}=e;const{conversationHistory:t,isInterviewStarted:n}=y();const a=t.filter(e=>e.role==="interviewer");return(0,r.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full shadow-sm overflow-y-auto scrollbar-hidden ".concat(s||""),children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Video Transcript"}),!n?(0,r.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Interview not started yet"}):a.length===0?(0,r.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Loading questions..."}):(0,r.jsx)("ul",{className:"space-y-4",children:a.map((e,s)=>(0,r.jsxs)("li",{className:"relative flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium bg-[#6938EF] text-white flex-shrink-0",children:s+1}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsx)("p",{className:"text-sm text-gray-800 leading-relaxed",children:e.content})})]},s))})]})};const A=N;const S=e=>{let{children:s}=e;return(0,r.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:s})};const k=S;var I=t(8979);var C=t(5925);let E=null;let F=null;if(true){const e=t(6866);E=e.default;F=e.useSpeechRecognition}const R=e=>{let{onTranscriptChange:s,onFinalTranscript:t,isDisabled:i=false,className:l=""}=e;const[c,o]=(0,n.useState)(false);const[d,x]=(0,n.useState)(false);let m;try{m=F?F():{transcript:"",listening:false,resetTranscript:()=>{},browserSupportsSpeechRecognition:false,isMicrophoneAvailable:false}}catch(e){m={transcript:"",listening:false,resetTranscript:()=>{},browserSupportsSpeechRecognition:false,isMicrophoneAvailable:false}}const{transcript:h,listening:p,resetTranscript:g,browserSupportsSpeechRecognition:f,isMicrophoneAvailable:v}=m;(0,n.useEffect)(()=>{if(h){s(h)}},[h,s]);(0,n.useEffect)(()=>{if(d&&!p&&h){t(h);o(false);x(false)}},[p,h,d,t]);(0,n.useEffect)(()=>{if(!p)return},[p,c]);const j=()=>{if(!E||!f||!v||i){return}g();o(true);x(true);E.startListening({continuous:true,language:"en-US"})};const b=()=>{if(!E){return}E.stopListening();o(false);if(h){t(h)}x(false)};const w=()=>{g();s("")};if(!f){return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(l),children:(0,r.jsx)("p",{className:"text-red-700 text-sm",children:"Your browser doesn't support speech recognition. Please use a modern browser like Chrome, Edge, or Safari."})})}if(!v){return(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 ".concat(l),children:(0,r.jsx)("p",{className:"text-yellow-700 text-sm",children:"Microphone access is required for voice input. Please allow microphone permissions and refresh the page."})})}return(0,r.jsxs)("div",{className:"space-y-4 ".concat(l),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[!c&&(0,r.jsxs)(a.$,{onClick:j,disabled:i,variant:"default",size:"sm",className:"flex items-center gap-2 bg-primary  text-white",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),"Start Recording"]}),c&&(0,r.jsxs)(a.$,{onClick:b,variant:"destructive",size:"sm",className:"flex items-center gap-2 text-red-400",children:[(0,r.jsx)(I.A,{className:"w-4 h-4 "}),"Stop Recording"]}),c&&(0,r.jsxs)(a.$,{size:"sm",className:"flex items-center gap-2 text-white",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),"Listening"]}),h&&!c&&(0,r.jsx)(a.$,{onClick:w,variant:"outline",size:"sm",className:"flex items-center gap-2",children:"Clear"})]}),h&&(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-gray-800 text-sm leading-relaxed",children:h})})]})};const q=e=>{const[s,t]=(0,n.useState)(false);(0,n.useEffect)(()=>{t(true)},[]);if(!s){return(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 ".concat(e.className||""),children:(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Initializing voice recognition..."})})}return(0,r.jsx)(R,{...e})};const T=q;const z=e=>{let{onNext:s}=e;const{currentQuestion:t,isInterviewStarted:l,isLoading:c,startInterview:o,submitAnswer:d,error:x,isInterviewCompleted:h,interviewSummary:p}=y();const[g,v]=(0,n.useState)(false);const[j,b]=(0,n.useState)("");const[w,N]=(0,n.useState)("voice");const S=async()=>{try{await o();v(true)}catch(e){console.error("Failed to start interview:",e)}};const I=e=>{b(e)};const C=e=>{b(e)};const E=()=>{N(w==="voice"?"text":"voice");b("")};const F=async()=>{if(!j.trim()){alert("Please provide an answer before continuing.");return}try{v(false);await d(j);b("");v(true)}catch(e){console.error("Failed to submit answer:",e);v(true)}};if(!l){return(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(f,{}),(0,r.jsxs)(k,{children:[(0,r.jsx)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:(0,r.jsx)(A,{className:"h-[550px]"})}),(0,r.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,r.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:S,children:["Start Interview",(0,r.jsx)(i.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,r.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"Ready to begin"})]})]})}if(h){return(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(f,{}),(0,r.jsx)(k,{children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Interview Completed!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Thank you for completing the interview. Your responses have been recorded."}),p&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Detailed Interview Summary"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Technical Skills:"}),(0,r.jsxs)("span",{className:"font-medium",children:[p.ScoreCard.technicalSkills,"/20"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Problem Solving:"}),(0,r.jsxs)("span",{className:"font-medium",children:[p.ScoreCard.problemSolving,"/20"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Communication:"}),(0,r.jsxs)("span",{className:"font-medium",children:[p.ScoreCard.communication,"/20"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Experience:"}),(0,r.jsxs)("span",{className:"font-medium",children:[p.ScoreCard.experience,"/20"]})]}),(0,r.jsx)("hr",{className:"my-2"}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold text-base",children:[(0,r.jsx)("span",{children:"Total Score:"}),(0,r.jsxs)("span",{children:[p.ScoreCard.overall,"/100"]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(p.recommendation==="HIRE"?"bg-green-100 text-green-800":p.recommendation==="REJECT"?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:p.recommendation})}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mt-2",children:p.reason})]})]})]})]}),(0,r.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>s===null||s===void 0?void 0:s(),children:["View Results",(0,r.jsx)(i.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})]})})]})}return(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(f,{}),(0,r.jsxs)(k,{children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-10 justify-center items-center lg:items-start",children:[(0,r.jsxs)("div",{className:"flex-1 max-w-2xl",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Question:"}),(0,r.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed",children:t||"Loading question..."}),x&&(0,r.jsxs)("div",{className:"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:["Error: ",x]})]}),g&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Your Answer:"}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsxs)(a.$,{variant:w==="voice"?"default":"outline",size:"sm",onClick:E,className:"flex items-center gap-2 text-white",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),"Voice"]})})]}),w==="voice"?(0,r.jsx)(T,{onTranscriptChange:I,onFinalTranscript:C,isDisabled:c,className:"mb-4"}):(0,r.jsx)("textarea",{value:j,onChange:e=>b(e.target.value),placeholder:"Type your answer here...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4})]})]}),(0,r.jsx)(A,{className:"h-[400px] lg:w-80"})]}),(0,r.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:g&&!c?(0,r.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:F,children:[h?"Finish Interview":"Submit Answer",(0,r.jsx)(i.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]}):(0,r.jsx)("div",{className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500",children:c?"Loading question...":"Listen to the question"})})]})]})};const P=z;const M=()=>{const{conversationHistory:e,currentQuestionScore:s,totalScore:t}=y();return(0,r.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-5",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-black",children:"Interview Transcript"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Score: ",t,"/100"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[e.map((t,n)=>(0,r.jsx)("div",{className:"mb-4",children:t.role==="interviewer"?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-semibold text-blue-600 mb-1",children:["Question ",Math.floor(n/2)+1,":"]}),(0,r.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:t.content})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-green-600 mb-1",children:"Answer:"}),(0,r.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:t.content}),n===e.length-1&&s>0&&(0,r.jsxs)("p",{className:"text-xs text-blue-500 mt-1",children:["Score: ",s,"/20"]})]})},n)),e.length===0&&(0,r.jsx)("p",{className:"text-sm text-gray-500 italic",children:"Interview transcript will appear here as you progress..."})]})]})};const Q=M;const V=e=>{let{onNext:s}=e;return(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(f,{}),(0,r.jsxs)(k,{children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,r.jsx)(A,{}),(0,r.jsx)(Q,{})]}),(0,r.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,r.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>s&&s(),children:["Finish Interview",(0,r.jsx)(i.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})};const L=V;var U=t(6766);const $={"src":"/_next/static/media/trophy.73528452.png","height":28,"width":28,"blurDataURL":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC","blurWidth":8,"blurHeight":8};const D=()=>{return(0,r.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,r.jsx)("div",{className:"flex justify-center mb-2",children:(0,r.jsx)(U["default"],{src:$,alt:"Trophy"})}),(0,r.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,r.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,r.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,r.jsx)("div",{className:"top-0",children:(0,r.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]})};const B=D;const _=e=>{let{label:s,value:t,color:n="bg-orange-500"}=e;return(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsx)("span",{className:"mb-1",children:s}),(0,r.jsxs)("span",{children:[t,"/100"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:"h-2.5 rounded-full ".concat(n),style:{width:"".concat(t,"%")}})})]})};const H=_;var X=t(6636);var Y=t(839);const O=e=>{let{label:s,percent:t,color:n,trailColor:a}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,r.jsx)("p",{className:"text-sm font-semibold mb-3",children:s}),(0,r.jsx)("div",{className:"w-32 h-28",children:(0,r.jsx)(X.QF,{value:t,text:"".concat(t,"%"),strokeWidth:10,styles:(0,X.Hf)({textSize:"12px",pathColor:n,textColor:"#5a5a5a",trailColor:a})})})]})};const W=O;const J=()=>{return(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,r.jsx)("span",{children:"Resume Score"}),(0,r.jsx)("span",{children:"65%"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)(H,{label:"Company Fit",value:66}),(0,r.jsx)(H,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,r.jsx)(H,{label:"Job Knowledge",value:66}),(0,r.jsx)(H,{label:"Education",value:66}),(0,r.jsx)(H,{label:"Hard Skills",value:66})]}),(0,r.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,r.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,r.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)(H,{label:"Professionalism",value:64}),(0,r.jsx)(H,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,r.jsx)(H,{label:"Communication",value:58}),(0,r.jsx)(H,{label:"Sociability",value:70})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,r.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,r.jsx)(W,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,r.jsx)(W,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]})};const Z=J;const G=()=>{return(0,r.jsxs)("div",{className:"h-screen",children:[(0,r.jsx)(B,{}),(0,r.jsx)(k,{children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,r.jsx)(A,{}),(0,r.jsx)(Q,{})]})}),(0,r.jsx)(Z,{})]})};const K=G;const ee=()=>{const[e,s]=(0,n.useState)("instructions");const t=()=>{switch(e){case"instructions":return(0,r.jsx)(x,{onNext:()=>s("questions")});case"questions":return(0,r.jsx)(P,{onNext:()=>s("finishInterview")});case"finishInterview":return(0,r.jsx)(L,{onNext:()=>s("analysis")});case"analysis":return(0,r.jsx)(K,{});default:return(0,r.jsx)(x,{onNext:()=>s("questions")})}};return(0,r.jsx)(w,{children:(0,r.jsx)("div",{children:t()})})};const es=ee}},e=>{var s=s=>e(e.s=s);e.O(0,[318,766,831,444,441,684,358],()=>s(6149));var t=e.O();_N_E=t}]);