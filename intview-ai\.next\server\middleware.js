(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[751],{16:(o,i,s)=>{"use strict";s.d(i,{F:()=>c,h:()=>u});const a="DYNAMIC_SERVER_USAGE";class c extends Error{constructor(o){super("Dynamic server usage: "+o),this.description=o,this.digest=a}}function u(o){if(typeof o!=="object"||o===null||!("digest"in o)||typeof o.digest!=="string"){return false}return o.digest===a}},35:(o,i)=>{"use strict";var s={H:null,A:null};function a(o){var i="https://react.dev/errors/"+o;if(1<arguments.length){i+="?args[]="+encodeURIComponent(arguments[1]);for(var s=2;s<arguments.length;s++)i+="&args[]="+encodeURIComponent(arguments[s])}return"Minified React error #"+o+"; visit "+i+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var c=Array.isArray,u=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),h=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),w=Symbol.iterator;function b(o){if(null===o||"object"!==typeof o)return null;o=w&&o[w]||o["@@iterator"];return"function"===typeof o?o:null}var _=Object.prototype.hasOwnProperty,v=Object.assign;function E(o,i,s,a,c,l){s=l.ref;return{$$typeof:u,type:o,key:i,ref:void 0!==s?s:null,props:l}}function S(o,i){return E(o.type,i,void 0,void 0,void 0,o.props)}function k(o){return"object"===typeof o&&null!==o&&o.$$typeof===u}function x(o){var i={"=":"=0",":":"=2"};return"$"+o.replace(/[=:]/g,function(o){return i[o]})}var R=/\/+/g;function P(o,i){return"object"===typeof o&&null!==o&&null!=o.key?x(""+o.key):i.toString(36)}function A(){}function T(o){switch(o.status){case"fulfilled":return o.value;case"rejected":throw o.reason;default:switch("string"===typeof o.status?o.then(A,A):(o.status="pending",o.then(function(i){"pending"===o.status&&(o.status="fulfilled",o.value=i)},function(i){"pending"===o.status&&(o.status="rejected",o.reason=i)})),o.status){case"fulfilled":return o.value;case"rejected":throw o.reason}}throw o}function O(o,i,s,d,f){var p=typeof o;if("undefined"===p||"boolean"===p)o=null;var h=!1;if(null===o)h=!0;else switch(p){case"bigint":case"string":case"number":h=!0;break;case"object":switch(o.$$typeof){case u:case l:h=!0;break;case y:return h=o._init,O(h(o._payload),i,s,d,f)}}if(h)return f=f(o),h=""===d?"."+P(o,0):d,c(f)?(s="",null!=h&&(s=h.replace(R,"$&/")+"/"),O(f,i,s,"",function(o){return o})):null!=f&&(k(f)&&(f=S(f,s+(null==f.key||o&&o.key===f.key?"":(""+f.key).replace(R,"$&/")+"/")+h)),i.push(f)),1;h=0;var g=""===d?".":d+":";if(c(o))for(var m=0;m<o.length;m++)d=o[m],p=g+P(d,m),h+=O(d,i,s,p,f);else if(m=b(o),"function"===typeof m)for(o=m.call(o),m=0;!(d=o.next()).done;)d=d.value,p=g+P(d,m++),h+=O(d,i,s,p,f);else if("object"===p){if("function"===typeof o.then)return O(T(o),i,s,d,f);i=String(o);throw Error(a(31,"[object Object]"===i?"object with keys {"+Object.keys(o).join(", ")+"}":i))}return h}function C(o,i,s){if(null==o)return o;var a=[],c=0;O(o,a,"","",function(o){return i.call(s,o,c++)});return a}function I(o){if(-1===o._status){var i=o._result;i=i();i.then(function(i){if(0===o._status||-1===o._status)o._status=1,o._result=i},function(i){if(0===o._status||-1===o._status)o._status=2,o._result=i});-1===o._status&&(o._status=0,o._result=i)}if(1===o._status)return o._result.default;throw o._result}function j(){return new WeakMap}function N(){return{s:0,v:void 0,o:null,p:null}}i.Children={map:C,forEach:function(o,i,s){C(o,function(){i.apply(this,arguments)},s)},count:function(o){var i=0;C(o,function(){i++});return i},toArray:function(o){return C(o,function(o){return o})||[]},only:function(o){if(!k(o))throw Error(a(143));return o}};i.Fragment=d;i.Profiler=p;i.StrictMode=f;i.Suspense=g;i.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s;i.cache=function(o){return function(){var i=s.A;if(!i)return o.apply(null,arguments);var a=i.getCacheForType(j);i=a.get(o);void 0===i&&(i=N(),a.set(o,i));a=0;for(var c=arguments.length;a<c;a++){var u=arguments[a];if("function"===typeof u||"object"===typeof u&&null!==u){var l=i.o;null===l&&(i.o=l=new WeakMap);i=l.get(u);void 0===i&&(i=N(),l.set(u,i))}else l=i.p,null===l&&(i.p=l=new Map),i=l.get(u),void 0===i&&(i=N(),l.set(u,i))}if(1===i.s)return i.v;if(2===i.s)throw i.v;try{var d=o.apply(null,arguments);a=i;a.s=1;return a.v=d}catch(o){throw d=i,d.s=2,d.v=o,o}}};i.captureOwnerStack=function(){return null};i.cloneElement=function(o,i,s){if(null===o||void 0===o)throw Error(a(267,o));var c=v({},o.props),u=o.key,l=void 0;if(null!=i)for(d in void 0!==i.ref&&(l=void 0),void 0!==i.key&&(u=""+i.key),i)!_.call(i,d)||"key"===d||"__self"===d||"__source"===d||"ref"===d&&void 0===i.ref||(c[d]=i[d]);var d=arguments.length-2;if(1===d)c.children=s;else if(1<d){for(var f=Array(d),p=0;p<d;p++)f[p]=arguments[p+2];c.children=f}return E(o.type,u,void 0,void 0,l,c)};i.createElement=function(o,i,s){var a,c={},u=null;if(null!=i)for(a in void 0!==i.key&&(u=""+i.key),i)_.call(i,a)&&"key"!==a&&"__self"!==a&&"__source"!==a&&(c[a]=i[a]);var l=arguments.length-2;if(1===l)c.children=s;else if(1<l){for(var d=Array(l),f=0;f<l;f++)d[f]=arguments[f+2];c.children=d}if(o&&o.defaultProps)for(a in l=o.defaultProps,l)void 0===c[a]&&(c[a]=l[a]);return E(o,u,void 0,void 0,null,c)};i.createRef=function(){return{current:null}};i.forwardRef=function(o){return{$$typeof:h,render:o}};i.isValidElement=k;i.lazy=function(o){return{$$typeof:y,_payload:{_status:-1,_result:o},_init:I}};i.memo=function(o,i){return{$$typeof:m,type:o,compare:void 0===i?null:i}};i.use=function(o){return s.H.use(o)};i.useCallback=function(o,i){return s.H.useCallback(o,i)};i.useDebugValue=function(){};i.useId=function(){return s.H.useId()};i.useMemo=function(o,i){return s.H.useMemo(o,i)};i.version="19.2.0-canary-3fbfb9ba-20250409"},58:(o,i,s)=>{"use strict";s.d(i,{xl:()=>l});const a=Object.defineProperty(new Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:false,configurable:true});class c{disable(){throw a}getStore(){return undefined}run(){throw a}exit(){throw a}enterWith(){throw a}static bind(o){return o}}const u=typeof globalThis!=="undefined"&&globalThis.AsyncLocalStorage;function l(){if(u){return new u}return new c}function d(o){if(u){return u.bind(o)}return c.bind(o)}function f(){if(u){return u.snapshot()}return function(o,...i){return o(...i)}}},115:(o,i,s)=>{"use strict";s.d(i,{XN:()=>x,FP:()=>c});var a=s(58);const c=(0,a.xl)();const u="RSC";const l="Next-Action";const d="Next-Router-State-Tree";const f="Next-Router-Prefetch";const p="Next-Router-Segment-Prefetch";const h="Next-HMR-Refresh";const g="__next_hmr_refresh_hash__";const m="Next-Url";const y="text/x-component";const w=[u,d,f,h,p];const b="_rsc";const _="x-nextjs-stale-time";const v="x-nextjs-postponed";const E="x-nextjs-rewritten-path";const S="x-nextjs-rewritten-query";const k="x-nextjs-prerender";function x(o){const i=c.getStore();if(!i){R(o)}switch(i.type){case"request":return i;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(new Error(`\`${o}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:false,configurable:true});case"cache":throw Object.defineProperty(new Error(`\`${o}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:false,configurable:true});case"unstable-cache":throw Object.defineProperty(new Error(`\`${o}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:false,configurable:true});default:const s=i;return s}}function R(o){throw Object.defineProperty(new Error(`\`${o}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:false,configurable:true})}function P(o){if(o.type==="prerender"||o.type==="prerender-ppr"){return o.prerenderResumeDataCache}return null}function A(o){if(o.type!=="prerender-legacy"&&o.type!=="cache"&&o.type!=="unstable-cache"){if(o.type==="request"){return o.renderResumeDataCache}return o.prerenderResumeDataCache}return null}function T(o,i){var s;if(!o.dev){return undefined}return i.type==="cache"||i.type==="prerender"?i.hmrRefreshHash:i.type==="request"?(s=i.cookies.get(NEXT_HMR_REFRESH_HASH_COOKIE))==null?void 0:s.value:undefined}function O(o,i){if(o.isDraftMode){switch(i.type){case"cache":case"unstable-cache":case"request":return i.draftMode;default:return undefined}}return undefined}},125:(o,i,s)=>{"use strict";s.r(i);s.d(i,{"default":()=>hE});var a={};s.r(a);s.d(a,{q:()=>sR,l:()=>sT});var c={};s.r(c);s.d(c,{middleware:()=>hm});async function u(){const o="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation;return o}let l=null;async function d(){if(process.env.NEXT_PHASE==="phase-production-build")return;if(!l){l=u()}const o=await l;if(o==null?void 0:o.register){try{await o.register()}catch(o){o.message=`An error occurred while loading instrumentation hook: ${o.message}`;throw o}}}async function f(...o){const i=await u();try{var s;await (i==null?void 0:(s=i.onRequestError)==null?void 0:s.call(i,...o))}catch(o){console.error("Error in instrumentation.onRequestError:",o)}}let p=null;function h(){if(!p){p=d()}return p}function g(o){return`The edge runtime does not support Node.js '${o}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}function m(o){const i=new Proxy(function(){},{get(i,s){if(s==="then"){return{}}throw Object.defineProperty(new Error(g(o)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})},construct(){throw Object.defineProperty(new Error(g(o)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})},apply(s,a,c){if(typeof c[0]==="function"){return c[0](i)}throw Object.defineProperty(new Error(g(o)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}});return new Proxy({},{get:()=>i})}function y(){if(false){}if(process!==s.g.process){process.env=s.g.process.env;s.g.process=process}Object.defineProperty(globalThis,"__import_unsupported",{value:m,enumerable:false,configurable:false});void h()}y();class w extends Error{constructor({page:o}){super(`The middleware "${o}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class b extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class _ extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}};const v="nxtP";const E="nxtI";const S="x-matched-path";const k="x-prerender-revalidate";const x="x-prerender-revalidate-if-generated";const R=".prefetch.rsc";const P=".segments";const A=".segment.rsc";const T=".rsc";const O=".action";const C=".json";const I=".meta";const j=".body";const N="x-next-cache-tags";const D="x-next-revalidated-tags";const U="x-next-revalidate-tag-token";const $="next-resume";const L=128;const M=256;const H=1024;const W="_N_T_";const K=31536e3;const q=0xfffffffe;const B="middleware";const J=null&&`(?:src/)?${B}`;const z="instrumentation";const V="private-next-pages";const G="private-dot-next";const F="private-next-root-dir";const X="private-next-app-dir";const Y="next/dist/build/webpack/loaders/next-flight-loader/module-proxy";const Z="private-next-rsc-action-validate";const Q="private-next-rsc-server-reference";const ee="private-next-rsc-cache-wrapper";const et="private-next-rsc-action-encryption";const en="private-next-rsc-action-client-wrapper";const er=null&&`You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`;const eo=null&&`You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`;const ei=null&&`You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`;const es=null&&`You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`;const ea=null&&`can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`;const ec=null&&`pages with \`getServerSideProps\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`;const eu="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?";const el="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?";const ed=null&&"The `unstable_revalidate` property is available for general use.\n"+"Please use `revalidate` instead.";const ef=null&&`can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`;const ep=null&&`You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`;const eh=null&&`Pages with \`fallback\` enabled in \`getStaticPaths\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`;const eg=null&&["app","pages","components","lib","src"];const em={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"};const ey={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};const ew={...ey,GROUP:{builtinReact:[ey.reactServerComponents,ey.actionBrowser],serverOnly:[ey.reactServerComponents,ey.actionBrowser,ey.instrument,ey.middleware],neutralTarget:[ey.apiNode,ey.apiEdge],clientOnly:[ey.serverSideRendering,ey.appPagesBrowser],bundled:[ey.reactServerComponents,ey.actionBrowser,ey.serverSideRendering,ey.appPagesBrowser,ey.shared,ey.instrument,ey.middleware],appPages:[ey.reactServerComponents,ey.serverSideRendering,ey.appPagesBrowser,ey.actionBrowser]}};const eb={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"};function e_(o){const i=new Headers;for(let[s,a]of Object.entries(o)){const o=Array.isArray(a)?a:[a];for(let a of o){if(typeof a==="undefined")continue;if(typeof a==="number"){a=a.toString()}i.append(s,a)}}return i}function ev(o){var i=[];var s=0;var a;var c;var u;var l;var d;function f(){while(s<o.length&&/\s/.test(o.charAt(s))){s+=1}return s<o.length}function p(){c=o.charAt(s);return c!=="="&&c!==";"&&c!==","}while(s<o.length){a=s;d=false;while(f()){c=o.charAt(s);if(c===","){u=s;s+=1;f();l=s;while(s<o.length&&p()){s+=1}if(s<o.length&&o.charAt(s)==="="){d=true;s=l;i.push(o.substring(a,u));a=s}else{s=u+1}}else{s+=1}}if(!d||s>=o.length){i.push(o.substring(a,o.length))}}return i}function eE(o){const i={};const s=[];if(o){for(const[a,c]of o.entries()){if(a.toLowerCase()==="set-cookie"){s.push(...ev(c));i[a]=s.length===1?s[0]:s}else{i[a]=c}}}return i}function eS(o){try{return String(new URL(String(o)))}catch(i){throw Object.defineProperty(new Error(`URL is malformed "${String(o)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:i}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:false,configurable:true})}}function ek(o){const i=[v,E];for(const s of i){if(o!==s&&o.startsWith(s)){return o.substring(s.length)}}return null};const ex=Symbol("response");const eR=Symbol("passThrough");const eP=Symbol("waitUntil");class eA{constructor(o,i){this[eR]=false;this[eP]=i?{kind:"external",function:i}:{kind:"internal",promises:[]}}respondWith(o){if(!this[ex]){this[ex]=Promise.resolve(o)}}passThroughOnException(){this[eR]=true}waitUntil(o){if(this[eP].kind==="external"){const i=this[eP].function;return i(o)}else{this[eP].promises.push(o)}}}function eT(o){return o[eP].kind==="internal"?Promise.all(o[eP].promises).then(()=>{}):undefined}class eO extends eA{constructor(o){var i;super(o.request,(i=o.context)==null?void 0:i.waitUntil);this.sourcePage=o.page}get request(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}respondWith(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}};function eC(o,i,s){if(!o)return;if(s){s=s.toLowerCase()}for(const u of o){var a,c;const o=(a=u.domain)==null?void 0:a.split(":",1)[0].toLowerCase();if(i===o||s===u.defaultLocale.toLowerCase()||((c=u.locales)==null?void 0:c.some(o=>o.toLowerCase()===s))){return u}}};function eI(o){return o.replace(/\/$/,"")||"/"};function ej(o){const i=o.indexOf("#");const s=o.indexOf("?");const a=s>-1&&(i<0||s<i);if(a||i>-1){return{pathname:o.substring(0,a?s:i),query:a?o.substring(s,i>-1?i:undefined):"",hash:i>-1?o.slice(i):""}}return{pathname:o,query:"",hash:""}};function eN(o,i){if(!o.startsWith("/")||!i){return o}const{pathname:s,query:a,hash:c}=ej(o);return""+i+s+a+c};function eD(o,i){if(!o.startsWith("/")||!i){return o}const{pathname:s,query:a,hash:c}=ej(o);return""+s+i+a+c};function eU(o,i){if(typeof o!=="string"){return false}const{pathname:s}=ej(o);return s===i||s.startsWith(i+"/")};function e$(o,i,s,a){if(!i||i===s)return o;const c=o.toLowerCase();if(!a){if(eU(c,"/api"))return o;if(eU(c,"/"+i.toLowerCase()))return o}return eN(o,"/"+i)};function eL(o){let i=e$(o.pathname,o.locale,o.buildId?undefined:o.defaultLocale,o.ignorePrefix);if(o.buildId||!o.trailingSlash){i=eI(i)}if(o.buildId){i=eD(eN(i,"/_next/data/"+o.buildId),o.pathname==="/"?"index.json":".json")}i=eN(i,o.basePath);return!o.buildId&&o.trailingSlash?!i.endsWith("/")?eD(i,"/"):i:eI(i)};function eM(o,i){let s;if((i==null?void 0:i.host)&&!Array.isArray(i.host)){s=i.host.toString().split(":",1)[0]}else if(o.hostname){s=o.hostname}else return;return s.toLowerCase()};const eH=new WeakMap;function eW(o,i){if(!i)return{pathname:o};let s=eH.get(i);if(!s){s=i.map(o=>o.toLowerCase());eH.set(i,s)}let a;const c=o.split("/",2);if(!c[1])return{pathname:o};const u=c[1].toLowerCase();const l=s.indexOf(u);if(l<0)return{pathname:o};a=i[l];o=o.slice(a.length+1)||"/";return{pathname:o,detectedLocale:a}};function eK(o,i){if(!eU(o,i)){return o}const s=o.slice(i.length);if(s.startsWith("/")){return s}return"/"+s};function eq(o,i){var s;const{basePath:a,i18n:c,trailingSlash:u}=(s=i.nextConfig)!=null?s:{};const l={pathname:o,trailingSlash:o!=="/"?o.endsWith("/"):u};if(a&&eU(l.pathname,a)){l.pathname=eK(l.pathname,a);l.basePath=a}let d=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){const o=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");const s=o[0];l.buildId=s;d=o[1]!=="index"?"/"+o.slice(1).join("/"):"/";if(i.parseData===true){l.pathname=d}}if(c){let o=i.i18nProvider?i.i18nProvider.analyze(l.pathname):eW(l.pathname,c.locales);l.locale=o.detectedLocale;var f;l.pathname=(f=o.pathname)!=null?f:l.pathname;if(!o.detectedLocale&&l.buildId){o=i.i18nProvider?i.i18nProvider.analyze(d):eW(d,c.locales);if(o.detectedLocale){l.locale=o.detectedLocale}}}return l};const eB=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function eJ(o,i){return new URL(String(o).replace(eB,"localhost"),i&&String(i).replace(eB,"localhost"))}const ez=Symbol("NextURLInternal");class eV{constructor(o,i,s){let a;let c;if(typeof i==="object"&&"pathname"in i||typeof i==="string"){a=i;c=s||{}}else{c=s||i||{}}this[ez]={url:eJ(o,a??c.base),options:c,basePath:""};this.analyze()}analyze(){var o,i,s,a,c;const u=eq(this[ez].url.pathname,{nextConfig:this[ez].options.nextConfig,parseData:!undefined,i18nProvider:this[ez].options.i18nProvider});const l=eM(this[ez].url,this[ez].options.headers);this[ez].domainLocale=this[ez].options.i18nProvider?this[ez].options.i18nProvider.detectDomainLocale(l):eC((i=this[ez].options.nextConfig)==null?void 0:(o=i.i18n)==null?void 0:o.domains,l);const d=((s=this[ez].domainLocale)==null?void 0:s.defaultLocale)||((c=this[ez].options.nextConfig)==null?void 0:(a=c.i18n)==null?void 0:a.defaultLocale);this[ez].url.pathname=u.pathname;this[ez].defaultLocale=d;this[ez].basePath=u.basePath??"";this[ez].buildId=u.buildId;this[ez].locale=u.locale??d;this[ez].trailingSlash=u.trailingSlash}formatPathname(){return eL({basePath:this[ez].basePath,buildId:this[ez].buildId,defaultLocale:!this[ez].options.forceLocale?this[ez].defaultLocale:undefined,locale:this[ez].locale,pathname:this[ez].url.pathname,trailingSlash:this[ez].trailingSlash})}formatSearch(){return this[ez].url.search}get buildId(){return this[ez].buildId}set buildId(o){this[ez].buildId=o}get locale(){return this[ez].locale??""}set locale(o){var i,s;if(!this[ez].locale||!((s=this[ez].options.nextConfig)==null?void 0:(i=s.i18n)==null?void 0:i.locales.includes(o))){throw Object.defineProperty(new TypeError(`The NextURL configuration includes no locale "${o}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:false,configurable:true})}this[ez].locale=o}get defaultLocale(){return this[ez].defaultLocale}get domainLocale(){return this[ez].domainLocale}get searchParams(){return this[ez].url.searchParams}get host(){return this[ez].url.host}set host(o){this[ez].url.host=o}get hostname(){return this[ez].url.hostname}set hostname(o){this[ez].url.hostname=o}get port(){return this[ez].url.port}set port(o){this[ez].url.port=o}get protocol(){return this[ez].url.protocol}set protocol(o){this[ez].url.protocol=o}get href(){const o=this.formatPathname();const i=this.formatSearch();return`${this.protocol}//${this.host}${o}${i}${this.hash}`}set href(o){this[ez].url=eJ(o);this.analyze()}get origin(){return this[ez].url.origin}get pathname(){return this[ez].url.pathname}set pathname(o){this[ez].url.pathname=o}get hash(){return this[ez].url.hash}set hash(o){this[ez].url.hash=o}get search(){return this[ez].url.search}set search(o){this[ez].url.search=o}get password(){return this[ez].url.password}set password(o){this[ez].url.password=o}get username(){return this[ez].url.username}set username(o){this[ez].url.username=o}get basePath(){return this[ez].basePath}set basePath(o){this[ez].basePath=o.startsWith("/")?o:`/${o}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new eV(String(this),this[ez].options)}}var eG=s(724);;const eF=Symbol("internal request");class eX extends Request{constructor(o,i={}){const s=typeof o!=="string"&&"url"in o?o.url:String(o);eS(s);if(false){}if(o instanceof Request)super(o,i);else super(s,i);const a=new eV(s,{headers:eE(this.headers),nextConfig:i.nextConfig});this[eF]={cookies:new eG.RequestCookies(this.headers),nextUrl:a,url:false?0:a.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[eF].cookies}get nextUrl(){return this[eF].nextUrl}get page(){throw new b}get ua(){throw new _}get url(){return this[eF].url}};class eY{static get(o,i,s){const a=Reflect.get(o,i,s);if(typeof a==="function"){return a.bind(o)}return a}static set(o,i,s,a){return Reflect.set(o,i,s,a)}static has(o,i){return Reflect.has(o,i)}static deleteProperty(o,i){return Reflect.deleteProperty(o,i)}};const eZ=Symbol("internal response");const eQ=new Set([301,302,303,307,308]);function e0(o,i){var s;if(o==null?void 0:(s=o.request)==null?void 0:s.headers){if(!(o.request.headers instanceof Headers)){throw Object.defineProperty(new Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:false,configurable:true})}const s=[];for(const[a,c]of o.request.headers){i.set("x-middleware-request-"+a,c);s.push(a)}i.set("x-middleware-override-headers",s.join(","))}}class e1 extends Response{constructor(o,i={}){super(o,i);const s=this.headers;const a=new eG.ResponseCookies(s);const c=new Proxy(a,{get(o,a,c){switch(a){case"delete":case"set":{return(...c)=>{const u=Reflect.apply(o[a],o,c);const l=new Headers(s);if(u instanceof eG.ResponseCookies){s.set("x-middleware-set-cookie",u.getAll().map(o=>(0,eG.stringifyCookie)(o)).join(","))}e0(i,l);return u}}default:return eY.get(o,a,c)}}});this[eZ]={cookies:c,url:i.url?new eV(i.url,{headers:eE(s),nextConfig:i.nextConfig}):undefined}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[eZ].cookies}static json(o,i){const s=Response.json(o,i);return new e1(s.body,s)}static redirect(o,i){const s=typeof i==="number"?i:(i==null?void 0:i.status)??307;if(!eQ.has(s)){throw Object.defineProperty(new RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:false,configurable:true})}const a=typeof i==="object"?i:{};const c=new Headers(a==null?void 0:a.headers);c.set("Location",eS(o));return new e1(null,{...a,headers:c,status:s})}static rewrite(o,i){const s=new Headers(i==null?void 0:i.headers);s.set("x-middleware-rewrite",eS(o));e0(i,s);return new e1(null,{...i,headers:s})}static next(o){const i=new Headers(o==null?void 0:o.headers);i.set("x-middleware-next","1");e0(o,i);return new e1(null,{...o,headers:i})}};function e2(o,i){const s=typeof i==="string"?new URL(i):i;const a=new URL(o,i);const c=a.origin===s.origin;return{url:c?a.toString().slice(s.origin.length):a.toString(),isRelative:c}}function e5(o,i){const s=e2(o,i);return s.url};const e3="RSC";const e6="Next-Action";const e4="Next-Router-State-Tree";const e8="Next-Router-Prefetch";const e9="Next-Router-Segment-Prefetch";const e7="Next-HMR-Refresh";const te="__next_hmr_refresh_hash__";const tt="Next-Url";const tn="text/x-component";const tr=[e3,e4,e8,e7,e9];const to="_rsc";const ti="x-nextjs-stale-time";const ts="x-nextjs-postponed";const ta="x-nextjs-rewritten-path";const tc="x-nextjs-rewritten-query";const tu="x-nextjs-prerender";const tl=[to];function td(o){for(const i of tl){delete o[i]}}function tf(o){const i=typeof o==="string";const s=i?new URL(o):o;s.searchParams.delete(to);return i?s.toString():s};function tp(o){return o.startsWith("/")?o:"/"+o};function th(o){return o[0]==="("&&o.endsWith(")")}function tg(o){return o.startsWith("@")&&o!=="@children"}function tm(o,i){const s=o.includes(ty);if(s){const o=JSON.stringify(i);return o!=="{}"?ty+"?"+o:ty}return o}const ty="__PAGE__";const tw="__DEFAULT__";function tb(o){return tp(o.split("/").reduce((o,i,s,a)=>{if(!i){return o}if(th(i)){return o}if(i[0]==="@"){return o}if((i==="page"||i==="route")&&s===a.length-1){return o}return o+"/"+i},""))}function t_(o){return o.replace(/\.rsc($|\?)/,"$1")};class tv extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new tv}}class tE extends Headers{constructor(o){super();this.headers=new Proxy(o,{get(i,s,a){if(typeof s==="symbol"){return eY.get(i,s,a)}const c=s.toLowerCase();const u=Object.keys(o).find(o=>o.toLowerCase()===c);if(typeof u==="undefined")return;return eY.get(i,u,a)},set(i,s,a,c){if(typeof s==="symbol"){return eY.set(i,s,a,c)}const u=s.toLowerCase();const l=Object.keys(o).find(o=>o.toLowerCase()===u);return eY.set(i,l??s,a,c)},has(i,s){if(typeof s==="symbol")return eY.has(i,s);const a=s.toLowerCase();const c=Object.keys(o).find(o=>o.toLowerCase()===a);if(typeof c==="undefined")return false;return eY.has(i,c)},deleteProperty(i,s){if(typeof s==="symbol")return eY.deleteProperty(i,s);const a=s.toLowerCase();const c=Object.keys(o).find(o=>o.toLowerCase()===a);if(typeof c==="undefined")return true;return eY.deleteProperty(i,c)}})}static seal(o){return new Proxy(o,{get(o,i,s){switch(i){case"append":case"delete":case"set":return tv.callable;default:return eY.get(o,i,s)}}})}merge(o){if(Array.isArray(o))return o.join(", ");return o}static from(o){if(o instanceof Headers)return o;return new tE(o)}append(o,i){const s=this.headers[o];if(typeof s==="string"){this.headers[o]=[s,i]}else if(Array.isArray(s)){s.push(i)}else{this.headers[o]=i}}delete(o){delete this.headers[o]}get(o){const i=this.headers[o];if(typeof i!=="undefined")return this.merge(i);return null}has(o){return typeof this.headers[o]!=="undefined"}set(o,i){this.headers[o]=i}forEach(o,i){for(const[s,a]of this.entries()){o.call(i,a,s,this)}}*entries(){for(const o of Object.keys(this.headers)){const i=o.toLowerCase();const s=this.get(i);yield[i,s]}}*keys(){for(const o of Object.keys(this.headers)){const i=o.toLowerCase();yield i}}*values(){for(const o of Object.keys(this.headers)){const i=this.get(o);yield i}}[Symbol.iterator](){return this.entries()}}var tS=s(535);var tk=s(115);class tx extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new tx}}class tR{static seal(o){return new Proxy(o,{get(o,i,s){switch(i){case"clear":case"delete":case"set":return tx.callable;default:return eY.get(o,i,s)}}})}}const tP=Symbol.for("next.mutated.cookies");function tA(o){const i=o[tP];if(!i||!Array.isArray(i)||i.length===0){return[]}return i}function tT(o,i){const s=tA(i);if(s.length===0){return false}const a=new ResponseCookies(o);const c=a.getAll();for(const o of s){a.set(o)}for(const o of c){a.set(o)}return true}class tO{static wrap(o,i){const s=new eG.ResponseCookies(new Headers);for(const i of o.getAll()){s.set(i)}let a=[];const c=new Set;const u=()=>{const o=tS.J.getStore();if(o){o.pathWasRevalidated=true}const u=s.getAll();a=u.filter(o=>c.has(o.name));if(i){const o=[];for(const i of a){const s=new eG.ResponseCookies(new Headers);s.set(i);o.push(s.toString())}i(o)}};const l=new Proxy(s,{get(o,i,s){switch(i){case tP:return a;case"delete":return function(...i){c.add(typeof i[0]==="string"?i[0]:i[0].name);try{o.delete(...i);return l}finally{u()}};case"set":return function(...i){c.add(typeof i[0]==="string"?i[0]:i[0].name);try{o.set(...i);return l}finally{u()}};default:return eY.get(o,i,s)}}});return l}}function tC(o){const i=new Proxy(o,{get(o,s,a){switch(s){case"delete":return function(...s){tj("cookies().delete");o.delete(...s);return i};case"set":return function(...s){tj("cookies().set");o.set(...s);return i};default:return eY.get(o,s,a)}}});return i}function tI(o){return o.phase==="action"}function tj(o){const i=(0,tk.XN)(o);if(!tI(i)){throw new tx}}function tN(o){const i=new RequestCookies(new Headers);for(const s of o.getAll()){i.set(s)}return i};var tD=function(o){o["handleRequest"]="BaseServer.handleRequest";o["run"]="BaseServer.run";o["pipe"]="BaseServer.pipe";o["getStaticHTML"]="BaseServer.getStaticHTML";o["render"]="BaseServer.render";o["renderToResponseWithComponents"]="BaseServer.renderToResponseWithComponents";o["renderToResponse"]="BaseServer.renderToResponse";o["renderToHTML"]="BaseServer.renderToHTML";o["renderError"]="BaseServer.renderError";o["renderErrorToResponse"]="BaseServer.renderErrorToResponse";o["renderErrorToHTML"]="BaseServer.renderErrorToHTML";o["render404"]="BaseServer.render404";return o}(tD||{});var tU=function(o){o["loadDefaultErrorComponents"]="LoadComponents.loadDefaultErrorComponents";o["loadComponents"]="LoadComponents.loadComponents";return o}(tU||{});var t$=function(o){o["getRequestHandler"]="NextServer.getRequestHandler";o["getServer"]="NextServer.getServer";o["getServerRequestHandler"]="NextServer.getServerRequestHandler";o["createServer"]="createServer.createServer";return o}(t$||{});var tL=function(o){o["compression"]="NextNodeServer.compression";o["getBuildId"]="NextNodeServer.getBuildId";o["createComponentTree"]="NextNodeServer.createComponentTree";o["clientComponentLoading"]="NextNodeServer.clientComponentLoading";o["getLayoutOrPageModule"]="NextNodeServer.getLayoutOrPageModule";o["generateStaticRoutes"]="NextNodeServer.generateStaticRoutes";o["generateFsStaticRoutes"]="NextNodeServer.generateFsStaticRoutes";o["generatePublicRoutes"]="NextNodeServer.generatePublicRoutes";o["generateImageRoutes"]="NextNodeServer.generateImageRoutes.route";o["sendRenderResult"]="NextNodeServer.sendRenderResult";o["proxyRequest"]="NextNodeServer.proxyRequest";o["runApi"]="NextNodeServer.runApi";o["render"]="NextNodeServer.render";o["renderHTML"]="NextNodeServer.renderHTML";o["imageOptimizer"]="NextNodeServer.imageOptimizer";o["getPagePath"]="NextNodeServer.getPagePath";o["getRoutesManifest"]="NextNodeServer.getRoutesManifest";o["findPageComponents"]="NextNodeServer.findPageComponents";o["getFontManifest"]="NextNodeServer.getFontManifest";o["getServerComponentManifest"]="NextNodeServer.getServerComponentManifest";o["getRequestHandler"]="NextNodeServer.getRequestHandler";o["renderToHTML"]="NextNodeServer.renderToHTML";o["renderError"]="NextNodeServer.renderError";o["renderErrorToHTML"]="NextNodeServer.renderErrorToHTML";o["render404"]="NextNodeServer.render404";o["startResponse"]="NextNodeServer.startResponse";o["route"]="route";o["onProxyReq"]="onProxyReq";o["apiResolver"]="apiResolver";o["internalFetch"]="internalFetch";return o}(tL||{});var tM=function(o){o["startServer"]="startServer.startServer";return o}(tM||{});var tH=function(o){o["getServerSideProps"]="Render.getServerSideProps";o["getStaticProps"]="Render.getStaticProps";o["renderToString"]="Render.renderToString";o["renderDocument"]="Render.renderDocument";o["createBodyResult"]="Render.createBodyResult";return o}(tH||{});var tW=function(o){o["renderToString"]="AppRender.renderToString";o["renderToReadableStream"]="AppRender.renderToReadableStream";o["getBodyResult"]="AppRender.getBodyResult";o["fetch"]="AppRender.fetch";return o}(tW||{});var tK=function(o){o["executeRoute"]="Router.executeRoute";return o}(tK||{});var tq=function(o){o["runHandler"]="Node.runHandler";return o}(tq||{});var tB=function(o){o["runHandler"]="AppRouteRouteHandlers.runHandler";return o}(tB||{});var tJ=function(o){o["generateMetadata"]="ResolveMetadata.generateMetadata";o["generateViewport"]="ResolveMetadata.generateViewport";return o}(tJ||{});var tz=function(o){o["execute"]="Middleware.execute";return o}(tz||{});const tV=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"];const tG=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function tF(o){return o!==null&&typeof o==="object"&&"then"in o&&typeof o.then==="function"};let tX;if(true){tX=s(956)}else{}const{context:tY,propagation:tZ,trace:tQ,SpanStatusCode:t0,SpanKind:t1,ROOT_CONTEXT:t2}=tX;class t5 extends Error{constructor(o,i){super(),this.bubble=o,this.result=i}}function t3(o){if(typeof o!=="object"||o===null)return false;return o instanceof t5}const t6=(o,i)=>{if(t3(i)&&i.bubble){o.setAttribute("next.bubble",true)}else{if(i){o.recordException(i)}o.setStatus({code:t0.ERROR,message:i==null?void 0:i.message})}o.end()};const t4=new Map;const t8=tX.createContextKey("next.rootSpanId");let t9=0;const t7=()=>t9++;const ne={set(o,i,s){o.push({key:i,value:s})}};class nt{getTracerInstance(){return tQ.getTracer("next.js","0.0.1")}getContext(){return tY}getTracePropagationData(){const o=tY.active();const i=[];tZ.inject(o,i,ne);return i}getActiveScopeSpan(){return tQ.getSpan(tY==null?void 0:tY.active())}withPropagatedContext(o,i,s){const a=tY.active();if(tQ.getSpanContext(a)){return i()}const c=tZ.extract(a,o,s);return tY.with(c,i)}trace(...o){var i;const[s,a,c]=o;const{fn:u,options:l}=typeof a==="function"?{fn:a,options:{}}:{fn:c,options:{...a}};const d=l.spanName??s;if(!tV.includes(s)&&process.env.NEXT_OTEL_VERBOSE!=="1"||l.hideSpan){return u()}let f=this.getSpanContext((l==null?void 0:l.parentSpan)??this.getActiveScopeSpan());let p=false;if(!f){f=(tY==null?void 0:tY.active())??t2;p=true}else if((i=tQ.getSpanContext(f))==null?void 0:i.isRemote){p=true}const h=t7();l.attributes={"next.span_name":d,"next.span_type":s,...l.attributes};return tY.with(f.setValue(t8,h),()=>this.getTracerInstance().startActiveSpan(d,l,o=>{const i="performance"in globalThis&&"measure"in performance?globalThis.performance.now():undefined;const a=()=>{t4.delete(h);if(i&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&tG.includes(s||"")){performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(s.split(".").pop()||"").replace(/[A-Z]/g,o=>"-"+o.toLowerCase())}`,{start:i,end:performance.now()})}};if(p){t4.set(h,new Map(Object.entries(l.attributes??{})))}try{if(u.length>1){return u(o,i=>t6(o,i))}const i=u(o);if(tF(i)){return i.then(i=>{o.end();return i}).catch(i=>{t6(o,i);throw i}).finally(a)}else{o.end();a()}return i}catch(i){t6(o,i);a();throw i}}))}wrap(...o){const i=this;const[s,a,c]=o.length===3?o:[o[0],{},o[1]];if(!tV.includes(s)&&process.env.NEXT_OTEL_VERBOSE!=="1"){return c}return function(){let o=a;if(typeof o==="function"&&typeof c==="function"){o=o.apply(this,arguments)}const u=arguments.length-1;const l=arguments[u];if(typeof l==="function"){const a=i.getContext().bind(tY.active(),l);return i.trace(s,o,(o,i)=>{arguments[u]=function(o){i==null?void 0:i(o);return a.apply(this,arguments)};return c.apply(this,arguments)})}else{return i.trace(s,o,()=>c.apply(this,arguments))}}}startSpan(...o){const[i,s]=o;const a=this.getSpanContext((s==null?void 0:s.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(i,s,a)}getSpanContext(o){const i=o?tQ.setSpan(tY.active(),o):undefined;return i}getRootSpanAttributes(){const o=tY.active().getValue(t8);return t4.get(o)}setRootSpanAttribute(o,i){const s=tY.active().getValue(t8);const a=t4.get(s);if(a){a.set(o,i)}}}const nn=(()=>{const o=new nt;return()=>o})();function nr(o,i){return(...s)=>{getTracer().setRootSpanAttribute("next.route",o);return getTracer().trace(NodeSpan.runHandler,{spanName:`executing api route (pages) ${o}`},()=>i(...s))}}function no(o,i){o.statusCode=i;return o}function ni(o,i,s){if(typeof i==="string"){s=i;i=307}if(typeof i!=="number"||typeof s!=="string"){throw Object.defineProperty(new Error(`Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`),"__NEXT_ERROR_CODE",{value:"E389",enumerable:false,configurable:true})}o.writeHead(i,{Location:s});o.write(s);o.end();return o}function ns(o,i){const s=tE.from(o.headers);const a=s.get(k);const c=a===i.previewModeId;const u=s.has(x);return{isOnDemandRevalidate:c,revalidateOnlyGenerated:u}}const na=`__prerender_bypass`;const nc=`__next_preview_data`;const nu=null&&4*1024*1024;const nl=Symbol(nc);const nd=Symbol(na);function nf(o,i={}){if(nd in o){return o}const{serialize:a}=s(890);const c=o.getHeader("Set-Cookie");o.setHeader(`Set-Cookie`,[...typeof c==="string"?[c]:Array.isArray(c)?c:[],a(na,"",{expires:new Date(0),httpOnly:true,sameSite:true?"none":0,secure:"production"!=="development",path:"/",...i.path!==undefined?{path:i.path}:undefined}),a(nc,"",{expires:new Date(0),httpOnly:true,sameSite:true?"none":0,secure:"production"!=="development",path:"/",...i.path!==undefined?{path:i.path}:undefined})]);Object.defineProperty(o,nd,{value:true,enumerable:false});return o}class np extends Error{constructor(o,i){super(i);this.statusCode=o}}function nh(o,i,s){o.statusCode=i;o.statusMessage=s;o.end(s)}function ng({req:o},i,s){const a={configurable:true,enumerable:true};const c={...a,writable:true};Object.defineProperty(o,i,{...a,get:()=>{const a=s();Object.defineProperty(o,i,{...c,value:a});return a},set:s=>{Object.defineProperty(o,i,{...c,value:s})}})};class nm{constructor(o,i,s,a){var c;const u=o&&ns(i,o).isOnDemandRevalidate;const l=(c=s.get(na))==null?void 0:c.value;this._isEnabled=Boolean(!u&&l&&o&&(l===o.previewModeId||false&&0));this._previewModeId=o==null?void 0:o.previewModeId;this._mutableCookies=a}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId){throw Object.defineProperty(new Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:false,configurable:true})}this._mutableCookies.set({name:na,value:this._previewModeId,httpOnly:true,sameSite:true?"none":0,secure:"production"!=="development",path:"/"});this._isEnabled=true}disable(){this._mutableCookies.set({name:na,value:"",httpOnly:true,sameSite:true?"none":0,secure:"production"!=="development",path:"/",expires:new Date(0)});this._isEnabled=false}};function ny(o){const i=tE.from(o);for(const o of tr){i.delete(o.toLowerCase())}return tE.seal(i)}function nw(o,i){const s=new eG.RequestCookies(tE.from(o));return tO.wrap(s,i)}function nb(o,i){if("x-middleware-set-cookie"in o.headers&&typeof o.headers["x-middleware-set-cookie"]==="string"){const s=o.headers["x-middleware-set-cookie"];const a=new Headers;for(const o of ev(s)){a.append("set-cookie",o)}const c=new eG.ResponseCookies(a);for(const o of c.getAll()){i.set(o)}}}function n_(o,i,s,a,c,u,l,d,f,p){return nE("render",o,i,s,a,c,u,p,l,d,f)}function nv(o,i,s,a,c){return nE("action",o,undefined,i,{},s,a,undefined,c,false,undefined)}function nE(o,i,s,a,c,u,l,d,f,p,h){function g(o){if(s){s.setHeader("Set-Cookie",o)}}const m={};return{type:"request",phase:o,implicitTags:u,url:{pathname:a.pathname,search:a.search??""},rootParams:c,get headers(){if(!m.headers){m.headers=ny(i.headers)}return m.headers},get cookies(){if(!m.cookies){const o=new eG.RequestCookies(tE.from(i.headers));nb(i,o);m.cookies=tR.seal(o)}return m.cookies},set cookies(value){m.cookies=value},get mutableCookies(){if(!m.mutableCookies){const o=nw(i.headers,l||(s?g:undefined));nb(i,o);m.mutableCookies=o}return m.mutableCookies},get userspaceMutableCookies(){if(!m.userspaceMutableCookies){const o=tC(this.mutableCookies);m.userspaceMutableCookies=o}return m.userspaceMutableCookies},get draftMode(){if(!m.draftMode){m.draftMode=new nm(f,i,this.cookies,this.mutableCookies)}return m.draftMode},renderResumeDataCache:d??null,isHmrRefresh:p,serverComponentsHmrCache:h||globalThis.__serverComponentsHmrCache}}function nS(o){o.cookies=RequestCookiesAdapter.seal(responseCookiesToRequestCookies(o.mutableCookies))}var nk=s(802);var nx=s.n(nk);class nR extends Error{constructor(o,i){super("Invariant: "+(o.endsWith(".")?o:o+".")+" This is a bug in Next.js.",i);this.name="InvariantError"}};class nP{constructor(o,i){this.cache=new Map;this.sizes=new Map;this.totalSize=0;this.maxSize=o;this.calculateSize=i||(()=>1)}set(o,i){if(!o||!i)return;const s=this.calculateSize(i);if(s>this.maxSize){console.warn("Single item size exceeds maxSize");return}if(this.cache.has(o)){this.totalSize-=this.sizes.get(o)||0}this.cache.set(o,i);this.sizes.set(o,s);this.totalSize+=s;this.touch(o)}has(o){if(!o)return false;this.touch(o);return Boolean(this.cache.get(o))}get(o){if(!o)return;const i=this.cache.get(o);if(i===undefined){return undefined}this.touch(o);return i}touch(o){const i=this.cache.get(o);if(i!==undefined){this.cache.delete(o);this.cache.set(o,i);this.evictIfNecessary()}}evictIfNecessary(){while(this.totalSize>this.maxSize&&this.cache.size>0){this.evictLeastRecentlyUsed()}}evictLeastRecentlyUsed(){const o=this.cache.keys().next().value;if(o!==undefined){const i=this.sizes.get(o)||0;this.totalSize-=i;this.cache.delete(o);this.sizes.delete(o)}}reset(){this.cache.clear();this.sizes.clear();this.totalSize=0}keys(){return[...this.cache.keys()]}remove(o){if(this.cache.has(o)){this.totalSize-=this.sizes.get(o)||0;this.cache.delete(o);this.sizes.delete(o)}}clear(){this.cache.clear();this.sizes.clear();this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}};const nA=new Map;const nT=(o,i)=>{for(const s of o){const o=nA.get(s);if(typeof o==="number"&&o>=i){return true}}return false};var nO=s(356)["Buffer"];const nC=new nP(50*1024*1024,o=>o.size);const nI=new Map;const nj=process.env.NEXT_PRIVATE_DEBUG_CACHE?console.debug.bind(console,"DefaultCacheHandler:"):undefined;const nN={async get(o){const i=nI.get(o);if(i){nj==null?void 0:nj("get",o,"pending");await i}const s=nC.get(o);if(!s){nj==null?void 0:nj("get",o,"not found");return undefined}const a=s.entry;if(performance.timeOrigin+performance.now()>a.timestamp+a.revalidate*1e3){nj==null?void 0:nj("get",o,"expired");return undefined}if(nT(a.tags,a.timestamp)){nj==null?void 0:nj("get",o,"had stale tag");return undefined}const[c,u]=a.value.tee();a.value=u;nj==null?void 0:nj("get",o,"found",{tags:a.tags,timestamp:a.timestamp,revalidate:a.revalidate,expire:a.expire});return{...a,value:c}},async set(o,i){nj==null?void 0:nj("set",o,"start");let s=()=>{};const a=new Promise(o=>{s=o});nI.set(o,a);const c=await i;let u=0;try{const[i,s]=c.value.tee();c.value=i;const a=s.getReader();for(let o;!(o=await a.read()).done;){u+=nO.from(o.value).byteLength}nC.set(o,{entry:c,isErrored:false,errorRetryCount:0,size:u});nj==null?void 0:nj("set",o,"done")}catch(i){nj==null?void 0:nj("set",o,"failed",i)}finally{s();nI.delete(o)}},async refreshTags(){},async getExpiration(...o){const i=Math.max(...o.map(o=>nA.get(o)??0));nj==null?void 0:nj("getExpiration",{tags:o,expiration:i});return i},async expireTags(...o){const i=Math.round(performance.timeOrigin+performance.now());nj==null?void 0:nj("expireTags",{tags:o,timestamp:i});for(const s of o){nA.set(s,i)}}};const nD=null&&nN;const nU=process.env.NEXT_PRIVATE_DEBUG_CACHE?(o,...i)=>{console.log(`use-cache: ${o}`,...i)}:undefined;const n$=Symbol.for("@next/cache-handlers");const nL=Symbol.for("@next/cache-handlers-map");const nM=Symbol.for("@next/cache-handlers-set");const nH=globalThis;function nW(){if(nH[nL]){nU==null?void 0:nU("cache handlers already initialized");return false}nU==null?void 0:nU("initializing cache handlers");nH[nL]=new Map;if(nH[n$]){let o;if(nH[n$].DefaultCache){nU==null?void 0:nU('setting "default" cache handler from symbol');o=nH[n$].DefaultCache}else{nU==null?void 0:nU('setting "default" cache handler from default');o=DefaultCacheHandler}nH[nL].set("default",o);if(nH[n$].RemoteCache){nU==null?void 0:nU('setting "remote" cache handler from symbol');nH[nL].set("remote",nH[n$].RemoteCache)}else{nU==null?void 0:nU('setting "remote" cache handler from default');nH[nL].set("remote",o)}}else{nU==null?void 0:nU('setting "default" cache handler from default');nH[nL].set("default",DefaultCacheHandler);nU==null?void 0:nU('setting "remote" cache handler from default');nH[nL].set("remote",DefaultCacheHandler)}nH[nM]=new Set(nH[nL].values());return true}function nK(o){if(!nH[nL]){throw Object.defineProperty(new Error("Cache handlers not initialized"),"__NEXT_ERROR_CODE",{value:"E649",enumerable:false,configurable:true})}return nH[nL].get(o)}function nq(){if(!nH[nM]){return undefined}return nH[nM].values()}function nB(){if(!nH[nL]){return undefined}return nH[nL].entries()}function nJ(o,i){if(!nH[nL]||!nH[nM]){throw Object.defineProperty(new Error("Cache handlers not initialized"),"__NEXT_ERROR_CODE",{value:"E649",enumerable:false,configurable:true})}nU==null?void 0:nU('setting cache handler for "%s"',o);nH[nL].set(o,i);nH[nM].add(i)};async function nz(o,i){if(!o){return i()}const s=nV(o);try{return await i()}finally{const i=nG(s,nV(o));await nX(o,i)}}function nV(o){return{pendingRevalidatedTags:o.pendingRevalidatedTags?[...o.pendingRevalidatedTags]:[],pendingRevalidates:{...o.pendingRevalidates},pendingRevalidateWrites:o.pendingRevalidateWrites?[...o.pendingRevalidateWrites]:[]}}function nG(o,i){const s=new Set(o.pendingRevalidatedTags);const a=new Set(o.pendingRevalidateWrites);return{pendingRevalidatedTags:i.pendingRevalidatedTags.filter(o=>!s.has(o)),pendingRevalidates:Object.fromEntries(Object.entries(i.pendingRevalidates).filter(([i])=>!(i in o.pendingRevalidates))),pendingRevalidateWrites:i.pendingRevalidateWrites.filter(o=>!a.has(o))}}async function nF(o,i){if(o.length===0){return}const s=[];if(i){s.push(i.revalidateTag(o))}const a=nq();if(a){for(const i of a){s.push(i.expireTags(...o))}}await Promise.all(s)}async function nX(o,i){const s=(i==null?void 0:i.pendingRevalidatedTags)??o.pendingRevalidatedTags??[];const a=(i==null?void 0:i.pendingRevalidates)??o.pendingRevalidates??{};const c=(i==null?void 0:i.pendingRevalidateWrites)??o.pendingRevalidateWrites??[];return Promise.all([nF(s,o.incrementalCache),...Object.values(a),...c])};const nY=Object.defineProperty(new Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:false,configurable:true});class nZ{disable(){throw nY}getStore(){return undefined}run(){throw nY}exit(){throw nY}enterWith(){throw nY}static bind(o){return o}}const nQ=typeof globalThis!=="undefined"&&globalThis.AsyncLocalStorage;function n0(){if(nQ){return new nQ}return new nZ}function n1(o){if(nQ){return nQ.bind(o)}return nZ.bind(o)}function n2(){if(nQ){return nQ.snapshot()}return function(o,...i){return o(...i)}};const n5=n0();;class n3{constructor({waitUntil:o,onClose:i,onTaskError:s}){this.workUnitStores=new Set;this.waitUntil=o;this.onClose=i;this.onTaskError=s;this.callbackQueue=new(nx());this.callbackQueue.pause()}after(o){if(tF(o)){if(!this.waitUntil){n6()}this.waitUntil(o.catch(o=>this.reportTaskError("promise",o)))}else if(typeof o==="function"){this.addCallback(o)}else{throw Object.defineProperty(new Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:false,configurable:true})}}addCallback(o){if(!this.waitUntil){n6()}const i=tk.FP.getStore();if(i){this.workUnitStores.add(i)}const s=n5.getStore();const a=s?s.rootTaskSpawnPhase:i==null?void 0:i.phase;if(!this.runCallbacksOnClosePromise){this.runCallbacksOnClosePromise=this.runCallbacksOnClose();this.waitUntil(this.runCallbacksOnClosePromise)}const c=n1(async()=>{try{await n5.run({rootTaskSpawnPhase:a},()=>o())}catch(o){this.reportTaskError("function",o)}});this.callbackQueue.add(c)}async runCallbacksOnClose(){await new Promise(o=>this.onClose(o));return this.runCallbacks()}async runCallbacks(){if(this.callbackQueue.size===0)return;for(const o of this.workUnitStores){o.phase="after"}const o=tS.J.getStore();if(!o){throw Object.defineProperty(new nR("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:false,configurable:true})}return nz(o,()=>{this.callbackQueue.start();return this.callbackQueue.onIdle()})}reportTaskError(o,i){console.error(o==="promise"?`A promise passed to \`after()\` rejected:`:`An error occurred in a function passed to \`after()\`:`,i);if(this.onTaskError){try{this.onTaskError==null?void 0:this.onTaskError.call(this,i)}catch(o){console.error(Object.defineProperty(new nR("`onTaskError` threw while handling an error thrown from an `after` task",{cause:o}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:false,configurable:true}))}}}}function n6(){throw Object.defineProperty(new Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:false,configurable:true})};function n4(o){let i;const s={then(a,c){if(!i){i=o()}i.then(o=>{s.value=o}).catch(()=>{});return i.then(a,c)}};return s}function n8(o){return o.hasOwnProperty("value")};function n9({page:o,fallbackRouteParams:i,renderOpts:s,requestEndedState:a,isPrefetchRequest:c,buildId:u,previouslyRevalidatedTags:l}){const d=!s.shouldWaitOnAllReady&&!s.supportsDynamicResponse&&!s.isDraftMode&&!s.isPossibleServerAction;const f={isStaticGeneration:d,page:o,fallbackRouteParams:i,route:tb(o),incrementalCache:s.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:s.cacheLifeProfiles,isRevalidate:s.isRevalidate,isPrerendering:s.nextExport,fetchCache:s.fetchCache,isOnDemandRevalidate:s.isOnDemandRevalidate,isDraftMode:s.isDraftMode,requestEndedState:a,isPrefetchRequest:c,buildId:u,reactLoadableManifest:(s==null?void 0:s.reactLoadableManifest)||{},assetPrefix:(s==null?void 0:s.assetPrefix)||"",afterContext:n7(s),dynamicIOEnabled:s.experimental.dynamicIO,dev:s.dev??false,previouslyRevalidatedTags:l,refreshTagsByCacheKind:re()};s.store=f;return f}function n7(o){const{waitUntil:i,onClose:s,onAfterTaskError:a}=o;return new n3({waitUntil:i,onClose:s,onTaskError:a})}function re(){const o=new Map;const i=nB();if(i){for(const[s,a]of i){if("refreshTags"in a){o.set(s,n4(async()=>a.refreshTags()))}}}return o};function rt(o,i){if(typeof o==="string"){const s=async function* s(){const s=new TextEncoder;yield s.encode(o);i()};return s()}else{return rn(o,i)}}function rn(o,i){const s=new TransformStream;const a=()=>i();o.pipeTo(s.writable).then(a,a);return s.readable}class rr{onClose(o){if(this.isClosed){throw Object.defineProperty(new Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:false,configurable:true})}this.target.addEventListener("close",o);this.listeners++}dispatchClose(){if(this.isClosed){throw Object.defineProperty(new Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:false,configurable:true})}if(this.listeners>0){this.target.dispatchEvent(new Event("close"))}this.isClosed=true}constructor(){this.target=new EventTarget;this.listeners=0;this.isClosed=false}};function ro(){return{previewModeId:true?process.env.__NEXT_PREVIEW_MODE_ID:0,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}};function ri(){const o=globalThis;const i=o[rs];return i==null?void 0:i.get()}const rs=Symbol.for("@next/request-context");function ra(){const o=createAsyncLocalStorage();return{get:()=>o.getStore(),run:(i,s)=>o.run(i,s)}};const rc=o=>{const i=[`/layout`];if(o.startsWith("/")){const s=o.split("/");for(let o=1;o<s.length+1;o++){let a=s.slice(0,o).join("/");if(a){if(!a.endsWith("/page")&&!a.endsWith("/route")){a=`${a}${!a.endsWith("/")?"/":""}layout`}i.push(a)}}}return i};function ru(o){const i=new Map;const s=nB();if(s){for(const[a,c]of s){if("getExpiration"in c){i.set(a,n4(async()=>c.getExpiration(...o)))}}}return i}async function rl(o,i,s){const a=[];const c=s&&s.size>0;const u=rc(o);for(let o of u){o=`${W}${o}`;a.push(o)}if(i.pathname&&!c){const o=`${W}${i.pathname}`;a.push(o)}return{tags:a,expirationsByCacheKind:ru(a)}};class rd extends eX{constructor(o){super(o.input,o.init);this.sourcePage=o.page}get request(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}respondWith(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}waitUntil(){throw Object.defineProperty(new w({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true})}}const rf={keys:o=>Array.from(o.keys()),get:(o,i)=>o.get(i)??undefined};let rp=(o,i)=>{const s=nn();return s.withPropagatedContext(o.headers,i,rf)};let rh=false;function rg(){if(!rh){rh=true;if(process.env.NEXT_PRIVATE_TEST_PROXY==="true"){const{interceptTestApis:o,wrapRequestHandler:i}=s(905);o();rp=i(rp)}}}async function rm(o){var i;rg();await h();const s=typeof globalThis.__BUILD_MANIFEST!=="undefined";o.request.url=t_(o.request.url);const a=new eV(o.request.url,{headers:o.request.headers,nextConfig:o.request.nextConfig});const c=[...a.searchParams.keys()];for(const o of c){const i=a.searchParams.getAll(o);const s=ek(o);if(s){a.searchParams.delete(s);for(const o of i){a.searchParams.append(s,o)}a.searchParams.delete(o)}}const u=a.buildId;a.buildId="";const l=e_(o.request.headers);const d=l.has("x-nextjs-data");const f=l.get(e3)==="1";if(d&&a.pathname==="/index"){a.pathname="/"}const p=new Map;if(!s){for(const o of tr){const i=o.toLowerCase();const s=l.get(i);if(s!==null){p.set(i,s);l.delete(i)}}}const g=false?0:a;const m=new rd({page:o.page,input:tf(g).toString(),init:{body:o.request.body,headers:l,method:o.request.method,nextConfig:o.request.nextConfig,signal:o.request.signal}});if(d){Object.defineProperty(m,"__isData",{enumerable:false,value:true})}if(!globalThis.__incrementalCache&&o.IncrementalCache){;globalThis.__incrementalCache=new o.IncrementalCache({appDir:true,fetchCache:true,minimalMode:"production"!=="development",fetchCacheKeyPrefix:"",dev:"production"==="development",requestHeaders:o.request.headers,requestProtocol:"https",getPrerenderManifest:()=>{return{version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:ro()}}})}const y=o.request.waitUntil??((i=ri())==null?void 0:i.waitUntil);const w=new eO({request:m,page:o.page,context:y?{waitUntil:y}:undefined});let b;let _;b=await rp(m,()=>{const i=o.page==="/middleware"||o.page==="/src/middleware";if(i){const i=w.waitUntil.bind(w);const s=new rr;return nn().trace(tz.execute,{spanName:`middleware ${m.method} ${m.nextUrl.pathname}`,attributes:{"http.target":m.nextUrl.pathname,"http.method":m.method}},async()=>{try{var a,c,l,d;const f=o=>{_=o};const p=ro();const h="/";const g=null;const y=await rl(h,m.nextUrl,g);const b=nv(m,m.nextUrl,y,f,p);const v=n9({page:h,fallbackRouteParams:g,renderOpts:{cacheLifeProfiles:(c=o.request.nextConfig)==null?void 0:(a=c.experimental)==null?void 0:a.cacheLife,experimental:{isRoutePPREnabled:false,dynamicIO:false,authInterrupts:!!((d=o.request.nextConfig)==null?void 0:(l=d.experimental)==null?void 0:l.authInterrupts)},supportsDynamicResponse:true,waitUntil:i,onClose:s.onClose.bind(s),onAfterTaskError:undefined},requestEndedState:{ended:false},isPrefetchRequest:m.headers.has(e8),buildId:u??"",previouslyRevalidatedTags:[]});return await tS.J.run(v,()=>tk.FP.run(b,o.handler,m,w))}finally{setTimeout(()=>{s.dispatchClose()},0)}})}return o.handler(m,w)});if(b&&!(b instanceof Response)){throw Object.defineProperty(new TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:false,configurable:true})}if(b&&_){b.headers.set("set-cookie",_)}const v=b==null?void 0:b.headers.get("x-middleware-rewrite");if(b&&v&&(f||!s)){const i=new eV(v,{forceLocale:true,headers:o.request.headers,nextConfig:o.request.nextConfig});if(true&&!s){if(i.host===m.nextUrl.host){i.buildId=u||i.buildId;b.headers.set("x-middleware-rewrite",String(i))}}const{url:c,isRelative:l}=e2(i.toString(),a.toString());if(!s&&d&&!(false&&0)){b.headers.set("x-nextjs-rewrite",c)}if(f&&l){if(a.pathname!==i.pathname){b.headers.set(ta,i.pathname)}if(a.search!==i.search){b.headers.set(tc,i.search.slice(1))}}}const E=b==null?void 0:b.headers.get("Location");if(b&&E&&!s){const i=new eV(E,{forceLocale:false,headers:o.request.headers,nextConfig:o.request.nextConfig});b=new Response(b.body,b);if(true){if(i.host===a.host){i.buildId=u||i.buildId;b.headers.set("Location",i.toString())}}if(d){b.headers.delete("Location");b.headers.set("x-nextjs-redirect",e5(i.toString(),a.toString()))}}const S=b?b:e1.next();const k=S.headers.get("x-middleware-override-headers");const x=[];if(k){for(const[o,i]of p){S.headers.set(`x-middleware-request-${o}`,i);x.push(o)}if(x.length>0){S.headers.set("x-middleware-override-headers",k+","+x.join(","))}}return{response:S,waitUntil:eT(w)??Promise.resolve(),fetchMetrics:m.fetchMetrics}};var ry=undefined&&undefined.__classPrivateFieldSet||function(o,i,s,a,c){if(a==="m")throw new TypeError("Private method is not writable");if(a==="a"&&!c)throw new TypeError("Private accessor was defined without a setter");if(typeof i==="function"?o!==i||!c:!i.has(o))throw new TypeError("Cannot write private member to an object whose class did not declare it");return a==="a"?c.call(o,s):c?c.value=s:i.set(o,s),s};var rw=undefined&&undefined.__classPrivateFieldGet||function(o,i,s,a){if(s==="a"&&!a)throw new TypeError("Private accessor was defined without a getter");if(typeof i==="function"?o!==i||!a:!i.has(o))throw new TypeError("Cannot read private member from an object whose class did not declare it");return s==="m"?a:s==="a"?a.call(o):a?a.value:i.get(o)};var rb,r_,rv,rE,rS,rk;const rx=4096;const rR=160;const rP=rx-rR;function rA(o){const i=o?"__Secure-":"";return{sessionToken:{name:`${i}authjs.session-token`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},callbackUrl:{name:`${i}authjs.callback-url`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},csrfToken:{name:`${o?"__Host-":""}authjs.csrf-token`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},pkceCodeVerifier:{name:`${i}authjs.pkce.code_verifier`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o,maxAge:60*15}},state:{name:`${i}authjs.state`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o,maxAge:60*15}},nonce:{name:`${i}authjs.nonce`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o}},webauthnChallenge:{name:`${i}authjs.challenge`,options:{httpOnly:true,sameSite:"lax",path:"/",secure:o,maxAge:60*15}}}}class rT{constructor(o,i,s){rb.add(this);r_.set(this,{});rv.set(this,void 0);rE.set(this,void 0);ry(this,rE,s,"f");ry(this,rv,o,"f");if(!i)return;const{name:a}=o;for(const[o,s]of Object.entries(i)){if(!o.startsWith(a)||!s)continue;rw(this,r_,"f")[o]=s}}get value(){const o=Object.keys(rw(this,r_,"f")).sort((o,i)=>{const s=parseInt(o.split(".").pop()||"0");const a=parseInt(i.split(".").pop()||"0");return s-a});return o.map(o=>rw(this,r_,"f")[o]).join("")}chunk(o,i){const s=rw(this,rb,"m",rk).call(this);const a=rw(this,rb,"m",rS).call(this,{name:rw(this,rv,"f").name,value:o,options:{...rw(this,rv,"f").options,...i}});for(const o of a){s[o.name]=o}return Object.values(s)}clean(){return Object.values(rw(this,rb,"m",rk).call(this))}}r_=new WeakMap,rv=new WeakMap,rE=new WeakMap,rb=new WeakSet,rS=function o(o){const i=Math.ceil(o.value.length/rP);if(i===1){rw(this,r_,"f")[o.name]=o.value;return[o]}const s=[];for(let a=0;a<i;a++){const i=`${o.name}.${a}`;const c=o.value.substr(a*rP,rP);s.push({...o,name:i,value:c});rw(this,r_,"f")[i]=c}rw(this,rE,"f").debug("CHUNKING_SESSION_COOKIE",{message:`Session cookie exceeds allowed ${rx} bytes.`,emptyCookieSize:rR,valueSize:o.value.length,chunks:s.map(o=>o.value.length+rR)});return s},rk=function o(){const o={};for(const i in rw(this,r_,"f")){delete rw(this,r_,"f")?.[i];o[i]={name:i,value:"",options:{...rw(this,rv,"f").options,maxAge:0}}}return o};class rO extends Error{constructor(o,i){if(o instanceof Error){super(undefined,{cause:{err:o,...o.cause,...i}})}else if(typeof o==="string"){if(i instanceof Error){i={err:i,...i.cause}}super(o,i)}else{super(undefined,o)}this.name=this.constructor.name;this.type=this.constructor.type??"AuthError";this.kind=this.constructor.kind??"error";Error.captureStackTrace?.(this,this.constructor);const s=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${s}`}}class rC extends rO{}rC.kind="signIn";class rI extends rO{}rI.type="AdapterError";class rj extends rO{}rj.type="AccessDenied";class rN extends rO{}rN.type="CallbackRouteError";class rD extends rO{}rD.type="ErrorPageLoop";class rU extends rO{}rU.type="EventError";class r$ extends rO{}r$.type="InvalidCallbackUrl";class rL extends rC{constructor(){super(...arguments);this.code="credentials"}}rL.type="CredentialsSignin";class rM extends rO{}rM.type="InvalidEndpoints";class rH extends rO{}rH.type="InvalidCheck";class rW extends rO{}rW.type="JWTSessionError";class rK extends rO{}rK.type="MissingAdapter";class rq extends rO{}rq.type="MissingAdapterMethods";class rB extends rO{}rB.type="MissingAuthorize";class rJ extends rO{}rJ.type="MissingSecret";class rz extends rC{}rz.type="OAuthAccountNotLinked";class rV extends rC{}rV.type="OAuthCallbackError";class rG extends rO{}rG.type="OAuthProfileParseError";class rF extends rO{}rF.type="SessionTokenError";class rX extends rC{}rX.type="OAuthSignInError";class rY extends rC{}rY.type="EmailSignInError";class rZ extends rO{}rZ.type="SignOutError";class rQ extends rO{}rQ.type="UnknownAction";class r0 extends rO{}r0.type="UnsupportedStrategy";class r1 extends rO{}r1.type="InvalidProvider";class r2 extends rO{}r2.type="UntrustedHost";class r5 extends rO{}r5.type="Verification";class r3 extends rC{}r3.type="MissingCSRF";const r6=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function r4(o){if(o instanceof rO)return r6.has(o.type);return false}class r8 extends rO{}r8.type="DuplicateConditionalUI";class r9 extends rO{}r9.type="MissingWebAuthnAutocomplete";class r7 extends rO{}r7.type="WebAuthnVerificationError";class oe extends rC{}oe.type="AccountNotLinked";class ot extends rO{}ot.type="ExperimentalFeatureNotEnabled";let on=false;function or(o,i){try{return/^https?:/.test(new URL(o,o.startsWith("/")?i:undefined).protocol)}catch{return false}}function oo(o){return/^v\d+(?:\.\d+){0,2}$/.test(o)}let oi=false;let os=false;let oa=false;const oc=["createVerificationToken","useVerificationToken","getUserByEmail"];const ou=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"];const ol=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];function od(o,i){const{url:s}=o;const a=[];if(!on&&i.debug)a.push("debug-enabled");if(!i.trustHost){return new r2(`Host must be trusted. URL was: ${o.url}`)}if(!i.secret?.length){return new rJ("Please define a `secret`")}const c=o.query?.callbackUrl;if(c&&!or(c,s.origin)){return new r$(`Invalid callback URL. Received: ${c}`)}const{callbackUrl:u}=rA(i.useSecureCookies??s.protocol==="https:");const l=o.cookies?.[i.cookies?.callbackUrl?.name??u.name];if(l&&!or(l,s.origin)){return new r$(`Invalid callback URL. Received: ${l}`)}let d=false;for(const o of i.providers){const i=typeof o==="function"?o():o;if((i.type==="oauth"||i.type==="oidc")&&!(i.issuer??i.options?.issuer)){const{authorization:o,token:s,userinfo:a}=i;let c;if(typeof o!=="string"&&!o?.url)c="authorization";else if(typeof s!=="string"&&!s?.url)c="token";else if(typeof a!=="string"&&!a?.url)c="userinfo";if(c){return new rM(`Provider "${i.id}" is missing both \`issuer\` and \`${c}\` endpoint config. At least one of them is required`)}}if(i.type==="credentials")oi=true;else if(i.type==="email")os=true;else if(i.type==="webauthn"){oa=true;if(i.simpleWebAuthnBrowserVersion&&!oo(i.simpleWebAuthnBrowserVersion)){return new rO(`Invalid provider config for "${i.id}": simpleWebAuthnBrowserVersion "${i.simpleWebAuthnBrowserVersion}" must be a valid semver string.`)}if(i.enableConditionalUI){if(d){return new r8(`Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time`)}d=true;const o=Object.values(i.formFields).some(o=>o.autocomplete&&o.autocomplete.toString().indexOf("webauthn")>-1);if(!o){return new r9(`Provider "${i.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}}if(oi){const o=i.session?.strategy==="database";const s=!i.providers.some(o=>(typeof o==="function"?o():o).type!=="credentials");if(o&&s){return new r0("Signing in with credentials only supported if JWT strategy is enabled")}const a=i.providers.some(o=>{const i=typeof o==="function"?o():o;return i.type==="credentials"&&!i.authorize});if(a){return new rB("Must define an authorize() handler to use credentials authentication provider")}}const{adapter:f,session:p}=i;const h=[];if(os||p?.strategy==="database"||!p?.strategy&&f){if(os){if(!f)return new rK("Email login requires an adapter");h.push(...oc)}else{if(!f)return new rK("Database session requires an adapter");h.push(...ou)}}if(oa){if(i.experimental?.enableWebAuthn){a.push("experimental-webauthn")}else{return new ot("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config")}if(!f)return new rK("WebAuthn requires an adapter");h.push(...ol)}if(f){const o=h.filter(o=>!(o in f));if(o.length){return new rq(`Required adapter methods were missing: ${o.join(", ")}`)}}if(!on)on=true;return a};const of=()=>{if(typeof globalThis!=="undefined")return globalThis;if(typeof self!=="undefined")return self;if(typeof window!=="undefined")return window;throw new Error("unable to locate global object")};const op=async(o,i,s,a,c)=>{const{crypto:{subtle:u}}=of();return new Uint8Array(await u.deriveBits({name:"HKDF",hash:`SHA-${o.substr(3)}`,salt:s,info:a},await u.importKey("raw",i,"HKDF",false,["deriveBits"]),c<<3))};function oh(o){switch(o){case"sha256":case"sha384":case"sha512":case"sha1":return o;default:throw new TypeError('unsupported "digest" value')}}function og(o,i){if(typeof o==="string")return new TextEncoder().encode(o);if(!(o instanceof Uint8Array))throw new TypeError(`"${i}"" must be an instance of Uint8Array or a string`);return o}function om(o){const i=og(o,"ikm");if(!i.byteLength)throw new TypeError(`"ikm" must be at least one byte in length`);return i}function oy(o){const i=og(o,"info");if(i.byteLength>1024){throw TypeError('"info" must not contain more than 1024 bytes')}return i}function ow(o,i){if(typeof o!=="number"||!Number.isInteger(o)||o<1){throw new TypeError('"keylen" must be a positive integer')}const s=parseInt(i.substr(3),10)>>3||20;if(o>255*s){throw new TypeError('"keylen" too large')}return o}async function ob(o,i,s,a,c){return op(oh(o),om(i),og(s,"salt"),oy(a),ow(c,o))};const o_=async(o,i)=>{const s=`SHA-${o.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(s,i))};const ov=new TextEncoder;const oE=new TextDecoder;const oS=2**32;function ok(...o){const i=o.reduce((o,{length:i})=>o+i,0);const s=new Uint8Array(i);let a=0;for(const i of o){s.set(i,a);a+=i.length}return s}function ox(o,i,s){if(i<0||i>=oS){throw new RangeError(`value must be >= 0 and <= ${oS-1}. Received ${i}`)}o.set([i>>>24,i>>>16,i>>>8,i&255],s)}function oR(o){const i=Math.floor(o/oS);const s=o%oS;const a=new Uint8Array(8);ox(a,i,0);ox(a,s,4);return a}function oP(o){const i=new Uint8Array(4);ox(i,o);return i};function oA(o){if(Uint8Array.prototype.toBase64){return o.toBase64()}const i=32768;const s=[];for(let a=0;a<o.length;a+=i){s.push(String.fromCharCode.apply(null,o.subarray(a,a+i)))}return btoa(s.join(""))}function oT(o){if(Uint8Array.fromBase64){return Uint8Array.fromBase64(o)}const i=atob(o);const s=new Uint8Array(i.length);for(let o=0;o<i.length;o++){s[o]=i.charCodeAt(o)}return s};function oO(o){if(Uint8Array.fromBase64){return Uint8Array.fromBase64(typeof o==="string"?o:oE.decode(o),{alphabet:"base64url"})}let i=o;if(i instanceof Uint8Array){i=oE.decode(i)}i=i.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return oT(i)}catch{throw new TypeError("The input to be decoded is not correctly encoded.")}}function oC(o){let i=o;if(typeof i==="string"){i=ov.encode(i)}if(Uint8Array.prototype.toBase64){return i.toBase64({alphabet:"base64url",omitPadding:true})}return oA(i).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")};class oI extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(o,i){super(o,i);this.name=this.constructor.name;Error.captureStackTrace?.(this,this.constructor)}}class oj extends oI{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(o,i,s="unspecified",a="unspecified"){super(o,{cause:{claim:s,reason:a,payload:i}});this.claim=s;this.reason=a;this.payload=i}}class oN extends oI{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(o,i,s="unspecified",a="unspecified"){super(o,{cause:{claim:s,reason:a,payload:i}});this.claim=s;this.reason=a;this.payload=i}}class oD extends oI{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class oU extends oI{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class o$ extends oI{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(o="decryption operation failed",i){super(o,i)}}class oL extends oI{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class oM extends(null&&oI){static code=null&&"ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class oH extends oI{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class oW extends oI{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class oK extends(null&&oI){static code=null&&"ERR_JWKS_INVALID";code="ERR_JWKS_INVALID"}class oq extends oI{static code="ERR_JWKS_NO_MATCHING_KEY";code="ERR_JWKS_NO_MATCHING_KEY";constructor(o="no applicable key found in the JSON Web Key Set",i){super(o,i)}}class oB extends oI{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(o="multiple matching keys found in the JSON Web Key Set",i){super(o,i)}}class oJ extends oI{static code="ERR_JWKS_TIMEOUT";code="ERR_JWKS_TIMEOUT";constructor(o="request timed out",i){super(o,i)}}class oz extends oI{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(o="signature verification failed",i){super(o,i)}};function oV(o){if(!oG(o)){throw new Error("CryptoKey instance expected")}}function oG(o){return o?.[Symbol.toStringTag]==="CryptoKey"}function oF(o){return o?.[Symbol.toStringTag]==="KeyObject"}const oX=o=>{return oG(o)||oF(o)};function oY(o){return typeof o==="object"&&o!==null}const oZ=o=>{if(!oY(o)||Object.prototype.toString.call(o)!=="[object Object]"){return false}if(Object.getPrototypeOf(o)===null){return true}let i=o;while(Object.getPrototypeOf(i)!==null){i=Object.getPrototypeOf(i)}return Object.getPrototypeOf(o)===i};function oQ(o){return oZ(o)&&typeof o.kty==="string"}function o0(o){return o.kty!=="oct"&&typeof o.d==="string"}function o1(o){return o.kty!=="oct"&&typeof o.d==="undefined"}function o2(o){return o.kty==="oct"&&typeof o.k==="string"};function o5(o,i,...s){s=s.filter(Boolean);if(s.length>2){const i=s.pop();o+=`one of type ${s.join(", ")}, or ${i}.`}else if(s.length===2){o+=`one of type ${s[0]} or ${s[1]}.`}else{o+=`of type ${s[0]}.`}if(i==null){o+=` Received ${i}`}else if(typeof i==="function"&&i.name){o+=` Received function ${i.name}`}else if(typeof i==="object"&&i!=null){if(i.constructor?.name){o+=` Received an instance of ${i.constructor.name}`}}return o}const o3=(o,...i)=>{return o5("Key must be ",o,...i)};function o6(o,i,...s){return o5(`Key for the ${o} algorithm must be `,i,...s)};async function o4(o){if(oF(o)){if(o.type==="secret"){o=o.export()}else{return o.export({format:"jwk"})}}if(o instanceof Uint8Array){return{kty:"oct",k:oC(o)}}if(!oG(o)){throw new TypeError(o3(o,"CryptoKey","KeyObject","Uint8Array"))}if(!o.extractable){throw new TypeError("non-extractable CryptoKey cannot be exported as a JWK")}const{ext:i,key_ops:s,alg:a,use:c,...u}=await crypto.subtle.exportKey("jwk",o);return u};async function o8(o){return exportPublic(o)}async function o9(o){return exportPrivate(o)}async function o7(o){return o4(o)};const ie=(o,i)=>{if(typeof o!=="string"||!o){throw new oW(`${i} missing or invalid`)}};async function it(o,i){let s;if(oQ(o)){s=o}else if(oX(o)){s=await o7(o)}else{throw new TypeError(o3(o,"CryptoKey","KeyObject","JSON Web Key"))}i??="sha256";if(i!=="sha256"&&i!=="sha384"&&i!=="sha512"){throw new TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"')}let a;switch(s.kty){case"EC":ie(s.crv,'"crv" (Curve) Parameter');ie(s.x,'"x" (X Coordinate) Parameter');ie(s.y,'"y" (Y Coordinate) Parameter');a={crv:s.crv,kty:s.kty,x:s.x,y:s.y};break;case"OKP":ie(s.crv,'"crv" (Subtype of Key Pair) Parameter');ie(s.x,'"x" (Public Key) Parameter');a={crv:s.crv,kty:s.kty,x:s.x};break;case"RSA":ie(s.e,'"e" (Exponent) Parameter');ie(s.n,'"n" (Modulus) Parameter');a={e:s.e,kty:s.kty,n:s.n};break;case"oct":ie(s.k,'"k" (Key Value) Parameter');a={k:s.k,kty:s.kty};break;default:throw new oU('"kty" (Key Type) Parameter missing or unsupported')}const c=ov.encode(JSON.stringify(a));return oC(await o_(i,c))}async function ir(o,i){i??="sha256";const s=await it(o,i);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${i.slice(-3)}:${s}`};const io=Symbol();function ii(o){switch(o){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new oU(`Unsupported JWE Algorithm: ${o}`)}}const is=o=>crypto.getRandomValues(new Uint8Array(ii(o)>>3));const ia=(o,i)=>{if(i.length<<3!==ii(o)){throw new oL("Invalid Initialization Vector length")}};const ic=(o,i)=>{const s=o.byteLength<<3;if(s!==i){throw new oL(`Invalid Content Encryption Key length. Expected ${i} bits, got ${s} bits`)}};function iu(o,i="algorithm.name"){return new TypeError(`CryptoKey does not support this operation, its ${i} must be ${o}`)}function il(o,i){return o.name===i}function id(o){return parseInt(o.name.slice(4),10)}function ip(o){switch(o){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw new Error("unreachable")}}function ih(o,i){if(i&&!o.usages.includes(i)){throw new TypeError(`CryptoKey does not support this operation, its usages must include ${i}.`)}}function ig(o,i,s){switch(i){case"HS256":case"HS384":case"HS512":{if(!il(o.algorithm,"HMAC"))throw iu("HMAC");const s=parseInt(i.slice(2),10);const a=id(o.algorithm.hash);if(a!==s)throw iu(`SHA-${s}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!il(o.algorithm,"RSASSA-PKCS1-v1_5"))throw iu("RSASSA-PKCS1-v1_5");const s=parseInt(i.slice(2),10);const a=id(o.algorithm.hash);if(a!==s)throw iu(`SHA-${s}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!il(o.algorithm,"RSA-PSS"))throw iu("RSA-PSS");const s=parseInt(i.slice(2),10);const a=id(o.algorithm.hash);if(a!==s)throw iu(`SHA-${s}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":{if(!il(o.algorithm,"Ed25519"))throw iu("Ed25519");break}case"ES256":case"ES384":case"ES512":{if(!il(o.algorithm,"ECDSA"))throw iu("ECDSA");const s=ip(i);const a=o.algorithm.namedCurve;if(a!==s)throw iu(s,"algorithm.namedCurve");break}default:throw new TypeError("CryptoKey does not support this operation")}ih(o,s)}function im(o,i,s){switch(i){case"A128GCM":case"A192GCM":case"A256GCM":{if(!il(o.algorithm,"AES-GCM"))throw iu("AES-GCM");const s=parseInt(i.slice(1,4),10);const a=o.algorithm.length;if(a!==s)throw iu(s,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!il(o.algorithm,"AES-KW"))throw iu("AES-KW");const s=parseInt(i.slice(1,4),10);const a=o.algorithm.length;if(a!==s)throw iu(s,"algorithm.length");break}case"ECDH":{switch(o.algorithm.name){case"ECDH":case"X25519":break;default:throw iu("ECDH or X25519")}break}case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!il(o.algorithm,"PBKDF2"))throw iu("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!il(o.algorithm,"RSA-OAEP"))throw iu("RSA-OAEP");const s=parseInt(i.slice(9),10)||1;const a=id(o.algorithm.hash);if(a!==s)throw iu(`SHA-${s}`,"algorithm.hash");break}default:throw new TypeError("CryptoKey does not support this operation")}ih(o,s)};async function iy(o,i,s,a,c){if(!(s instanceof Uint8Array)){throw new TypeError(o3(s,"Uint8Array"))}const u=parseInt(o.slice(1,4),10);const l=await crypto.subtle.importKey("raw",s.subarray(u>>3),"AES-CBC",false,["encrypt"]);const d=await crypto.subtle.importKey("raw",s.subarray(0,u>>3),{hash:`SHA-${u<<1}`,name:"HMAC"},false,["sign"]);const f=new Uint8Array(await crypto.subtle.encrypt({iv:a,name:"AES-CBC"},l,i));const p=ok(c,a,f,oR(c.length<<3));const h=new Uint8Array((await crypto.subtle.sign("HMAC",d,p)).slice(0,u>>3));return{ciphertext:f,tag:h,iv:a}}async function iw(o,i,s,a,c){let u;if(s instanceof Uint8Array){u=await crypto.subtle.importKey("raw",s,"AES-GCM",false,["encrypt"])}else{im(s,o,"encrypt");u=s}const l=new Uint8Array(await crypto.subtle.encrypt({additionalData:c,iv:a,name:"AES-GCM",tagLength:128},u,i));const d=l.slice(-16);const f=l.slice(0,-16);return{ciphertext:f,tag:d,iv:a}}const ib=async(o,i,s,a,c)=>{if(!oG(s)&&!(s instanceof Uint8Array)){throw new TypeError(o3(s,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"))}if(a){ia(o,a)}else{a=is(o)}switch(o){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":if(s instanceof Uint8Array){ic(s,parseInt(o.slice(-3),10))}return iy(o,i,s,a,c);case"A128GCM":case"A192GCM":case"A256GCM":if(s instanceof Uint8Array){ic(s,parseInt(o.slice(1,4),10))}return iw(o,i,s,a,c);default:throw new oU("Unsupported JWE Content Encryption Algorithm")}};function i_(o,i){if(o.algorithm.length!==parseInt(i.slice(1,4),10)){throw new TypeError(`Invalid key size for alg: ${i}`)}}function iv(o,i,s){if(o instanceof Uint8Array){return crypto.subtle.importKey("raw",o,"AES-KW",true,[s])}im(o,i,s);return o}async function iE(o,i,s){const a=await iv(i,o,"wrapKey");i_(a,o);const c=await crypto.subtle.importKey("raw",s,{hash:"SHA-256",name:"HMAC"},true,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",c,a,"AES-KW"))}async function iS(o,i,s){const a=await iv(i,o,"unwrapKey");i_(a,o);const c=await crypto.subtle.unwrapKey("raw",s,a,"AES-KW",{hash:"SHA-256",name:"HMAC"},true,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",c))};function ik(o){return ok(oP(o.length),o)}async function ix(o,i,s){const a=Math.ceil((i>>3)/32);const c=new Uint8Array(a*32);for(let i=0;i<a;i++){const a=new Uint8Array(4+o.length+s.length);a.set(oP(i+1));a.set(o,4);a.set(s,4+o.length);c.set(await o_("sha256",a),i*32)}return c.slice(0,i>>3)}async function iR(o,i,s,a,c=new Uint8Array(0),u=new Uint8Array(0)){im(o,"ECDH");im(i,"ECDH","deriveBits");const l=ok(ik(ov.encode(s)),ik(c),ik(u),oP(a));let d;if(o.algorithm.name==="X25519"){d=256}else{d=Math.ceil(parseInt(o.algorithm.namedCurve.slice(-3),10)/8)<<3}const f=new Uint8Array(await crypto.subtle.deriveBits({name:o.algorithm.name,public:o},i,d));return ix(f,a,l)}function iP(o){switch(o.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return true;default:return o.algorithm.name==="X25519"}};function iA(o,i){if(o instanceof Uint8Array){return crypto.subtle.importKey("raw",o,"PBKDF2",false,["deriveBits"])}im(o,i,"deriveBits");return o}const iT=(o,i)=>ok(ov.encode(o),new Uint8Array([0]),i);async function iO(o,i,s,a){if(!(o instanceof Uint8Array)||o.length<8){throw new oL("PBES2 Salt Input must be 8 or more octets")}const c=iT(i,o);const u=parseInt(i.slice(13,16),10);const l={hash:`SHA-${i.slice(8,11)}`,iterations:s,name:"PBKDF2",salt:c};const d=await iA(a,i);return new Uint8Array(await crypto.subtle.deriveBits(l,d,u))}async function iC(o,i,s,a=2048,c=crypto.getRandomValues(new Uint8Array(16))){const u=await iO(c,o,a,i);const l=await iE(o.slice(-6),u,s);return{encryptedKey:l,p2c:a,p2s:oC(c)}}async function iI(o,i,s,a,c){const u=await iO(c,o,a,i);return iS(o.slice(-6),u,s)};const ij=(o,i)=>{if(o.startsWith("RS")||o.startsWith("PS")){const{modulusLength:s}=i.algorithm;if(typeof s!=="number"||s<2048){throw new TypeError(`${o} requires key modulusLength to be 2048 bits or larger`)}}};const iN=o=>{switch(o){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new oU(`alg ${o} is not supported either by JOSE or your javascript runtime`)}};async function iD(o,i,s){im(i,o,"encrypt");ij(o,i);return new Uint8Array(await crypto.subtle.encrypt(iN(o),i,s))}async function iU(o,i,s){im(i,o,"decrypt");ij(o,i);return new Uint8Array(await crypto.subtle.decrypt(iN(o),i,s))};function i$(o){let i;let s;switch(o.kty){case"RSA":{switch(o.alg){case"PS256":case"PS384":case"PS512":i={name:"RSA-PSS",hash:`SHA-${o.alg.slice(-3)}`};s=o.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":i={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${o.alg.slice(-3)}`};s=o.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":i={name:"RSA-OAEP",hash:`SHA-${parseInt(o.alg.slice(-3),10)||1}`};s=o.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new oU('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}case"EC":{switch(o.alg){case"ES256":i={name:"ECDSA",namedCurve:"P-256"};s=o.d?["sign"]:["verify"];break;case"ES384":i={name:"ECDSA",namedCurve:"P-384"};s=o.d?["sign"]:["verify"];break;case"ES512":i={name:"ECDSA",namedCurve:"P-521"};s=o.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":i={name:"ECDH",namedCurve:o.crv};s=o.d?["deriveBits"]:[];break;default:throw new oU('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}case"OKP":{switch(o.alg){case"Ed25519":case"EdDSA":i={name:"Ed25519"};s=o.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":i={name:o.crv};s=o.d?["deriveBits"]:[];break;default:throw new oU('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break}default:throw new oU('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:i,keyUsages:s}}const iL=async o=>{if(!o.alg){throw new TypeError('"alg" argument is required when "jwk.alg" is not present')}const{algorithm:i,keyUsages:s}=i$(o);const a={...o};delete a.alg;delete a.use;return crypto.subtle.importKey("jwk",a,i,o.ext??(o.d?false:true),o.key_ops??s)};let iM;const iH=async(o,i,s,a=false)=>{iM||=new WeakMap;let c=iM.get(o);if(c?.[s]){return c[s]}const u=await iL({...i,alg:s});if(a)Object.freeze(o);if(!c){iM.set(o,{[s]:u})}else{c[s]=u}return u};const iW=(o,i)=>{iM||=new WeakMap;let s=iM.get(o);if(s?.[i]){return s[i]}const a=o.type==="public";const c=a?true:false;let u;if(o.asymmetricKeyType==="x25519"){switch(i){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw new TypeError("given KeyObject instance cannot be used for this algorithm")}u=o.toCryptoKey(o.asymmetricKeyType,c,a?[]:["deriveBits"])}if(o.asymmetricKeyType==="ed25519"){if(i!=="EdDSA"&&i!=="Ed25519"){throw new TypeError("given KeyObject instance cannot be used for this algorithm")}u=o.toCryptoKey(o.asymmetricKeyType,c,[a?"verify":"sign"])}if(o.asymmetricKeyType==="rsa"){let s;switch(i){case"RSA-OAEP":s="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":s="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":s="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":s="SHA-512";break;default:throw new TypeError("given KeyObject instance cannot be used for this algorithm")}if(i.startsWith("RSA-OAEP")){return o.toCryptoKey({name:"RSA-OAEP",hash:s},c,a?["encrypt"]:["decrypt"])}u=o.toCryptoKey({name:i.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:s},c,[a?"verify":"sign"])}if(o.asymmetricKeyType==="ec"){const s=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]);const l=s.get(o.asymmetricKeyDetails?.namedCurve);if(!l){throw new TypeError("given KeyObject instance cannot be used for this algorithm")}if(i==="ES256"&&l==="P-256"){u=o.toCryptoKey({name:"ECDSA",namedCurve:l},c,[a?"verify":"sign"])}if(i==="ES384"&&l==="P-384"){u=o.toCryptoKey({name:"ECDSA",namedCurve:l},c,[a?"verify":"sign"])}if(i==="ES512"&&l==="P-521"){u=o.toCryptoKey({name:"ECDSA",namedCurve:l},c,[a?"verify":"sign"])}if(i.startsWith("ECDH-ES")){u=o.toCryptoKey({name:"ECDH",namedCurve:l},c,a?[]:["deriveBits"])}}if(!u){throw new TypeError("given KeyObject instance cannot be used for this algorithm")}if(!s){iM.set(o,{[i]:u})}else{s[i]=u}return u};const iK=async(o,i)=>{if(o instanceof Uint8Array){return o}if(oG(o)){return o}if(oF(o)){if(o.type==="secret"){return o.export()}if("toCryptoKey"in o&&typeof o.toCryptoKey==="function"){try{return iW(o,i)}catch(o){if(o instanceof TypeError){throw o}}}let s=o.export({format:"jwk"});return iH(o,s,i)}if(oQ(o)){if(o.k){return oO(o.k)}return iH(o,o,i,true)}throw new Error("unreachable")};function iq(o){switch(o){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new oU(`Unsupported JWE Algorithm: ${o}`)}}const iB=o=>crypto.getRandomValues(new Uint8Array(iq(o)>>3));async function iJ(o,i){if(!(o instanceof Uint8Array)){throw new TypeError("First argument must be a buffer")}if(!(i instanceof Uint8Array)){throw new TypeError("Second argument must be a buffer")}const s={name:"HMAC",hash:"SHA-256"};const a=await crypto.subtle.generateKey(s,false,["sign"]);const c=new Uint8Array(await crypto.subtle.sign(s,a,o));const u=new Uint8Array(await crypto.subtle.sign(s,a,i));let l=0;let d=-1;while(++d<32){l|=c[d]^u[d]}return l===0}async function iz(o,i,s,a,c,u){if(!(i instanceof Uint8Array)){throw new TypeError(o3(i,"Uint8Array"))}const l=parseInt(o.slice(1,4),10);const d=await crypto.subtle.importKey("raw",i.subarray(l>>3),"AES-CBC",false,["decrypt"]);const f=await crypto.subtle.importKey("raw",i.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},false,["sign"]);const p=ok(u,a,s,oR(u.length<<3));const h=new Uint8Array((await crypto.subtle.sign("HMAC",f,p)).slice(0,l>>3));let g;try{g=await iJ(c,h)}catch{}if(!g){throw new o$}let m;try{m=new Uint8Array(await crypto.subtle.decrypt({iv:a,name:"AES-CBC"},d,s))}catch{}if(!m){throw new o$}return m}async function iV(o,i,s,a,c,u){let l;if(i instanceof Uint8Array){l=await crypto.subtle.importKey("raw",i,"AES-GCM",false,["decrypt"])}else{im(i,o,"decrypt");l=i}try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:u,iv:a,name:"AES-GCM",tagLength:128},l,ok(s,c)))}catch{throw new o$}}const iG=async(o,i,s,a,c,u)=>{if(!oG(i)&&!(i instanceof Uint8Array)){throw new TypeError(o3(i,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"))}if(!a){throw new oL("JWE Initialization Vector missing")}if(!c){throw new oL("JWE Authentication Tag missing")}ia(o,a);switch(o){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":if(i instanceof Uint8Array)ic(i,parseInt(o.slice(-3),10));return iz(o,i,s,a,c,u);case"A128GCM":case"A192GCM":case"A256GCM":if(i instanceof Uint8Array)ic(i,parseInt(o.slice(1,4),10));return iV(o,i,s,a,c,u);default:throw new oU("Unsupported JWE Content Encryption Algorithm")}};async function iF(o,i,s,a){const c=o.slice(0,7);const u=await ib(c,s,i,a,new Uint8Array(0));return{encryptedKey:u.ciphertext,iv:oC(u.iv),tag:oC(u.tag)}}async function iX(o,i,s,a,c){const u=o.slice(0,7);return iG(u,i,s,a,c,new Uint8Array(0))};const iY=async(o,i,s,a,c={})=>{let u;let l;let d;switch(o){case"dir":{d=s;break}case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{oV(s);if(!iP(s)){throw new oU("ECDH with the provided key is not allowed or not supported by your javascript runtime")}const{apu:f,apv:p}=c;let h;if(c.epk){h=await iK(c.epk,o)}else{h=(await crypto.subtle.generateKey(s.algorithm,true,["deriveBits"])).privateKey}const{x:g,y:m,crv:y,kty:w}=await o7(h);const b=await iR(s,h,o==="ECDH-ES"?i:o,o==="ECDH-ES"?iq(i):parseInt(o.slice(-5,-2),10),f,p);l={epk:{x:g,crv:y,kty:w}};if(w==="EC")l.epk.y=m;if(f)l.apu=oC(f);if(p)l.apv=oC(p);if(o==="ECDH-ES"){d=b;break}d=a||iB(i);const _=o.slice(-6);u=await iE(_,b,d);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{d=a||iB(i);oV(s);u=await iD(o,s,d);break}case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{d=a||iB(i);const{p2c:f,p2s:p}=c;({encryptedKey:u,...l}=await iC(o,s,d,f,p));break}case"A128KW":case"A192KW":case"A256KW":{d=a||iB(i);u=await iE(o,s,d);break}case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{d=a||iB(i);const{iv:f}=c;({encryptedKey:u,...l}=await iF(o,s,d,f));break}default:{throw new oU('Invalid or unsupported "alg" (JWE Algorithm) header value')}}return{cek:d,encryptedKey:u,parameters:l}};const iZ=(...o)=>{const i=o.filter(Boolean);if(i.length===0||i.length===1){return true}let s;for(const o of i){const i=Object.keys(o);if(!s||s.size===0){s=new Set(i);continue}for(const o of i){if(s.has(o)){return false}s.add(o)}}return true};const iQ=(o,i,s,a,c)=>{if(c.crit!==undefined&&a?.crit===undefined){throw new o('"crit" (Critical) Header Parameter MUST be integrity protected')}if(!a||a.crit===undefined){return new Set}if(!Array.isArray(a.crit)||a.crit.length===0||a.crit.some(o=>typeof o!=="string"||o.length===0)){throw new o('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present')}let u;if(s!==undefined){u=new Map([...Object.entries(s),...i.entries()])}else{u=i}for(const i of a.crit){if(!u.has(i)){throw new oU(`Extension Header Parameter "${i}" is not recognized`)}if(c[i]===undefined){throw new o(`Extension Header Parameter "${i}" is missing`)}if(u.get(i)&&a[i]===undefined){throw new o(`Extension Header Parameter "${i}" MUST be integrity protected`)}}return new Set(a.crit)};const i0=o=>o?.[Symbol.toStringTag];const i1=(o,i,s)=>{if(i.use!==undefined){let o;switch(s){case"sign":case"verify":o="sig";break;case"encrypt":case"decrypt":o="enc";break}if(i.use!==o){throw new TypeError(`Invalid key for this operation, its "use" must be "${o}" when present`)}}if(i.alg!==undefined&&i.alg!==o){throw new TypeError(`Invalid key for this operation, its "alg" must be "${o}" when present`)}if(Array.isArray(i.key_ops)){let a;switch(true){case s==="sign"||s==="verify":case o==="dir":case o.includes("CBC-HS"):a=s;break;case o.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(o):if(!o.includes("GCM")&&o.endsWith("KW")){a=s==="encrypt"?"wrapKey":"unwrapKey"}else{a=s}break;case s==="encrypt"&&o.startsWith("RSA"):a="wrapKey";break;case s==="decrypt":a=o.startsWith("RSA")?"unwrapKey":"deriveBits";break}if(a&&i.key_ops?.includes?.(a)===false){throw new TypeError(`Invalid key for this operation, its "key_ops" must include "${a}" when present`)}}return true};const i2=(o,i,s)=>{if(i instanceof Uint8Array)return;if(oQ(i)){if(o2(i)&&i1(o,i,s))return;throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present`)}if(!oX(i)){throw new TypeError(o6(o,i,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"))}if(i.type!=="secret"){throw new TypeError(`${i0(i)} instances for symmetric algorithms must be of type "secret"`)}};const i5=(o,i,s)=>{if(oQ(i)){switch(s){case"decrypt":case"sign":if(o0(i)&&i1(o,i,s))return;throw new TypeError(`JSON Web Key for this operation be a private JWK`);case"encrypt":case"verify":if(o1(i)&&i1(o,i,s))return;throw new TypeError(`JSON Web Key for this operation be a public JWK`)}}if(!oX(i)){throw new TypeError(o6(o,i,"CryptoKey","KeyObject","JSON Web Key"))}if(i.type==="secret"){throw new TypeError(`${i0(i)} instances for asymmetric algorithms must not be of type "secret"`)}if(i.type==="public"){switch(s){case"sign":throw new TypeError(`${i0(i)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw new TypeError(`${i0(i)} instances for asymmetric algorithm decryption must be of type "private"`);default:break}}if(i.type==="private"){switch(s){case"verify":throw new TypeError(`${i0(i)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw new TypeError(`${i0(i)} instances for asymmetric algorithm encryption must be of type "public"`);default:break}}};const i3=(o,i,s)=>{const a=o.startsWith("HS")||o==="dir"||o.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(o)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(o);if(a){i2(o,i,s)}else{i5(o,i,s)}};class i6{#e;#t;#n;#r;#o;#i;#s;#a;constructor(o){if(!(o instanceof Uint8Array)){throw new TypeError("plaintext must be an instance of Uint8Array")}this.#e=o}setKeyManagementParameters(o){if(this.#a){throw new TypeError("setKeyManagementParameters can only be called once")}this.#a=o;return this}setProtectedHeader(o){if(this.#t){throw new TypeError("setProtectedHeader can only be called once")}this.#t=o;return this}setSharedUnprotectedHeader(o){if(this.#n){throw new TypeError("setSharedUnprotectedHeader can only be called once")}this.#n=o;return this}setUnprotectedHeader(o){if(this.#r){throw new TypeError("setUnprotectedHeader can only be called once")}this.#r=o;return this}setAdditionalAuthenticatedData(o){this.#o=o;return this}setContentEncryptionKey(o){if(this.#i){throw new TypeError("setContentEncryptionKey can only be called once")}this.#i=o;return this}setInitializationVector(o){if(this.#s){throw new TypeError("setInitializationVector can only be called once")}this.#s=o;return this}async encrypt(o,i){if(!this.#t&&!this.#r&&!this.#n){throw new oL("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()")}if(!iZ(this.#t,this.#r,this.#n)){throw new oL("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint")}const s={...this.#t,...this.#r,...this.#n};iQ(oL,new Map,i?.crit,this.#t,s);if(s.zip!==undefined){throw new oU('JWE "zip" (Compression Algorithm) Header Parameter is not supported.')}const{alg:a,enc:c}=s;if(typeof a!=="string"||!a){throw new oL('JWE "alg" (Algorithm) Header Parameter missing or invalid')}if(typeof c!=="string"||!c){throw new oL('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid')}let u;if(this.#i&&(a==="dir"||a==="ECDH-ES")){throw new TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${a}`)}i3(a==="dir"?c:a,o,"encrypt");let l;{let s;const d=await iK(o,a);({cek:l,encryptedKey:u,parameters:s}=await iY(a,c,d,this.#i,this.#a));if(s){if(i&&io in i){if(!this.#r){this.setUnprotectedHeader(s)}else{this.#r={...this.#r,...s}}}else if(!this.#t){this.setProtectedHeader(s)}else{this.#t={...this.#t,...s}}}}let d;let f;let p;if(this.#t){f=ov.encode(oC(JSON.stringify(this.#t)))}else{f=ov.encode("")}if(this.#o){p=oC(this.#o);d=ok(f,ov.encode("."),ov.encode(p))}else{d=f}const{ciphertext:h,tag:g,iv:m}=await ib(c,this.#e,l,this.#s,d);const y={ciphertext:oC(h)};if(m){y.iv=oC(m)}if(g){y.tag=oC(g)}if(u){y.encrypted_key=oC(u)}if(p){y.aad=p}if(this.#t){y.protected=oE.decode(f)}if(this.#n){y.unprotected=this.#n}if(this.#r){y.header=this.#r}return y}};class i4{#c;constructor(o){this.#c=new i6(o)}setContentEncryptionKey(o){this.#c.setContentEncryptionKey(o);return this}setInitializationVector(o){this.#c.setInitializationVector(o);return this}setProtectedHeader(o){this.#c.setProtectedHeader(o);return this}setKeyManagementParameters(o){this.#c.setKeyManagementParameters(o);return this}async encrypt(o,i){const s=await this.#c.encrypt(o,i);return[s.protected,s.encrypted_key,s.iv,s.ciphertext,s.tag].join(".")}};const i8=o=>Math.floor(o.getTime()/1e3);const i9=60;const i7=i9*60;const se=i7*24;const st=se*7;const sn=se*365.25;const sr=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;const so=o=>{const i=sr.exec(o);if(!i||i[4]&&i[1]){throw new TypeError("Invalid time period format")}const s=parseFloat(i[2]);const a=i[3].toLowerCase();let c;switch(a){case"sec":case"secs":case"second":case"seconds":case"s":c=Math.round(s);break;case"minute":case"minutes":case"min":case"mins":case"m":c=Math.round(s*i9);break;case"hour":case"hours":case"hr":case"hrs":case"h":c=Math.round(s*i7);break;case"day":case"days":case"d":c=Math.round(s*se);break;case"week":case"weeks":case"w":c=Math.round(s*st);break;default:c=Math.round(s*sn);break}if(i[1]==="-"||i[4]==="ago"){return-c}return c};function si(o,i){if(!Number.isFinite(i)){throw new TypeError(`Invalid ${o} input`)}return i}const ss=o=>{if(o.includes("/")){return o.toLowerCase()}return`application/${o.toLowerCase()}`};const sa=(o,i)=>{if(typeof o==="string"){return i.includes(o)}if(Array.isArray(o)){return i.some(Set.prototype.has.bind(new Set(o)))}return false};function sc(o,i,s={}){let a;try{a=JSON.parse(oE.decode(i))}catch{}if(!oZ(a)){throw new oH("JWT Claims Set must be a top-level JSON object")}const{typ:c}=s;if(c&&(typeof o.typ!=="string"||ss(o.typ)!==ss(c))){throw new oj('unexpected "typ" JWT header value',a,"typ","check_failed")}const{requiredClaims:u=[],issuer:l,subject:d,audience:f,maxTokenAge:p}=s;const h=[...u];if(p!==undefined)h.push("iat");if(f!==undefined)h.push("aud");if(d!==undefined)h.push("sub");if(l!==undefined)h.push("iss");for(const o of new Set(h.reverse())){if(!(o in a)){throw new oj(`missing required "${o}" claim`,a,o,"missing")}}if(l&&!(Array.isArray(l)?l:[l]).includes(a.iss)){throw new oj('unexpected "iss" claim value',a,"iss","check_failed")}if(d&&a.sub!==d){throw new oj('unexpected "sub" claim value',a,"sub","check_failed")}if(f&&!sa(a.aud,typeof f==="string"?[f]:f)){throw new oj('unexpected "aud" claim value',a,"aud","check_failed")}let g;switch(typeof s.clockTolerance){case"string":g=so(s.clockTolerance);break;case"number":g=s.clockTolerance;break;case"undefined":g=0;break;default:throw new TypeError("Invalid clockTolerance option type")}const{currentDate:m}=s;const y=i8(m||new Date);if((a.iat!==undefined||p)&&typeof a.iat!=="number"){throw new oj('"iat" claim must be a number',a,"iat","invalid")}if(a.nbf!==undefined){if(typeof a.nbf!=="number"){throw new oj('"nbf" claim must be a number',a,"nbf","invalid")}if(a.nbf>y+g){throw new oj('"nbf" claim timestamp check failed',a,"nbf","check_failed")}}if(a.exp!==undefined){if(typeof a.exp!=="number"){throw new oj('"exp" claim must be a number',a,"exp","invalid")}if(a.exp<=y-g){throw new oN('"exp" claim timestamp check failed',a,"exp","check_failed")}}if(p){const o=y-a.iat;const i=typeof p==="number"?p:so(p);if(o-g>i){throw new oN('"iat" claim timestamp check failed (too far in the past)',a,"iat","check_failed")}if(o<0-g){throw new oj('"iat" claim timestamp check failed (it should be in the past)',a,"iat","check_failed")}}return a}class su{#u;constructor(o){if(!oZ(o)){throw new TypeError("JWT Claims Set MUST be an object")}this.#u=structuredClone(o)}data(){return ov.encode(JSON.stringify(this.#u))}get iss(){return this.#u.iss}set iss(o){this.#u.iss=o}get sub(){return this.#u.sub}set sub(o){this.#u.sub=o}get aud(){return this.#u.aud}set aud(o){this.#u.aud=o}set jti(o){this.#u.jti=o}set nbf(o){if(typeof o==="number"){this.#u.nbf=si("setNotBefore",o)}else if(o instanceof Date){this.#u.nbf=si("setNotBefore",i8(o))}else{this.#u.nbf=i8(new Date)+so(o)}}set exp(o){if(typeof o==="number"){this.#u.exp=si("setExpirationTime",o)}else if(o instanceof Date){this.#u.exp=si("setExpirationTime",i8(o))}else{this.#u.exp=i8(new Date)+so(o)}}set iat(o){if(typeof o==="undefined"){this.#u.iat=i8(new Date)}else if(o instanceof Date){this.#u.iat=si("setIssuedAt",i8(o))}else if(typeof o==="string"){this.#u.iat=si("setIssuedAt",i8(new Date)+so(o))}else{this.#u.iat=si("setIssuedAt",o)}}};class sl{#i;#s;#a;#t;#l;#d;#f;#p;constructor(o={}){this.#p=new su(o)}setIssuer(o){this.#p.iss=o;return this}setSubject(o){this.#p.sub=o;return this}setAudience(o){this.#p.aud=o;return this}setJti(o){this.#p.jti=o;return this}setNotBefore(o){this.#p.nbf=o;return this}setExpirationTime(o){this.#p.exp=o;return this}setIssuedAt(o){this.#p.iat=o;return this}setProtectedHeader(o){if(this.#t){throw new TypeError("setProtectedHeader can only be called once")}this.#t=o;return this}setKeyManagementParameters(o){if(this.#a){throw new TypeError("setKeyManagementParameters can only be called once")}this.#a=o;return this}setContentEncryptionKey(o){if(this.#i){throw new TypeError("setContentEncryptionKey can only be called once")}this.#i=o;return this}setInitializationVector(o){if(this.#s){throw new TypeError("setInitializationVector can only be called once")}this.#s=o;return this}replicateIssuerAsHeader(){this.#l=true;return this}replicateSubjectAsHeader(){this.#d=true;return this}replicateAudienceAsHeader(){this.#f=true;return this}async encrypt(o,i){const s=new i4(this.#p.data());if(this.#t&&(this.#l||this.#d||this.#f)){this.#t={...this.#t,iss:this.#l?this.#p.iss:undefined,sub:this.#d?this.#p.sub:undefined,aud:this.#f?this.#p.aud:undefined}}s.setProtectedHeader(this.#t);if(this.#s){s.setInitializationVector(this.#s)}if(this.#i){s.setContentEncryptionKey(this.#i)}if(this.#a){s.setKeyManagementParameters(this.#a)}return s.encrypt(o,i)}};async function sd(o,i,s){if(typeof o!=="string"||o.indexOf("-----BEGIN PUBLIC KEY-----")!==0){throw new TypeError('"spki" must be SPKI formatted string')}return fromSPKI(o,i,s)}async function sf(o,i,s){if(typeof o!=="string"||o.indexOf("-----BEGIN CERTIFICATE-----")!==0){throw new TypeError('"x509" must be X.509 formatted string')}return fromX509(o,i,s)}async function sp(o,i,s){if(typeof o!=="string"||o.indexOf("-----BEGIN PRIVATE KEY-----")!==0){throw new TypeError('"pkcs8" must be PKCS#8 formatted string')}return fromPKCS8(o,i,s)}async function sh(o,i,s){if(!oZ(o)){throw new TypeError("JWK must be an object")}let a;i??=o.alg;a??=s?.extractable??o.ext;switch(o.kty){case"oct":if(typeof o.k!=="string"||!o.k){throw new TypeError('missing "k" (Key Value) Parameter value')}return oO(o.k);case"RSA":if("oth"in o&&o.oth!==undefined){throw new oU('RSA JWK "oth" (Other Primes Info) Parameter value is not supported')}case"EC":case"OKP":return iL({...o,alg:i,ext:a});default:throw new oU('Unsupported "kty" (Key Type) Parameter value')}};const sg=async(o,i,s,a,c)=>{switch(o){case"dir":{if(s!==undefined)throw new oL("Encountered unexpected JWE Encrypted Key");return i}case"ECDH-ES":if(s!==undefined)throw new oL("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!oZ(a.epk))throw new oL(`JOSE Header "epk" (Ephemeral Public Key) missing or invalid`);oV(i);if(!iP(i))throw new oU("ECDH with the provided key is not allowed or not supported by your javascript runtime");const c=await sh(a.epk,o);oV(c);let u;let l;if(a.apu!==undefined){if(typeof a.apu!=="string")throw new oL(`JOSE Header "apu" (Agreement PartyUInfo) invalid`);try{u=oO(a.apu)}catch{throw new oL("Failed to base64url decode the apu")}}if(a.apv!==undefined){if(typeof a.apv!=="string")throw new oL(`JOSE Header "apv" (Agreement PartyVInfo) invalid`);try{l=oO(a.apv)}catch{throw new oL("Failed to base64url decode the apv")}}const d=await iR(c,i,o==="ECDH-ES"?a.enc:o,o==="ECDH-ES"?iq(a.enc):parseInt(o.slice(-5,-2),10),u,l);if(o==="ECDH-ES")return d;if(s===undefined)throw new oL("JWE Encrypted Key missing");return iS(o.slice(-6),d,s)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(s===undefined)throw new oL("JWE Encrypted Key missing");oV(i);return iU(o,i,s)}case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{if(s===undefined)throw new oL("JWE Encrypted Key missing");if(typeof a.p2c!=="number")throw new oL(`JOSE Header "p2c" (PBES2 Count) missing or invalid`);const u=c?.maxPBES2Count||1e4;if(a.p2c>u)throw new oL(`JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds`);if(typeof a.p2s!=="string")throw new oL(`JOSE Header "p2s" (PBES2 Salt) missing or invalid`);let l;try{l=oO(a.p2s)}catch{throw new oL("Failed to base64url decode the p2s")}return iI(o,i,s,a.p2c,l)}case"A128KW":case"A192KW":case"A256KW":{if(s===undefined)throw new oL("JWE Encrypted Key missing");return iS(o,i,s)}case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{if(s===undefined)throw new oL("JWE Encrypted Key missing");if(typeof a.iv!=="string")throw new oL(`JOSE Header "iv" (Initialization Vector) missing or invalid`);if(typeof a.tag!=="string")throw new oL(`JOSE Header "tag" (Authentication Tag) missing or invalid`);let c;try{c=oO(a.iv)}catch{throw new oL("Failed to base64url decode the iv")}let u;try{u=oO(a.tag)}catch{throw new oL("Failed to base64url decode the tag")}return iX(o,i,s,c,u)}default:{throw new oU('Invalid or unsupported "alg" (JWE Algorithm) header value')}}};const sm=(o,i)=>{if(i!==undefined&&(!Array.isArray(i)||i.some(o=>typeof o!=="string"))){throw new TypeError(`"${o}" option must be an array of strings`)}if(!i){return undefined}return new Set(i)};async function sy(o,i,s){if(!oZ(o)){throw new oL("Flattened JWE must be an object")}if(o.protected===undefined&&o.header===undefined&&o.unprotected===undefined){throw new oL("JOSE Header missing")}if(o.iv!==undefined&&typeof o.iv!=="string"){throw new oL("JWE Initialization Vector incorrect type")}if(typeof o.ciphertext!=="string"){throw new oL("JWE Ciphertext missing or incorrect type")}if(o.tag!==undefined&&typeof o.tag!=="string"){throw new oL("JWE Authentication Tag incorrect type")}if(o.protected!==undefined&&typeof o.protected!=="string"){throw new oL("JWE Protected Header incorrect type")}if(o.encrypted_key!==undefined&&typeof o.encrypted_key!=="string"){throw new oL("JWE Encrypted Key incorrect type")}if(o.aad!==undefined&&typeof o.aad!=="string"){throw new oL("JWE AAD incorrect type")}if(o.header!==undefined&&!oZ(o.header)){throw new oL("JWE Shared Unprotected Header incorrect type")}if(o.unprotected!==undefined&&!oZ(o.unprotected)){throw new oL("JWE Per-Recipient Unprotected Header incorrect type")}let a;if(o.protected){try{const i=oO(o.protected);a=JSON.parse(oE.decode(i))}catch{throw new oL("JWE Protected Header is invalid")}}if(!iZ(a,o.header,o.unprotected)){throw new oL("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint")}const c={...a,...o.header,...o.unprotected};iQ(oL,new Map,s?.crit,a,c);if(c.zip!==undefined){throw new oU('JWE "zip" (Compression Algorithm) Header Parameter is not supported.')}const{alg:u,enc:l}=c;if(typeof u!=="string"||!u){throw new oL("missing JWE Algorithm (alg) in JWE Header")}if(typeof l!=="string"||!l){throw new oL("missing JWE Encryption Algorithm (enc) in JWE Header")}const d=s&&sm("keyManagementAlgorithms",s.keyManagementAlgorithms);const f=s&&sm("contentEncryptionAlgorithms",s.contentEncryptionAlgorithms);if(d&&!d.has(u)||!d&&u.startsWith("PBES2")){throw new oD('"alg" (Algorithm) Header Parameter value not allowed')}if(f&&!f.has(l)){throw new oD('"enc" (Encryption Algorithm) Header Parameter value not allowed')}let p;if(o.encrypted_key!==undefined){try{p=oO(o.encrypted_key)}catch{throw new oL("Failed to base64url decode the encrypted_key")}}let h=false;if(typeof i==="function"){i=await i(a,o);h=true}i3(u==="dir"?l:u,i,"decrypt");const g=await iK(i,u);let m;try{m=await sg(u,g,p,c,s)}catch(o){if(o instanceof TypeError||o instanceof oL||o instanceof oU){throw o}m=iB(l)}let y;let w;if(o.iv!==undefined){try{y=oO(o.iv)}catch{throw new oL("Failed to base64url decode the iv")}}if(o.tag!==undefined){try{w=oO(o.tag)}catch{throw new oL("Failed to base64url decode the tag")}}const b=ov.encode(o.protected??"");let _;if(o.aad!==undefined){_=ok(b,ov.encode("."),ov.encode(o.aad))}else{_=b}let v;try{v=oO(o.ciphertext)}catch{throw new oL("Failed to base64url decode the ciphertext")}const E=await iG(l,m,v,y,w,_);const S={plaintext:E};if(o.protected!==undefined){S.protectedHeader=a}if(o.aad!==undefined){try{S.additionalAuthenticatedData=oO(o.aad)}catch{throw new oL("Failed to base64url decode the aad")}}if(o.unprotected!==undefined){S.sharedUnprotectedHeader=o.unprotected}if(o.header!==undefined){S.unprotectedHeader=o.header}if(h){return{...S,key:g}}return S};async function sw(o,i,s){if(o instanceof Uint8Array){o=oE.decode(o)}if(typeof o!=="string"){throw new oL("Compact JWE must be a string or Uint8Array")}const{0:a,1:c,2:u,3:l,4:d,length:f}=o.split(".");if(f!==5){throw new oL("Invalid Compact JWE")}const p=await sy({ciphertext:l,iv:u||undefined,protected:a,tag:d||undefined,encrypted_key:c||undefined},i,s);const h={plaintext:p.plaintext,protectedHeader:p.protectedHeader};if(typeof i==="function"){return{...h,key:p.key}}return h};async function sb(o,i,s){const a=await sw(o,i,s);const c=sc(a.protectedHeader,a.plaintext,s);const{protectedHeader:u}=a;if(u.iss!==undefined&&u.iss!==c.iss){throw new oj('replicated "iss" claim header parameter mismatch',c,"iss","mismatch")}if(u.sub!==undefined&&u.sub!==c.sub){throw new oj('replicated "sub" claim header parameter mismatch',c,"sub","mismatch")}if(u.aud!==undefined&&JSON.stringify(u.aud)!==JSON.stringify(c.aud)){throw new oj('replicated "aud" claim header parameter mismatch',c,"aud","mismatch")}const l={payload:c,protectedHeader:u};if(typeof i==="function"){return{...l,key:a.key}}return l};const s_=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/;const sv=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/;const sE=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;const sS=/^[\u0020-\u003A\u003D-\u007E]*$/;const sk=Object.prototype.toString;const sx=(()=>{const o=function(){};o.prototype=Object.create(null);return o})();function sR(o,i){const s=new sx;const a=o.length;if(a<2)return s;const c=i?.decode||sO;let u=0;do{const i=o.indexOf("=",u);if(i===-1)break;const l=o.indexOf(";",u);const d=l===-1?a:l;if(i>d){u=o.lastIndexOf(";",i-1)+1;continue}const f=sP(o,u,i);const p=sA(o,i,f);const h=o.slice(f,p);if(s[h]===undefined){let a=sP(o,i+1,d);let u=sA(o,d,a);const l=c(o.slice(a,u));s[h]=l}u=d+1}while(u<a);return s}function sP(o,i,s){do{const s=o.charCodeAt(i);if(s!==32&&s!==9)return i}while(++i<s);return s}function sA(o,i,s){while(i>s){const s=o.charCodeAt(--i);if(s!==32&&s!==9)return i+1}return s}function sT(o,i,s){const a=s?.encode||encodeURIComponent;if(!s_.test(o)){throw new TypeError(`argument name is invalid: ${o}`)}const c=a(i);if(!sv.test(c)){throw new TypeError(`argument val is invalid: ${i}`)}let u=o+"="+c;if(!s)return u;if(s.maxAge!==undefined){if(!Number.isInteger(s.maxAge)){throw new TypeError(`option maxAge is invalid: ${s.maxAge}`)}u+="; Max-Age="+s.maxAge}if(s.domain){if(!sE.test(s.domain)){throw new TypeError(`option domain is invalid: ${s.domain}`)}u+="; Domain="+s.domain}if(s.path){if(!sS.test(s.path)){throw new TypeError(`option path is invalid: ${s.path}`)}u+="; Path="+s.path}if(s.expires){if(!sC(s.expires)||!Number.isFinite(s.expires.valueOf())){throw new TypeError(`option expires is invalid: ${s.expires}`)}u+="; Expires="+s.expires.toUTCString()}if(s.httpOnly){u+="; HttpOnly"}if(s.secure){u+="; Secure"}if(s.partitioned){u+="; Partitioned"}if(s.priority){const o=typeof s.priority==="string"?s.priority.toLowerCase():undefined;switch(o){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${s.priority}`)}}if(s.sameSite){const o=typeof s.sameSite==="string"?s.sameSite.toLowerCase():s.sameSite;switch(o){case true:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${s.sameSite}`)}}return u}function sO(o){if(o.indexOf("%")===-1)return o;try{return decodeURIComponent(o)}catch(i){return o}}function sC(o){return sk.call(o)==="[object Date]"};const{"q":sI}=a;const sj=30*24*60*60;const sN=()=>Date.now()/1e3|0;const sD="dir";const sU="A256CBC-HS512";async function s$(o){const{token:i={},secret:s,maxAge:a=sj,salt:c}=o;const u=Array.isArray(s)?s:[s];const l=await sH(sU,u[0],c);const d=await it({kty:"oct",k:oC(l)},`sha${l.byteLength<<3}`);return await new sl(i).setProtectedHeader({alg:sD,enc:sU,kid:d}).setIssuedAt().setExpirationTime(sN()+a).setJti(crypto.randomUUID()).encrypt(l)}async function sL(o){const{token:i,secret:s,salt:a}=o;const c=Array.isArray(s)?s:[s];if(!i)return null;const{payload:u}=await sb(i,async({kid:o,enc:i})=>{for(const s of c){const c=await sH(i,s,a);if(o===undefined)return c;const u=await it({kty:"oct",k:oC(c)},`sha${c.byteLength<<3}`);if(o===u)return c}throw new Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:[sD],contentEncryptionAlgorithms:[sU,"A256GCM"]});return u}async function sM(o){const{secureCookie:i,cookieName:s=defaultCookies(i??false).sessionToken.name,decode:a=sL,salt:c=s,secret:u,logger:l=console,raw:d,req:f}=o;if(!f)throw new Error("Must pass `req` to JWT getToken()");const p=f.headers instanceof Headers?f.headers:new Headers(f.headers);const h=new SessionStore({name:s,options:{secure:i}},sI(p.get("cookie")??""),l);let g=h.value;const m=p.get("authorization");if(!g&&m?.split(" ")[0]==="Bearer"){const o=m.split(" ")[1];g=decodeURIComponent(o)}if(!g)return null;if(d)return g;if(!u)throw new MissingSecret("Must pass `secret` if not set to JWT getToken()");try{return await a({token:g,secret:u,salt:c})}catch{return null}}async function sH(o,i,s){let a;switch(o){case"A256CBC-HS512":a=64;break;case"A256GCM":a=32;break;default:throw new Error("Unsupported JWT Content Encryption Algorithm")}return await ob("sha256",i,s,`Auth.js Generated Encryption Key (${s})`,a)};async function sW({options:o,paramValue:i,cookieValue:s}){const{url:a,callbacks:c}=o;let u=a.origin;if(i){u=await c.redirect({url:i,baseUrl:a.origin})}else if(s){u=await c.redirect({url:s,baseUrl:a.origin})}return{callbackUrl:u,callbackUrlCookie:u!==s?u:undefined}};const sK="\x1b[31m";const sq="\x1b[33m";const sB="\x1b[90m";const sJ="\x1b[0m";const sz={error(o){const i=o instanceof rO?o.type:o.name;console.error(`${sK}[auth][error]${sJ} ${i}: ${o.message}`);if(o.cause&&typeof o.cause==="object"&&"err"in o.cause&&o.cause.err instanceof Error){const{err:i,...s}=o.cause;console.error(`${sK}[auth][cause]${sJ}:`,i.stack);if(s)console.error(`${sK}[auth][details]${sJ}:`,JSON.stringify(s,null,2))}else if(o.stack){console.error(o.stack.replace(/.*/,"").substring(1))}},warn(o){const i=`https://warnings.authjs.dev`;console.warn(`${sq}[auth][warn][${o}]${sJ}`,`Read more: ${i}`)},debug(o,i){console.log(`${sB}[auth][debug]:${sJ} ${o}`,JSON.stringify(i,null,2))}};function sV(o){const i={...sz};if(!o.debug)i.debug=()=>{};if(o.logger?.error)i.error=o.logger.error;if(o.logger?.warn)i.warn=o.logger.warn;if(o.logger?.debug)i.debug=o.logger.debug;o.logger??(o.logger=i);return i};const sG=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];function sF(o){return sG.includes(o)};const{"q":sX,"l":sY}=a;async function sZ(o){if(!("body"in o)||!o.body||o.method!=="POST")return;const i=o.headers.get("content-type");if(i?.includes("application/json")){return await o.json()}else if(i?.includes("application/x-www-form-urlencoded")){const i=new URLSearchParams(await o.text());return Object.fromEntries(i)}}async function sQ(o,i){try{if(o.method!=="GET"&&o.method!=="POST")throw new rQ("Only GET and POST requests are supported");i.basePath??(i.basePath="/auth");const s=new URL(o.url);const{action:a,providerId:c}=s3(s.pathname,i.basePath);return{url:s,action:a,providerId:c,method:o.method,headers:Object.fromEntries(o.headers),body:o.body?await sZ(o):undefined,cookies:sX(o.headers.get("cookie")??"")??{},error:s.searchParams.get("error")??undefined,query:Object.fromEntries(s.searchParams)}}catch(a){const s=sV(i);s.error(a);s.debug("request",o)}}function s0(o){return new Request(o.url,{headers:o.headers,method:o.method,body:o.method==="POST"?JSON.stringify(o.body??{}):undefined})}function s1(o){const i=new Headers(o.headers);o.cookies?.forEach(o=>{const{name:s,value:a,options:c}=o;const u=sY(s,a,c);if(i.has("Set-Cookie"))i.append("Set-Cookie",u);else i.set("Set-Cookie",u)});let s=o.body;if(i.get("content-type")==="application/json")s=JSON.stringify(o.body);else if(i.get("content-type")==="application/x-www-form-urlencoded")s=new URLSearchParams(o.body).toString();const a=o.redirect?302:o.status??200;const c=new Response(s,{headers:i,status:a});if(o.redirect)c.headers.set("Location",o.redirect);return c}async function s2(o){const i=new TextEncoder().encode(o);const s=await crypto.subtle.digest("SHA-256",i);return Array.from(new Uint8Array(s)).map(o=>o.toString(16).padStart(2,"0")).join("").toString()}function s5(o){const i=o=>("0"+o.toString(16)).slice(-2);const s=(o,s)=>o+i(s);const a=crypto.getRandomValues(new Uint8Array(o));return Array.from(a).reduce(s,"")}function s3(o,i){const s=o.match(new RegExp(`^${i}(.+)`));if(s===null)throw new rQ(`Cannot parse action at ${o}`);const a=s.at(-1);const c=a.replace(/^\//,"").split("/").filter(Boolean);if(c.length!==1&&c.length!==2)throw new rQ(`Cannot parse action at ${o}`);const[u,l]=c;if(!sF(u))throw new rQ(`Cannot parse action at ${o}`);if(l&&!["signin","callback","webauthn-options"].includes(u))throw new rQ(`Cannot parse action at ${o}`);return{action:u,providerId:l=="undefined"?undefined:l}};async function s6({options:o,cookieValue:i,isPost:s,bodyValue:a}){if(i){const[c,u]=i.split("|");const l=await s2(`${c}${o.secret}`);if(u===l){const o=s&&c===a;return{csrfTokenVerified:o,csrfToken:c}}}const c=s5(32);const u=await s2(`${c}${o.secret}`);const l=`${c}|${u}`;return{cookie:l,csrfToken:c}}function s4(o,i){if(i)return;throw new r3(`CSRF token was missing during an action ${o}`)};function s8(o){return o!==null&&typeof o==="object"}function s9(o,...i){if(!i.length)return o;const s=i.shift();if(s8(o)&&s8(s)){for(const i in s){if(s8(s[i])){if(!s8(o[i]))o[i]=Array.isArray(s[i])?[]:{};s9(o[i],s[i])}else if(s[i]!==undefined)o[i]=s[i]}}return s9(o,...i)};const s7=Symbol("skip-csrf-check");const ae=Symbol("return-type-raw");const at=Symbol("custom-fetch");const an=Symbol("conform-internal");function ar(o){const{providerId:i,config:s}=o;const a=new URL(s.basePath??"/auth",o.url.origin);const c=s.providers.map(o=>{const i=typeof o==="function"?o():o;const{options:c,...u}=i;const l=c?.id??u.id;const d=s9(u,c,{signinUrl:`${a}/signin/${l}`,callbackUrl:`${a}/callback/${l}`});if(i.type==="oauth"||i.type==="oidc"){d.redirectProxyUrl??(d.redirectProxyUrl=c?.redirectProxyUrl??s.redirectProxyUrl);const o=ao(d);if(o.authorization?.url.searchParams.get("response_mode")==="form_post"){delete o.redirectProxyUrl}o[at]??(o[at]=c?.[at]);return o}return d});const u=c.find(({id:o})=>o===i);if(i&&!u){const o=c.map(o=>o.id).join(", ");throw new Error(`Provider with id "${i}" not found. Available providers: [${o}].`)}return{providers:c,provider:u}}function ao(o){if(o.issuer)o.wellKnown??(o.wellKnown=`${o.issuer}/.well-known/openid-configuration`);const i=ac(o.authorization,o.issuer);if(i&&!i.url?.searchParams.has("scope")){i.url.searchParams.set("scope","openid profile email")}const s=ac(o.token,o.issuer);const a=ac(o.userinfo,o.issuer);const c=o.checks??["pkce"];if(o.redirectProxyUrl){if(!c.includes("state"))c.push("state");o.redirectProxyUrl=`${o.redirectProxyUrl}/callback/${o.id}`}return{...o,authorization:i,token:s,checks:c,userinfo:a,profile:o.profile??ai,account:o.account??as}}const ai=o=>{return aa({id:o.sub??o.id??crypto.randomUUID(),name:o.name??o.nickname??o.preferred_username,email:o.email,image:o.picture})};const as=o=>{return aa({access_token:o.access_token,id_token:o.id_token,refresh_token:o.refresh_token,expires_at:o.expires_at,scope:o.scope,token_type:o.token_type,session_state:o.session_state})};function aa(o){const i={};for(const[s,a]of Object.entries(o)){if(a!==undefined)i[s]=a}return i}function ac(o,i){if(!o&&i)return;if(typeof o==="string"){return{url:new URL(o)}}const s=new URL(o?.url??"https://authjs.dev");if(o?.params!=null){for(let[i,a]of Object.entries(o.params)){if(i==="claims"){a=JSON.stringify(a)}s.searchParams.set(i,String(a))}}return{url:s,request:o?.request,conform:o?.conform,...o?.clientPrivateKey?{clientPrivateKey:o?.clientPrivateKey}:null}}function au(o){return o.type==="oidc"}function al(o){return o.type==="oauth"}function ad(o){return o.type==="oauth"||o.type==="oidc"};const af={signIn(){return true},redirect({url:o,baseUrl:i}){if(o.startsWith("/"))return`${i}${o}`;else if(new URL(o).origin===i)return o;return i},session({session:o}){return{user:{name:o.user?.name,email:o.user?.email,image:o.user?.image},expires:o.expires?.toISOString?.()??o.expires}},jwt({token:o}){return o}};async function ap({authOptions:o,providerId:i,action:s,url:a,cookies:c,callbackUrl:u,csrfToken:l,csrfDisabled:d,isPost:f}){const p=sV(o);const{providers:h,provider:g}=ar({url:a,providerId:i,config:o});const m=30*24*60*60;let y=false;if((g?.type==="oauth"||g?.type==="oidc")&&g.redirectProxyUrl){try{y=new URL(g.redirectProxyUrl).origin===a.origin}catch{throw new TypeError(`redirectProxyUrl must be a valid URL. Received: ${g.redirectProxyUrl}`)}}const w={debug:false,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...o,url:a,action:s,provider:g,cookies:s9(rA(o.useSecureCookies??a.protocol==="https:"),o.cookies),providers:h,session:{strategy:o.adapter?"database":"jwt",maxAge:m,updateAge:24*60*60,generateSessionToken:()=>crypto.randomUUID(),...o.session},jwt:{secret:o.secret,maxAge:o.session?.maxAge??m,encode:s$,decode:sL,...o.jwt},events:ah(o.events??{},p),adapter:ag(o.adapter,p),callbacks:{...af,...o.callbacks},logger:p,callbackUrl:a.origin,isOnRedirectProxy:y,experimental:{...o.experimental}};const b=[];if(d){w.csrfTokenVerified=true}else{const{csrfToken:o,cookie:i,csrfTokenVerified:s}=await s6({options:w,cookieValue:c?.[w.cookies.csrfToken.name],isPost:f,bodyValue:l});w.csrfToken=o;w.csrfTokenVerified=s;if(i){b.push({name:w.cookies.csrfToken.name,value:i,options:w.cookies.csrfToken.options})}}const{callbackUrl:_,callbackUrlCookie:v}=await sW({options:w,cookieValue:c?.[w.cookies.callbackUrl.name],paramValue:u});w.callbackUrl=_;if(v){b.push({name:w.cookies.callbackUrl.name,value:v,options:w.cookies.callbackUrl.options})}return{options:w,cookies:b}}function ah(o,i){return Object.keys(o).reduce((s,a)=>{s[a]=async(...s)=>{try{const i=o[a];return await i(...s)}catch(o){i.error(new rU(o))}};return s},{})}function ag(o,i){if(!o)return;return Object.keys(o).reduce((s,a)=>{s[a]=async(...s)=>{try{i.debug(`adapter_${a}`,{args:s});const c=o[a];return await c(...s)}catch(s){const o=new rI(s);i.error(o);throw o}};return s},{})};var am,ay,aw,ab,a_,av,aE,aS,ak,ax,aR,aP,aA={},aT=[],aO=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,aC=Array.isArray;function aI(o,i){for(var s in i)o[s]=i[s];return o}function aj(o){o&&o.parentNode&&o.parentNode.removeChild(o)}function aN(o,i,s){var a,c,u,l={};for(u in i)"key"==u?a=i[u]:"ref"==u?c=i[u]:l[u]=i[u];if(arguments.length>2&&(l.children=arguments.length>3?am.call(arguments,2):s),"function"==typeof o&&null!=o.defaultProps)for(u in o.defaultProps)void 0===l[u]&&(l[u]=o.defaultProps[u]);return aD(o,l,a,c,null)}function aD(o,i,s,a,c){var u={type:o,props:i,key:s,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==c?++aw:c,__i:-1,__u:0};return null==c&&null!=ay.vnode&&ay.vnode(u),u}function aU(){return{current:null}}function a$(o){return o.children}function aL(o,i){this.props=o,this.context=i}function aM(o,i){if(null==i)return o.__?aM(o.__,o.__i+1):null;for(var s;i<o.__k.length;i++)if(null!=(s=o.__k[i])&&null!=s.__e)return s.__e;return"function"==typeof o.type?aM(o):null}function aH(o){var i,s;if(null!=(o=o.__)&&null!=o.__c){for(o.__e=o.__c.base=null,i=0;i<o.__k.length;i++)if(null!=(s=o.__k[i])&&null!=s.__e){o.__e=o.__c.base=s.__e;break}return aH(o)}}function aW(o){(!o.__d&&(o.__d=!0)&&a_.push(o)&&!aK.__r++||av!==ay.debounceRendering)&&((av=ay.debounceRendering)||aE)(aK)}function aK(){var o,i,s,a,c,u,l,d;for(a_.sort(aS);o=a_.shift();)o.__d&&(i=a_.length,a=void 0,u=(c=(s=o).__v).__e,l=[],d=[],s.__P&&((a=aI({},c)).__v=c.__v+1,ay.vnode&&ay.vnode(a),aY(s.__P,a,c,s.__n,s.__P.namespaceURI,32&c.__u?[u]:null,l,null==u?aM(c):u,!!(32&c.__u),d),a.__v=c.__v,a.__.__k[a.__i]=a,aZ(l,a,d),a.__e!=u&&aH(a)),a_.length>i&&a_.sort(aS));aK.__r=0}function aq(o,i,s,a,c,u,l,d,f,p,h){var g,m,y,w,b,_=a&&a.__k||aT,v=i.length;for(s.__d=f,aB(s,i,_),f=s.__d,g=0;g<v;g++)null!=(y=s.__k[g])&&(m=-1===y.__i?aA:_[y.__i]||aA,y.__i=g,aY(o,y,m,c,u,l,d,f,p,h),w=y.__e,y.ref&&m.ref!=y.ref&&(m.ref&&a0(m.ref,null,y),h.push(y.ref,y.__c||w,y)),null==b&&null!=w&&(b=w),65536&y.__u||m.__k===y.__k?f=aJ(y,f,o):"function"==typeof y.type&&void 0!==y.__d?f=y.__d:w&&(f=w.nextSibling),y.__d=void 0,y.__u&=-196609);s.__d=f,s.__e=b}function aB(o,i,s){var a,c,u,l,d,f=i.length,p=s.length,h=p,g=0;for(o.__k=[],a=0;a<f;a++)null!=(c=i[a])&&"boolean"!=typeof c&&"function"!=typeof c?(l=a+g,(c=o.__k[a]="string"==typeof c||"number"==typeof c||"bigint"==typeof c||c.constructor==String?aD(null,c,null,null,null):aC(c)?aD(a$,{children:c},null,null,null):void 0===c.constructor&&c.__b>0?aD(c.type,c.props,c.key,c.ref?c.ref:null,c.__v):c).__=o,c.__b=o.__b+1,u=null,-1!==(d=c.__i=aV(c,s,l,h))&&(h--,(u=s[d])&&(u.__u|=131072)),null==u||null===u.__v?(-1==d&&g--,"function"!=typeof c.type&&(c.__u|=65536)):d!==l&&(d==l-1?g--:d==l+1?g++:(d>l?g--:g++,c.__u|=65536))):c=o.__k[a]=null;if(h)for(a=0;a<p;a++)null!=(u=s[a])&&0==(131072&u.__u)&&(u.__e==o.__d&&(o.__d=aM(u)),a1(u,u))}function aJ(o,i,s){var a,c;if("function"==typeof o.type){for(a=o.__k,c=0;a&&c<a.length;c++)a[c]&&(a[c].__=o,i=aJ(a[c],i,s));return i}o.__e!=i&&(i&&o.type&&!s.contains(i)&&(i=aM(o)),s.insertBefore(o.__e,i||null),i=o.__e);do{i=i&&i.nextSibling}while(null!=i&&8===i.nodeType);return i}function az(o,i){return i=i||[],null==o||"boolean"==typeof o||(aC(o)?o.some(function(o){az(o,i)}):i.push(o)),i}function aV(o,i,s,a){var c=o.key,u=o.type,l=s-1,d=s+1,f=i[s];if(null===f||f&&c==f.key&&u===f.type&&0==(131072&f.__u))return s;if(a>(null!=f&&0==(131072&f.__u)?1:0))for(;l>=0||d<i.length;){if(l>=0){if((f=i[l])&&0==(131072&f.__u)&&c==f.key&&u===f.type)return l;l--}if(d<i.length){if((f=i[d])&&0==(131072&f.__u)&&c==f.key&&u===f.type)return d;d++}}return-1}function aG(o,i,s){"-"===i[0]?o.setProperty(i,null==s?"":s):o[i]=null==s?"":"number"!=typeof s||aO.test(i)?s:s+"px"}function aF(o,i,s,a,c){var u;e:if("style"===i)if("string"==typeof s)o.style.cssText=s;else{if("string"==typeof a&&(o.style.cssText=a=""),a)for(i in a)s&&i in s||aG(o.style,i,"");if(s)for(i in s)a&&s[i]===a[i]||aG(o.style,i,s[i])}else if("o"===i[0]&&"n"===i[1])u=i!==(i=i.replace(/(PointerCapture)$|Capture$/i,"$1")),i=i.toLowerCase()in o||"onFocusOut"===i||"onFocusIn"===i?i.toLowerCase().slice(2):i.slice(2),o.l||(o.l={}),o.l[i+u]=s,s?a?s.u=a.u:(s.u=ak,o.addEventListener(i,u?aR:ax,u)):o.removeEventListener(i,u?aR:ax,u);else{if("http://www.w3.org/2000/svg"==c)i=i.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=i&&"height"!=i&&"href"!=i&&"list"!=i&&"form"!=i&&"tabIndex"!=i&&"download"!=i&&"rowSpan"!=i&&"colSpan"!=i&&"role"!=i&&"popover"!=i&&i in o)try{o[i]=null==s?"":s;break e}catch(o){}"function"==typeof s||(null==s||!1===s&&"-"!==i[4]?o.removeAttribute(i):o.setAttribute(i,"popover"==i&&1==s?"":s))}}function aX(o){return function(i){if(this.l){var s=this.l[i.type+o];if(null==i.t)i.t=ak++;else if(i.t<s.u)return;return s(ay.event?ay.event(i):i)}}}function aY(o,i,s,a,c,u,l,d,f,p){var h,g,m,y,w,b,_,v,E,S,k,x,R,P,A,T,O=i.type;if(void 0!==i.constructor)return null;128&s.__u&&(f=!!(32&s.__u),u=[d=i.__e=s.__e]),(h=ay.__b)&&h(i);e:if("function"==typeof O)try{if(v=i.props,E="prototype"in O&&O.prototype.render,S=(h=O.contextType)&&a[h.__c],k=h?S?S.props.value:h.__:a,s.__c?_=(g=i.__c=s.__c).__=g.__E:(E?i.__c=g=new O(v,k):(i.__c=g=new aL(v,k),g.constructor=O,g.render=a2),S&&S.sub(g),g.props=v,g.state||(g.state={}),g.context=k,g.__n=a,m=g.__d=!0,g.__h=[],g._sb=[]),E&&null==g.__s&&(g.__s=g.state),E&&null!=O.getDerivedStateFromProps&&(g.__s==g.state&&(g.__s=aI({},g.__s)),aI(g.__s,O.getDerivedStateFromProps(v,g.__s))),y=g.props,w=g.state,g.__v=i,m)E&&null==O.getDerivedStateFromProps&&null!=g.componentWillMount&&g.componentWillMount(),E&&null!=g.componentDidMount&&g.__h.push(g.componentDidMount);else{if(E&&null==O.getDerivedStateFromProps&&v!==y&&null!=g.componentWillReceiveProps&&g.componentWillReceiveProps(v,k),!g.__e&&(null!=g.shouldComponentUpdate&&!1===g.shouldComponentUpdate(v,g.__s,k)||i.__v===s.__v)){for(i.__v!==s.__v&&(g.props=v,g.state=g.__s,g.__d=!1),i.__e=s.__e,i.__k=s.__k,i.__k.some(function(o){o&&(o.__=i)}),x=0;x<g._sb.length;x++)g.__h.push(g._sb[x]);g._sb=[],g.__h.length&&l.push(g);break e}null!=g.componentWillUpdate&&g.componentWillUpdate(v,g.__s,k),E&&null!=g.componentDidUpdate&&g.__h.push(function(){g.componentDidUpdate(y,w,b)})}if(g.context=k,g.props=v,g.__P=o,g.__e=!1,R=ay.__r,P=0,E){for(g.state=g.__s,g.__d=!1,R&&R(i),h=g.render(g.props,g.state,g.context),A=0;A<g._sb.length;A++)g.__h.push(g._sb[A]);g._sb=[]}else do{g.__d=!1,R&&R(i),h=g.render(g.props,g.state,g.context),g.state=g.__s}while(g.__d&&++P<25);g.state=g.__s,null!=g.getChildContext&&(a=aI(aI({},a),g.getChildContext())),E&&!m&&null!=g.getSnapshotBeforeUpdate&&(b=g.getSnapshotBeforeUpdate(y,w)),aq(o,aC(T=null!=h&&h.type===a$&&null==h.key?h.props.children:h)?T:[T],i,s,a,c,u,l,d,f,p),g.base=i.__e,i.__u&=-161,g.__h.length&&l.push(g),_&&(g.__E=g.__=null)}catch(o){if(i.__v=null,f||null!=u){for(i.__u|=f?160:128;d&&8===d.nodeType&&d.nextSibling;)d=d.nextSibling;u[u.indexOf(d)]=null,i.__e=d}else i.__e=s.__e,i.__k=s.__k;ay.__e(o,i,s)}else null==u&&i.__v===s.__v?(i.__k=s.__k,i.__e=s.__e):i.__e=aQ(s.__e,i,s,a,c,u,l,f,p);(h=ay.diffed)&&h(i)}function aZ(o,i,s){i.__d=void 0;for(var a=0;a<s.length;a++)a0(s[a],s[++a],s[++a]);ay.__c&&ay.__c(i,o),o.some(function(i){try{o=i.__h,i.__h=[],o.some(function(o){o.call(i)})}catch(o){ay.__e(o,i.__v)}})}function aQ(o,i,s,a,c,u,l,d,f){var p,h,g,m,y,w,b,_=s.props,v=i.props,E=i.type;if("svg"===E?c="http://www.w3.org/2000/svg":"math"===E?c="http://www.w3.org/1998/Math/MathML":c||(c="http://www.w3.org/1999/xhtml"),null!=u){for(p=0;p<u.length;p++)if((y=u[p])&&"setAttribute"in y==!!E&&(E?y.localName===E:3===y.nodeType)){o=y,u[p]=null;break}}if(null==o){if(null===E)return document.createTextNode(v);o=document.createElementNS(c,E,v.is&&v),d&&(ay.__m&&ay.__m(i,u),d=!1),u=null}if(null===E)_===v||d&&o.data===v||(o.data=v);else{if(u=u&&am.call(o.childNodes),_=s.props||aA,!d&&null!=u)for(_={},p=0;p<o.attributes.length;p++)_[(y=o.attributes[p]).name]=y.value;for(p in _)if(y=_[p],"children"==p);else if("dangerouslySetInnerHTML"==p)g=y;else if(!(p in v)){if("value"==p&&"defaultValue"in v||"checked"==p&&"defaultChecked"in v)continue;aF(o,p,null,y,c)}for(p in v)y=v[p],"children"==p?m=y:"dangerouslySetInnerHTML"==p?h=y:"value"==p?w=y:"checked"==p?b=y:d&&"function"!=typeof y||_[p]===y||aF(o,p,y,_[p],c);if(h)d||g&&(h.__html===g.__html||h.__html===o.innerHTML)||(o.innerHTML=h.__html),i.__k=[];else if(g&&(o.innerHTML=""),aq(o,aC(m)?m:[m],i,s,a,"foreignObject"===E?"http://www.w3.org/1999/xhtml":c,u,l,u?u[0]:s.__k&&aM(s,0),d,f),null!=u)for(p=u.length;p--;)aj(u[p]);d||(p="value","progress"===E&&null==w?o.removeAttribute("value"):void 0!==w&&(w!==o[p]||"progress"===E&&!w||"option"===E&&w!==_[p])&&aF(o,p,w,_[p],c),p="checked",void 0!==b&&b!==o[p]&&aF(o,p,b,_[p],c))}return o}function a0(o,i,s){try{if("function"==typeof o){var a="function"==typeof o.__u;a&&o.__u(),a&&null==i||(o.__u=o(i))}else o.current=i}catch(o){ay.__e(o,s)}}function a1(o,i,s){var a,c;if(ay.unmount&&ay.unmount(o),(a=o.ref)&&(a.current&&a.current!==o.__e||a0(a,null,i)),null!=(a=o.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(o){ay.__e(o,i)}a.base=a.__P=null}if(a=o.__k)for(c=0;c<a.length;c++)a[c]&&a1(a[c],i,s||"function"!=typeof o.type);s||aj(o.__e),o.__c=o.__=o.__e=o.__d=void 0}function a2(o,i,s){return this.constructor(o,s)}function a5(o,i,s){var a,c,u,l;ay.__&&ay.__(o,i),c=(a="function"==typeof s)?null:s&&s.__k||i.__k,u=[],l=[],aY(i,o=(!a&&s||i).__k=aN(a$,null,[o]),c||aA,aA,i.namespaceURI,!a&&s?[s]:c?null:i.firstChild?am.call(i.childNodes):null,u,!a&&s?s:c?c.__e:i.firstChild,a,l),aZ(u,o,l)}function a3(o,i){a5(o,i,a3)}function a6(o,i,s){var a,c,u,l,d=aI({},o.props);for(u in o.type&&o.type.defaultProps&&(l=o.type.defaultProps),i)"key"==u?a=i[u]:"ref"==u?c=i[u]:d[u]=void 0===i[u]&&void 0!==l?l[u]:i[u];return arguments.length>2&&(d.children=arguments.length>3?am.call(arguments,2):s),aD(o.type,d,a||o.key,c||o.ref,null)}function a4(o,i){var s={__c:i="__cC"+aP++,__:o,Consumer:function(o,i){return o.children(i)},Provider:function(o){var s,a;return this.getChildContext||(s=new Set,(a={})[i]=this,this.getChildContext=function(){return a},this.componentWillUnmount=function(){s=null},this.shouldComponentUpdate=function(o){this.props.value!==o.value&&s.forEach(function(o){o.__e=!0,aW(o)})},this.sub=function(o){s.add(o);var i=o.componentWillUnmount;o.componentWillUnmount=function(){s&&s.delete(o),i&&i.call(o)}}),o.children}};return s.Provider.__=s.Consumer.contextType=s}am=aT.slice,ay={__e:function(o,i,s,a){for(var c,u,l;i=i.__;)if((c=i.__c)&&!c.__)try{if((u=c.constructor)&&null!=u.getDerivedStateFromError&&(c.setState(u.getDerivedStateFromError(o)),l=c.__d),null!=c.componentDidCatch&&(c.componentDidCatch(o,a||{}),l=c.__d),l)return c.__E=c}catch(i){o=i}throw o}},aw=0,ab=function(o){return null!=o&&null==o.constructor},aL.prototype.setState=function(o,i){var s;s=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=aI({},this.state),"function"==typeof o&&(o=o(aI({},s),this.props)),o&&aI(s,o),null!=o&&this.__v&&(i&&this._sb.push(i),aW(this))},aL.prototype.forceUpdate=function(o){this.__v&&(this.__e=!0,o&&this.__h.push(o),aW(this))},aL.prototype.render=a$,a_=[],aE="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,aS=function(o,i){return o.__v.__b-i.__v.__b},aK.__r=0,ak=0,ax=aX(!1),aR=aX(!0),aP=0;var a8=/[\s\n\\/='"\0<>]/,a9=/^(xlink|xmlns|xml)([A-Z])/,a7=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,ce=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,ct=new Set(["draggable","spellcheck"]),cn=/["&<]/;function cr(o){if(0===o.length||!1===cn.test(o))return o;for(var i=0,s=0,a="",c="";s<o.length;s++){switch(o.charCodeAt(s)){case 34:c="&quot;";break;case 38:c="&amp;";break;case 60:c="&lt;";break;default:continue}s!==i&&(a+=o.slice(i,s)),a+=c,i=s+1}return s!==i&&(a+=o.slice(i,s)),a}var co={},ci=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),cs=/[A-Z]/g;function ca(o){var i="";for(var s in o){var a=o[s];if(null!=a&&""!==a){var c="-"==s[0]?s:co[s]||(co[s]=s.replace(cs,"-$&").toLowerCase()),u=";";"number"!=typeof a||c.startsWith("--")||ci.has(c)||(u="px;"),i=i+c+":"+a+u}}return i||void 0}function cc(){this.__d=!0}function cu(o,i){return{__v:o,context:i,props:o.props,setState:cc,forceUpdate:cc,__d:!0,__h:new Array(0)}}function cl(o,i,s){if(!o.s){if(s instanceof cd){if(!s.s)return void(s.o=cl.bind(null,o,i));1&i&&(i=s.s),s=s.v}if(s&&s.then)return void s.then(cl.bind(null,o,i),cl.bind(null,o,2));o.s=i,o.v=s;const a=o.o;a&&a(o)}}var cd=null&&function(){function o(){}return o.prototype.then=function(i,s){var a=new o,c=this.s;if(c){var u=1&c?i:s;if(u){try{cl(a,1,u(this.v))}catch(o){cl(a,2,o)}return a}return this}return this.o=function(o){try{var c=o.v;1&o.s?cl(a,1,i?i(c):c):s?cl(a,1,s(c)):cl(a,2,c)}catch(o){cl(a,2,o)}},a},o}();function cf(o){return o instanceof cd&&1&o.s}function cp(o,i,s){for(var a;;){var c=o();if(cf(c)&&(c=c.v),!c)return u;if(c.then){a=0;break}var u=s();if(u&&u.then){if(!cf(u)){a=1;break}u=u.s}if(i){var l=i();if(l&&l.then&&!cf(l)){a=2;break}}}var d=new cd,f=cl.bind(null,d,2);return(0===a?c.then(h):1===a?u.then(p):l.then(g)).then(void 0,f),d;function p(a){u=a;do{if(i&&(l=i())&&l.then&&!cf(l))return void l.then(g).then(void 0,f);if(!(c=o())||cf(c)&&!c.v)return void cl(d,1,u);if(c.then)return void c.then(h).then(void 0,f);cf(u=s())&&(u=u.v)}while(!u||!u.then);u.then(p).then(void 0,f)}function h(o){o?(u=s())&&u.then?u.then(p).then(void 0,f):p(u):cl(d,1,u)}function g(){(c=o())?c.then?c.then(h).then(void 0,f):h(c):cl(d,1,u)}}function ch(o,i){try{var s=o()}catch(o){return i(!0,o)}return s&&s.then?s.then(i.bind(null,!1),i.bind(null,!0)):i(!1,s)}var cg,cm,cy,cw,cb=function(o,i){try{var s=e.__s;e.__s=!0,cg=e.__b,cm=e.diffed,cy=e.__r,cw=e.unmount;var a=t(n,null);return a.__k=[o],Promise.resolve(ch(function(){return Promise.resolve(cP(o,i||c_,!1,void 0,a,!0,void 0)).then(function(o){var i,s=function(){if(cE(o)){var s=function(){var o=c.join(ck);return i=1,o},a=0,c=o,u=cp(function(){return!!c.some(function(o){return o&&"function"==typeof o.then})&&a++<25},void 0,function(){return Promise.resolve(Promise.all(c)).then(function(o){c=o.flat()})});return u&&u.then?u.then(s):s()}}();return s&&s.then?s.then(function(s){return i?s:o}):i?s:o})},function(i,a){if(e.__c&&e.__c(o,cv),e.__s=s,cv.length=0,i)throw a;return a}))}catch(o){return Promise.reject(o)}},c_={},cv=[],cE=Array.isArray,cS=Object.assign,ck="";function cx(o,i,s){var a=ay.__s;ay.__s=!0,cg=ay.__b,cm=ay.diffed,cy=ay.__r,cw=ay.unmount;var c=aN(a$,null);c.__k=[o];try{var u=cP(o,i||c_,!1,void 0,c,!1,s);return cE(u)?u.join(ck):u}catch(o){if(o.then)throw new Error('Use "renderToStringAsync" for suspenseful rendering.');throw o}finally{ay.__c&&ay.__c(o,cv),ay.__s=a,cv.length=0}}function cR(o,i){var s,a=o.type,c=!0;return o.__c?(c=!1,(s=o.__c).state=s.__s):s=new a(o.props,i),o.__c=s,s.__v=o,s.props=o.props,s.context=i,s.__d=!0,null==s.state&&(s.state=c_),null==s.__s&&(s.__s=s.state),a.getDerivedStateFromProps?s.state=cS({},s.state,a.getDerivedStateFromProps(s.props,s.state)):c&&s.componentWillMount?(s.componentWillMount(),s.state=s.__s!==s.state?s.__s:s.state):!c&&s.componentWillUpdate&&s.componentWillUpdate(),cy&&cy(o),s.render(s.props,s.state,i)}function cP(o,i,s,a,c,u,l){if(null==o||!0===o||!1===o||o===ck)return ck;var d=typeof o;if("object"!=d)return"function"==d?ck:"string"==d?cr(o):o+ck;if(cE(o)){var f,p=ck;c.__k=o;for(var h=0;h<o.length;h++){var g=o[h];if(null!=g&&"boolean"!=typeof g){var m,y=cP(g,i,s,a,c,u,l);"string"==typeof y?p+=y:(f||(f=[]),p&&f.push(p),p=ck,cE(y)?(m=f).push.apply(m,y):f.push(y))}}return f?(p&&f.push(p),f):p}if(void 0!==o.constructor)return ck;o.__=c,cg&&cg(o);var w=o.type,b=o.props;if("function"==typeof w){var _,v,E,S=i;if(w===a$){if("tpl"in b){for(var k=ck,x=0;x<b.tpl.length;x++)if(k+=b.tpl[x],b.exprs&&x<b.exprs.length){var R=b.exprs[x];if(null==R)continue;"object"!=typeof R||void 0!==R.constructor&&!cE(R)?k+=R:k+=cP(R,i,s,a,o,u,l)}return k}if("UNSTABLE_comment"in b)return"\x3c!--"+cr(b.UNSTABLE_comment)+"--\x3e";v=b.children}else{if(null!=(_=w.contextType)){var P=i[_.__c];S=P?P.props.value:_.__}var A=w.prototype&&"function"==typeof w.prototype.render;if(A)v=cR(o,S),E=o.__c;else{o.__c=E=cu(o,S);for(var T=0;E.__d&&T++<25;)E.__d=!1,cy&&cy(o),v=w.call(E,b,S);E.__d=!0}if(null!=E.getChildContext&&(i=cS({},i,E.getChildContext())),A&&ay.errorBoundaries&&(w.getDerivedStateFromError||E.componentDidCatch)){v=null!=v&&v.type===a$&&null==v.key&&null==v.props.tpl?v.props.children:v;try{return cP(v,i,s,a,o,u,l)}catch(c){return w.getDerivedStateFromError&&(E.__s=w.getDerivedStateFromError(c)),E.componentDidCatch&&E.componentDidCatch(c,c_),E.__d?(v=cR(o,i),null!=(E=o.__c).getChildContext&&(i=cS({},i,E.getChildContext())),cP(v=null!=v&&v.type===a$&&null==v.key&&null==v.props.tpl?v.props.children:v,i,s,a,o,u,l)):ck}finally{cm&&cm(o),o.__=null,cw&&cw(o)}}}v=null!=v&&v.type===a$&&null==v.key&&null==v.props.tpl?v.props.children:v;try{var O=cP(v,i,s,a,o,u,l);return cm&&cm(o),o.__=null,ay.unmount&&ay.unmount(o),O}catch(c){if(!u&&l&&l.onError){var C=l.onError(c,o,function(c){return cP(c,i,s,a,o,u,l)});if(void 0!==C)return C;var I=ay.__e;return I&&I(c,o),ck}if(!u)throw c;if(!c||"function"!=typeof c.then)throw c;return c.then(function c(){try{return cP(v,i,s,a,o,u,l)}catch(d){if(!d||"function"!=typeof d.then)throw d;return d.then(function(){return cP(v,i,s,a,o,u,l)},c)}})}}var j,N="<"+w,D=ck;for(var U in b){var $=b[U];if("function"!=typeof $||"class"===U||"className"===U){switch(U){case"children":j=$;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in b)continue;U="for";break;case"className":if("class"in b)continue;U="class";break;case"defaultChecked":U="checked";break;case"defaultSelected":U="selected";break;case"defaultValue":case"value":switch(U="value",w){case"textarea":j=$;continue;case"select":a=$;continue;case"option":a!=$||"selected"in b||(N+=" selected")}break;case"dangerouslySetInnerHTML":D=$&&$.__html;continue;case"style":"object"==typeof $&&($=ca($));break;case"acceptCharset":U="accept-charset";break;case"httpEquiv":U="http-equiv";break;default:if(a9.test(U))U=U.replace(a9,"$1:$2").toLowerCase();else{if(a8.test(U))continue;"-"!==U[4]&&!ct.has(U)||null==$?s?ce.test(U)&&(U="panose1"===U?"panose-1":U.replace(/([A-Z])/g,"-$1").toLowerCase()):a7.test(U)&&(U=U.toLowerCase()):$+=ck}}null!=$&&!1!==$&&(N=!0===$||$===ck?N+" "+U:N+" "+U+'="'+("string"==typeof $?cr($):$+ck)+'"')}}if(a8.test(w))throw new Error(w+" is not a valid HTML tag name in "+N+">");if(D||("string"==typeof j?D=cr(j):null!=j&&!1!==j&&!0!==j&&(D=cP(j,i,"svg"===w||"foreignObject"!==w&&s,a,o,u,l))),cm&&cm(o),o.__=null,cw&&cw(o),!D&&cA.has(w))return N+"/>";var L="</"+w+">",M=N+">";return cE(D)?[M].concat(D,[L]):"string"!=typeof D?[M,D,L]:M+D+L}var cA=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),cT=null&&cx,cO=null&&cx;const cC=null&&cx;var cI=/["&<]/;function cj(o){if(0===o.length||!1===cI.test(o))return o;for(var i=0,s=0,a="",c="";s<o.length;s++){switch(o.charCodeAt(s)){case 34:c="&quot;";break;case 38:c="&amp;";break;case 60:c="&lt;";break;default:continue}s!==i&&(a+=o.slice(i,s)),a+=c,i=s+1}return s!==i&&(a+=o.slice(i,s)),a}var cN=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,cD=0,cU=Array.isArray;function c$(o,i,s,a,c,u){i||(i={});var l,d,f=i;"ref"in i&&(l=i.ref,delete i.ref);var p={type:o,props:f,key:s,ref:l,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--cD,__i:-1,__u:0,__source:c,__self:u};if("function"==typeof o&&(l=o.defaultProps))for(d in l)void 0===f[d]&&(f[d]=l[d]);return ay.vnode&&ay.vnode(p),p}function cL(o){var i=c$(e,{tpl:o,exprs:[].slice.call(arguments,1)});return i.key=i.__v,i}var cM={},cH=/[A-Z]/g;function cW(o,i){if(r.attr){var s=r.attr(o,i);if("string"==typeof s)return s}if("ref"===o||"key"===o)return"";if("style"===o&&"object"==typeof i){var a="";for(var c in i){var u=i[c];if(null!=u&&""!==u){var l="-"==c[0]?c:cM[c]||(cM[c]=c.replace(cH,"-$&").toLowerCase()),d=";";"number"!=typeof u||l.startsWith("--")||cN.test(l)||(d="px;"),a=a+l+":"+u+d}}return o+'="'+a+'"'}return null==i||!1===i||"function"==typeof i||"object"==typeof i?"":!0===i?o:o+'="'+cj(i)+'"'}function cK(o){if(null==o||"boolean"==typeof o||"function"==typeof o)return null;if("object"==typeof o){if(void 0===o.constructor)return o;if(cU(o)){for(var i=0;i<o.length;i++)o[i]=cK(o[i]);return o}}return cj(""+o)};function cq(o){const{url:i,error:s="default",theme:a}=o;const c=`${i}/signin`;const u={default:{status:200,heading:"Error",message:c$("p",{children:c$("a",{className:"site",href:i?.origin,children:i?.host})})},Configuration:{status:500,heading:"Server error",message:c$("div",{children:[c$("p",{children:"There is a problem with the server configuration."}),c$("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:c$("div",{children:[c$("p",{children:"You do not have permission to sign in."}),c$("p",{children:c$("a",{className:"button",href:c,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:c$("div",{children:[c$("p",{children:"The sign in link is no longer valid."}),c$("p",{children:"It may have been used already or it may have expired."})]}),signin:c$("a",{className:"button",href:c,children:"Sign in"})}};const{status:l,heading:d,message:f,signin:p}=u[s]??u.default;return{status:l,html:c$("div",{className:"error",children:[a?.brandColor&&c$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${a?.brandColor}
        }
      `}}),c$("div",{className:"card",children:[a?.logo&&c$("img",{src:a?.logo,alt:"Logo",className:"logo"}),c$("h1",{children:d}),c$("div",{className:"message",children:f}),p]})]})}};async function cB(o,i){const s=window.SimpleWebAuthnBrowser;async function a(s){const a=new URL(`${o}/webauthn-options/${i}`);if(s)a.searchParams.append("action",s);const c=u();c.forEach(o=>{a.searchParams.append(o.name,o.value)});const l=await fetch(a);if(!l.ok){console.error("Failed to fetch options",l);return}return l.json()}function c(){const o=`#${i}-form`;const s=document.querySelector(o);if(!s)throw new Error(`Form '${o}' not found`);return s}function u(){const o=c();const i=Array.from(o.querySelectorAll("input[data-form-field]"));return i}async function l(o,i){const s=c();if(o){const i=document.createElement("input");i.type="hidden";i.name="action";i.value=o;s.appendChild(i)}if(i){const o=document.createElement("input");o.type="hidden";o.name="data";o.value=JSON.stringify(i);s.appendChild(o)}return s.submit()}async function d(o,i){const a=await s.startAuthentication(o,i);return await l("authenticate",a)}async function f(o){const i=u();i.forEach(o=>{if(o.required&&!o.value){throw new Error(`Missing required field: ${o.name}`)}});const a=await s.startRegistration(o);return await l("register",a)}async function p(){if(!s.browserSupportsWebAuthnAutofill())return;const o=await a("authenticate");if(!o){console.error("Failed to fetch option for autofill authentication");return}try{await d(o.options,true)}catch(o){console.error(o)}}async function h(){const o=c();if(!s.browserSupportsWebAuthn()){o.style.display="none";return}if(o){o.addEventListener("submit",async o=>{o.preventDefault();const i=await a(undefined);if(!i){console.error("Failed to fetch options for form submission");return}if(i.action==="authenticate"){try{await d(i.options,false)}catch(o){console.error(o)}}else if(i.action==="register"){try{await f(i.options)}catch(o){console.error(o)}}})}}h();p()};const cJ={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."};function cz(o){const i=`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${cB})(authURL, "${o}");
`;return c$(a$,{children:c$("script",{dangerouslySetInnerHTML:{__html:i}})})}function cV(o){const{csrfToken:i,providers:s=[],callbackUrl:a,theme:c,email:u,error:l}=o;if(typeof document!=="undefined"&&c?.brandColor){document.documentElement.style.setProperty("--brand-color",c.brandColor)}if(typeof document!=="undefined"&&c?.buttonText){document.documentElement.style.setProperty("--button-text-color",c.buttonText)}const d=l&&(cJ[l]??cJ.default);const f="https://authjs.dev/img/providers";const p=s.find(o=>o.type==="webauthn"&&o.enableConditionalUI)?.id;return c$("div",{className:"signin",children:[c?.brandColor&&c$("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${c.brandColor}}`}}),c?.buttonText&&c$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${c.buttonText}
        }
      `}}),c$("div",{className:"card",children:[d&&c$("div",{className:"error",children:c$("p",{children:d})}),c?.logo&&c$("img",{src:c.logo,alt:"Logo",className:"logo"}),s.map((o,c)=>{let l,d,p;if(o.type==="oauth"||o.type==="oidc"){;({bg:l="#fff",brandColor:d,logo:p=`${f}/${o.id}.svg`}=o.style??{})}const h=d??l??"#fff";return c$("div",{className:"provider",children:[o.type==="oauth"||o.type==="oidc"?c$("form",{action:o.signinUrl,method:"POST",children:[c$("input",{type:"hidden",name:"csrfToken",value:i}),a&&c$("input",{type:"hidden",name:"callbackUrl",value:a}),c$("button",{type:"submit",className:"button",style:{"--provider-brand-color":h},tabIndex:0,children:[c$("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",o.name]}),p&&c$("img",{loading:"lazy",height:24,src:p})]})]}):null,(o.type==="email"||o.type==="credentials"||o.type==="webauthn")&&c>0&&s[c-1].type!=="email"&&s[c-1].type!=="credentials"&&s[c-1].type!=="webauthn"&&c$("hr",{}),o.type==="email"&&c$("form",{action:o.signinUrl,method:"POST",children:[c$("input",{type:"hidden",name:"csrfToken",value:i}),c$("label",{className:"section-header",htmlFor:`input-email-for-${o.id}-provider`,children:"Email"}),c$("input",{id:`input-email-for-${o.id}-provider`,autoFocus:true,type:"email",name:"email",value:u,placeholder:"<EMAIL>",required:true}),c$("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",o.name]})]}),o.type==="credentials"&&c$("form",{action:o.callbackUrl,method:"POST",children:[c$("input",{type:"hidden",name:"csrfToken",value:i}),Object.keys(o.credentials).map(i=>{return c$("div",{children:[c$("label",{className:"section-header",htmlFor:`input-${i}-for-${o.id}-provider`,children:o.credentials[i].label??i}),c$("input",{name:i,id:`input-${i}-for-${o.id}-provider`,type:o.credentials[i].type??"text",placeholder:o.credentials[i].placeholder??"",...o.credentials[i]})]},`input-group-${o.id}`)}),c$("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",o.name]})]}),o.type==="webauthn"&&c$("form",{action:o.callbackUrl,method:"POST",id:`${o.id}-form`,children:[c$("input",{type:"hidden",name:"csrfToken",value:i}),Object.keys(o.formFields).map(i=>{return c$("div",{children:[c$("label",{className:"section-header",htmlFor:`input-${i}-for-${o.id}-provider`,children:o.formFields[i].label??i}),c$("input",{name:i,"data-form-field":true,id:`input-${i}-for-${o.id}-provider`,type:o.formFields[i].type??"text",placeholder:o.formFields[i].placeholder??"",...o.formFields[i]})]},`input-group-${o.id}`)}),c$("button",{id:`submitButton-${o.id}`,type:"submit",tabIndex:0,children:["Sign in with ",o.name]})]}),(o.type==="email"||o.type==="credentials"||o.type==="webauthn")&&c+1<s.length&&c$("hr",{})]},o.id)})]}),p&&cz(p)]})};function cG(o){const{url:i,csrfToken:s,theme:a}=o;return c$("div",{className:"signout",children:[a?.brandColor&&c$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${a.brandColor}
        }
      `}}),a?.buttonText&&c$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),c$("div",{className:"card",children:[a?.logo&&c$("img",{src:a.logo,alt:"Logo",className:"logo"}),c$("h1",{children:"Signout"}),c$("p",{children:"Are you sure you want to sign out?"}),c$("form",{action:i?.toString(),method:"POST",children:[c$("input",{type:"hidden",name:"csrfToken",value:s}),c$("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})};const cF=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function cX(o){const{url:i,theme:s}=o;return c$("div",{className:"verify-request",children:[s.brandColor&&c$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${s.brandColor}
        }
      `}}),c$("div",{className:"card",children:[s.logo&&c$("img",{src:s.logo,alt:"Logo",className:"logo"}),c$("h1",{children:"Check your email"}),c$("p",{children:"A sign in link has been sent to your email address."}),c$("p",{children:c$("a",{className:"site",href:i.origin,children:i.host})})]})]})};function cY({html:o,title:i,status:s,cookies:a,theme:c,headTags:u}){return{cookies:a,status:s,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${cF}</style><title>${i}</title>${u??""}</head><body class="__next-auth-theme-${c?.colorScheme??"auto"}"><div class="page">${cx(o)}</div></body></html>`}}function cZ(o){const{url:i,theme:s,query:a,cookies:c,pages:u,providers:l}=o;return{csrf(o,i,s){if(!o){return{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:i.csrfToken},cookies:s}}i.logger.warn("csrf-disabled");s.push({name:i.cookies.csrfToken.name,value:"",options:{...i.cookies.csrfToken.options,maxAge:0}});return{status:404,cookies:s}},providers(o){return{headers:{"Content-Type":"application/json"},body:o.reduce((o,{id:i,name:s,type:a,signinUrl:c,callbackUrl:u})=>{o[i]={id:i,name:s,type:a,signinUrl:c,callbackUrl:u};return o},{})}},signin(i,d){if(i)throw new rQ("Unsupported action");if(u?.signIn){let i=`${u.signIn}${u.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:o.callbackUrl??"/"})}`;if(d)i=`${i}&${new URLSearchParams({error:d})}`;return{redirect:i,cookies:c}}const f=l?.find(o=>o.type==="webauthn"&&o.enableConditionalUI&&!!o.simpleWebAuthnBrowserVersion);let p="";if(f){const{simpleWebAuthnBrowserVersion:o}=f;p=`<script src="https://unpkg.com/@simplewebauthn/browser@${o}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return cY({cookies:c,theme:s,html:cV({csrfToken:o.csrfToken,providers:o.providers?.filter(o=>["email","oauth","oidc"].includes(o.type)||o.type==="credentials"&&o.credentials||o.type==="webauthn"&&o.formFields||false),callbackUrl:o.callbackUrl,theme:o.theme,error:d,...a}),title:"Sign In",headTags:p})},signout(){if(u?.signOut)return{redirect:u.signOut,cookies:c};return cY({cookies:c,theme:s,html:cG({csrfToken:o.csrfToken,url:i,theme:s}),title:"Sign Out"})},verifyRequest(o){if(u?.verifyRequest)return{redirect:`${u.verifyRequest}${i?.search??""}`,cookies:c};return cY({cookies:c,theme:s,html:cX({url:i,theme:s,...o}),title:"Verify Request"})},error(o){if(u?.error){return{redirect:`${u.error}${u.error.includes("?")?"&":"?"}error=${o}`,cookies:c}}return cY({cookies:c,theme:s,...cq({url:i,theme:s,error:o}),title:"Error"})}}};function cQ(o,i=Date.now()){return new Date(i+o*1e3)};async function c0(o,i,s,a){if(!s?.providerAccountId||!s.type)throw new Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(s.type))throw new Error("Provider not supported");const{adapter:c,jwt:u,events:l,session:{strategy:d,generateSessionToken:f}}=a;if(!c){return{user:i,account:s}}const p=i;let h=s;const{createUser:g,updateUser:m,getUser:y,getUserByAccount:w,getUserByEmail:b,linkAccount:_,createSession:v,getSessionAndUser:E,deleteSession:S}=c;let k=null;let x=null;let R=false;const P=d==="jwt";if(o){if(P){try{const i=a.cookies.sessionToken.name;k=await u.decode({...u,token:o,salt:i});if(k&&"sub"in k&&k.sub){x=await y(k.sub)}}catch{}}else{const i=await E(o);if(i){k=i.session;x=i.user}}}if(h.type==="email"){const i=await b(p.email);if(i){if(x?.id!==i.id&&!P&&o){await S(o)}x=await m({id:i.id,emailVerified:new Date});await l.updateUser?.({user:x})}else{x=await g({...p,emailVerified:new Date});await l.createUser?.({user:x});R=true}k=P?{}:await v({sessionToken:f(),userId:x.id,expires:cQ(a.session.maxAge)});return{session:k,user:x,isNewUser:R}}else if(h.type==="webauthn"){const o=await w({providerAccountId:h.providerAccountId,provider:h.provider});if(o){if(x){if(o.id===x.id){const o={...h,userId:x.id};return{session:k,user:x,isNewUser:R,account:o}}throw new oe("The account is already associated with another user",{provider:h.provider})}k=P?{}:await v({sessionToken:f(),userId:o.id,expires:cQ(a.session.maxAge)});const i={...h,userId:o.id};return{session:k,user:o,isNewUser:R,account:i}}else{if(x){await _({...h,userId:x.id});await l.linkAccount?.({user:x,account:h,profile:p});const o={...h,userId:x.id};return{session:k,user:x,isNewUser:R,account:o}}const o=p.email?await b(p.email):null;if(o){throw new oe("Another account already exists with the same e-mail address",{provider:h.provider})}else{x=await g({...p})}await l.createUser?.({user:x});await _({...h,userId:x.id});await l.linkAccount?.({user:x,account:h,profile:p});k=P?{}:await v({sessionToken:f(),userId:x.id,expires:cQ(a.session.maxAge)});const i={...h,userId:x.id};return{session:k,user:x,isNewUser:true,account:i}}}const A=await w({providerAccountId:h.providerAccountId,provider:h.provider});if(A){if(x){if(A.id===x.id){return{session:k,user:x,isNewUser:R}}throw new rz("The account is already associated with another user",{provider:h.provider})}k=P?{}:await v({sessionToken:f(),userId:A.id,expires:cQ(a.session.maxAge)});return{session:k,user:A,isNewUser:R}}else{const{provider:o}=a;const{type:i,provider:s,providerAccountId:c,userId:u,...d}=h;const m={providerAccountId:c,provider:s,type:i,userId:u};h=Object.assign(o.account(d)??{},m);if(x){await _({...h,userId:x.id});await l.linkAccount?.({user:x,account:h,profile:p});return{session:k,user:x,isNewUser:R}}const y=p.email?await b(p.email):null;if(y){const o=a.provider;if(o?.allowDangerousEmailAccountLinking){x=y;R=false}else{throw new rz("Another account already exists with the same e-mail address",{provider:h.provider})}}else{x=await g({...p,emailVerified:null});R=true}await l.createUser?.({user:x});await _({...h,userId:x.id});await l.linkAccount?.({user:x,account:h,profile:p});k=P?{}:await v({sessionToken:f(),userId:x.id,expires:cQ(a.session.maxAge)});return{session:k,user:x,isNewUser:R}}};let c1;if(typeof navigator==="undefined"||!navigator.userAgent?.startsWith?.("Mozilla/5.0 ")){const o="oauth4webapi";const i="v3.6.0";c1=`${o}/${i}`}function c2(o,i){if(o==null){return false}try{return o instanceof i||Object.getPrototypeOf(o)[Symbol.toStringTag]===i.prototype[Symbol.toStringTag]}catch{return false}}const c5="ERR_INVALID_ARG_VALUE";const c3="ERR_INVALID_ARG_TYPE";function c6(o,i,s){const a=new TypeError(o,{cause:s});Object.assign(a,{code:i});return a}const c4=Symbol();const c8=Symbol();const c9=Symbol();const c7=Symbol();const ue=Symbol();const ut=Symbol();const un=Symbol();const ur=new TextEncoder;const uo=new TextDecoder;function ui(o){if(typeof o==="string"){return ur.encode(o)}return uo.decode(o)}let us;if(Uint8Array.prototype.toBase64){us=o=>{if(o instanceof ArrayBuffer){o=new Uint8Array(o)}return o.toBase64({alphabet:"base64url",omitPadding:true})}}else{const o=32768;us=i=>{if(i instanceof ArrayBuffer){i=new Uint8Array(i)}const s=[];for(let a=0;a<i.byteLength;a+=o){s.push(String.fromCharCode.apply(null,i.subarray(a,a+o)))}return btoa(s.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}}let ua;if(Uint8Array.fromBase64){ua=o=>{try{return Uint8Array.fromBase64(o,{alphabet:"base64url"})}catch(o){throw c6("The input to be decoded is not correctly encoded.",c5,o)}}}else{ua=o=>{try{const i=atob(o.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,""));const s=new Uint8Array(i.length);for(let o=0;o<i.length;o++){s[o]=i.charCodeAt(o)}return s}catch(o){throw c6("The input to be decoded is not correctly encoded.",c5,o)}}}function uc(o){if(typeof o==="string"){return ua(o)}return us(o)}class uu extends Error{code;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=l3;Error.captureStackTrace?.(this,this.constructor)}}class ul extends Error{code;constructor(o,i){super(o,i);this.name=this.constructor.name;if(i?.code){this.code=i?.code}Error.captureStackTrace?.(this,this.constructor)}}function ud(o,i,s){return new ul(o,{code:i,cause:s})}function uf(o,i){if(!(o instanceof CryptoKey)){throw c6(`${i} must be a CryptoKey`,c3)}}function up(o,i){uf(o,i);if(o.type!=="private"){throw c6(`${i} must be a private CryptoKey`,c5)}}function uh(o,i){uf(o,i);if(o.type!=="public"){throw c6(`${i} must be a public CryptoKey`,c5)}}function ug(o){return o.toLowerCase().replace(/^application\//,"")}function um(o){if(o===null||typeof o!=="object"||Array.isArray(o)){return false}return true}function uy(o){if(c2(o,Headers)){o=Object.fromEntries(o.entries())}const i=new Headers(o??{});if(c1&&!i.has("user-agent")){i.set("user-agent",c1)}if(i.has("authorization")){throw c6('"options.headers" must not include the "authorization" header name',c5)}return i}function uw(o,i){if(i!==undefined){if(typeof i==="function"){i=i(o.href)}if(!(i instanceof AbortSignal)){throw c6('"options.signal" must return or be an instance of AbortSignal',c3)}return i}return undefined}function ub(o){if(o.includes("//")){return o.replace("//","/")}return o}function u_(o,i,s=false){if(o.pathname==="/"){o.pathname=i}else{o.pathname=ub(`${i}/${s?o.pathname:o.pathname.replace(/(\/)$/,"")}`)}return o}function uv(o,i){o.pathname=ub(`${o.pathname}/${i}`);return o}async function uE(o,i,s,a){if(!(o instanceof URL)){throw c6(`"${i}" must be an instance of URL`,c3)}u4(o,a?.[c4]!==true);const c=s(new URL(o.href));const u=uy(a?.headers);u.set("accept","application/json");return(a?.[c7]||fetch)(c.href,{body:undefined,headers:Object.fromEntries(u.entries()),method:"GET",redirect:"manual",signal:uw(c,a?.signal)})}async function uS(o,i){return uE(o,"issuerIdentifier",o=>{switch(i?.algorithm){case undefined:case"oidc":uv(o,".well-known/openid-configuration");break;case"oauth2":u_(o,".well-known/oauth-authorization-server");break;default:throw c6('"options.algorithm" must be "oidc" (default), or "oauth2"',c5)}return o},i)}function uk(o,i,s,a,c){try{if(typeof o!=="number"||!Number.isFinite(o)){throw c6(`${s} must be a number`,c3,c)}if(o>0)return;if(i){if(o!==0){throw c6(`${s} must be a non-negative number`,c5,c)}return}throw c6(`${s} must be a positive number`,c5,c)}catch(o){if(a){throw ud(o.message,a,c)}throw o}}function ux(o,i,s,a){try{if(typeof o!=="string"){throw c6(`${i} must be a string`,c3,a)}if(o.length===0){throw c6(`${i} must not be empty`,c5,a)}}catch(o){if(s){throw ud(o.message,s,a)}throw o}}async function uR(o,i){const s=o;if(!(s instanceof URL)&&s!==fe){throw c6('"expectedIssuerIdentifier" must be an instance of URL',c3)}if(!c2(i,Response)){throw c6('"response" must be an instance of Response',c3)}if(i.status!==200){throw ud('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',dt,i)}dw(i);const a=await d9(i);ux(a.issuer,'"response" body "issuer" property',l9,{body:a});if(s!==fe&&new URL(a.issuer).href!==s.href){throw ud('"response" body "issuer" property does not match the expected value',da,{expected:s.href,body:a,attribute:"issuer"})}return a}function uP(o){uO(o,"application/json")}function uA(o,...i){let s='"response" content-type must be ';if(i.length>2){const o=i.pop();s+=`${i.join(", ")}, or ${o}`}else if(i.length===2){s+=`${i[0]} or ${i[1]}`}else{s+=i[0]}return ud(s,de,o)}function uT(o,...i){if(!i.includes(lT(o))){throw uA(o,...i)}}function uO(o,i){if(lT(o)!==i){throw uA(o,i)}}function uC(){return uc(crypto.getRandomValues(new Uint8Array(32)))}function uI(){return uC()}function uj(){return uC()}function uN(){return uC()}async function uD(o){ux(o,"codeVerifier");return uc(await crypto.subtle.digest("SHA-256",ui(o)))}function uU(o){if(o instanceof CryptoKey){return{key:o}}if(!(o?.key instanceof CryptoKey)){return{}}if(o.kid!==undefined){ux(o.kid,'"kid"')}return{key:o.key,kid:o.kid}}function u$(o){switch(o.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new uu("unsupported RsaHashedKeyAlgorithm hash name",{cause:o})}}function uL(o){switch(o.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new uu("unsupported RsaHashedKeyAlgorithm hash name",{cause:o})}}function uM(o){switch(o.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new uu("unsupported EcKeyAlgorithm namedCurve",{cause:o})}}function uH(o){switch(o.algorithm.name){case"RSA-PSS":return u$(o);case"RSASSA-PKCS1-v1_5":return uL(o);case"ECDSA":return uM(o);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new uu("unsupported CryptoKey algorithm name",{cause:o})}}function uW(o){const i=o?.[c8];return typeof i==="number"&&Number.isFinite(i)?i:0}function uK(o){const i=o?.[c9];return typeof i==="number"&&Number.isFinite(i)&&Math.sign(i)!==-1?i:30}function uq(){return Math.floor(Date.now()/1e3)}function uB(o){if(typeof o!=="object"||o===null){throw c6('"as" must be an object',c3)}ux(o.issuer,'"as.issuer"')}function uJ(o){if(typeof o!=="object"||o===null){throw c6('"client" must be an object',c3)}ux(o.client_id,'"client.client_id"')}function uz(o){return encodeURIComponent(o).replace(/(?:[-_.!~*'()]|%20)/g,o=>{switch(o){case"-":case"_":case".":case"!":case"~":case"*":case"'":case"(":case")":return`%${o.charCodeAt(0).toString(16).toUpperCase()}`;case"%20":return"+";default:throw new Error}})}function uV(o){ux(o,'"clientSecret"');return(i,s,a,c)=>{a.set("client_id",s.client_id);a.set("client_secret",o)}}function uG(o){ux(o,'"clientSecret"');return(i,s,a,c)=>{const u=uz(s.client_id);const l=uz(o);const d=btoa(`${u}:${l}`);c.set("authorization",`Basic ${d}`)}}function uF(o,i){const s=uq()+uW(i);return{jti:uC(),aud:o.issuer,exp:s+60,iat:s,nbf:s,iss:i.client_id,sub:i.client_id}}function uX(o,i){const{key:s,kid:a}=uU(o);up(s,'"clientPrivateKey.key"');return async(o,c,u,l)=>{const d={alg:uH(s),kid:a};const f=uF(o,c);i?.[ue]?.(d,f);u.set("client_id",c.client_id);u.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer");u.set("client_assertion",await u0(d,f,s))}}function uY(o,i){ux(o,'"clientSecret"');const s=i?.[ue];let a;return async(i,c,u,l)=>{a||=await crypto.subtle.importKey("raw",ui(o),{hash:"SHA-256",name:"HMAC"},false,["sign"]);const d={alg:"HS256"};const f=uF(i,c);s?.(d,f);const p=`${uc(ui(JSON.stringify(d)))}.${uc(ui(JSON.stringify(f)))}`;const h=await crypto.subtle.sign(a.algorithm,a,ui(p));u.set("client_id",c.client_id);u.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer");u.set("client_assertion",`${p}.${uc(new Uint8Array(h))}`)}}function uZ(){return(o,i,s,a)=>{s.set("client_id",i.client_id)}}function uQ(){return uZ()}async function u0(o,i,s){if(!s.usages.includes("sign")){throw c6('CryptoKey instances used for signing assertions must include "sign" in their "usages"',c5)}const a=`${uc(ui(JSON.stringify(o)))}.${uc(ui(JSON.stringify(i)))}`;const c=uc(await crypto.subtle.sign(dP(s),s,ui(a)));return`${a}.${c}`}async function u1(o,i,s,a,c){uB(o);uJ(i);s=new URLSearchParams(s);const{key:u,kid:l}=uU(a);up(u,'"privateKey.key"');s.set("client_id",i.client_id);const d=uq()+uW(i);const f={...Object.fromEntries(s.entries()),jti:uC(),aud:o.issuer,exp:d+60,iat:d,nbf:d,iss:i.client_id};let p;if(s.has("resource")&&(p=s.getAll("resource"))&&p.length>1){f.resource=p}{let o=s.get("max_age");if(o!==null){f.max_age=parseInt(o,10);uk(f.max_age,true,'"max_age" parameter')}}{let o=s.get("claims");if(o!==null){try{f.claims=JSON.parse(o)}catch(o){throw ud('failed to parse the "claims" parameter as JSON',l8,o)}if(!um(f.claims)){throw c6('"claims" parameter must be a JSON with a top level object',c5)}}}{let o=s.get("authorization_details");if(o!==null){try{f.authorization_details=JSON.parse(o)}catch(o){throw ud('failed to parse the "authorization_details" parameter as JSON',l8,o)}if(!Array.isArray(f.authorization_details)){throw c6('"authorization_details" parameter must be a JSON with a top level array',c5)}}}const h={alg:uH(u),typ:"oauth-authz-req+jwt",kid:l};c?.[ue]?.(h,f);return u0(h,f,u)}let u2;async function u5(o){const{kty:i,e:s,n:a,x:c,y:u,crv:l}=await crypto.subtle.exportKey("jwk",o);const d={kty:i,e:s,n:a,x:c,y:u,crv:l};u2.set(o,d);return d}async function u3(o){u2||=new WeakMap;return u2.get(o)||u5(o)}const u6=URL.parse?(o,i)=>URL.parse(o,i):(o,i)=>{try{return new URL(o,i)}catch{return null}};function u4(o,i){if(i&&o.protocol!=="https:"){throw ud("only requests to HTTPS are allowed",dn,o)}if(o.protocol!=="https:"&&o.protocol!=="http:"){throw ud("only HTTP and HTTPS requests are allowed",dr,o)}}function u8(o,i,s,a){let c;if(typeof o!=="string"||!(c=u6(o))){throw ud(`authorization server metadata does not contain a valid ${s?`"as.mtls_endpoint_aliases.${i}"`:`"as.${i}"`}`,o===undefined?du:dl,{attribute:s?`mtls_endpoint_aliases.${i}`:i})}u4(c,a);return c}function u9(o,i,s,a){if(s&&o.mtls_endpoint_aliases&&i in o.mtls_endpoint_aliases){return u8(o.mtls_endpoint_aliases[i],i,s,a)}return u8(o[i],i,s,a)}async function u7(o,i,s,a,c){uB(o);uJ(i);const u=u9(o,"pushed_authorization_request_endpoint",i.use_mtls_endpoint_aliases,c?.[c4]!==true);const l=new URLSearchParams(a);l.set("client_id",i.client_id);const d=uy(c?.headers);d.set("accept","application/json");if(c?.DPoP!==undefined){lb(c.DPoP);await c.DPoP.addProof(u,d,"POST")}const f=await lC(o,i,s,u,l,d,c);c?.DPoP?.cacheNonce(f);return f}class le{#h;#g;#m;#y;#w;#b;#_;constructor(o,i,s){up(i?.privateKey,'"DPoP.privateKey"');uh(i?.publicKey,'"DPoP.publicKey"');if(!i.publicKey.extractable){throw c6('"DPoP.publicKey.extractable" must be true',c5)}this.#w=s?.[ue];this.#y=uW(o);this.#g=i.privateKey;this.#m=i.publicKey;lJ.add(this)}#v(o){this.#b||=new Map;let i=this.#b.get(o);if(i){this.#b.delete(o);this.#b.set(o,i)}return i}#E(o,i){this.#b||=new Map;this.#b.delete(o);if(this.#b.size===100){this.#b.delete(this.#b.keys().next().value)}this.#b.set(o,i)}async calculateThumbprint(){if(!this.#_){const o=await crypto.subtle.exportKey("jwk",this.#m);let i;switch(o.kty){case"EC":i={crv:o.crv,kty:o.kty,x:o.x,y:o.y};break;case"OKP":i={crv:o.crv,kty:o.kty,x:o.x};break;case"RSA":i={e:o.e,kty:o.kty,n:o.n};break;default:throw new uu("unsupported JWK",{cause:{jwk:o}})}this.#_||=uc(await crypto.subtle.digest({name:"SHA-256"},ui(JSON.stringify(i))))}return this.#_}async addProof(o,i,s,a){this.#h||={alg:uH(this.#g),typ:"dpop+jwt",jwk:await u3(this.#m)};const c=this.#v(o.origin);const u=uq()+this.#y;const l={iat:u,jti:uC(),htm:s,nonce:c,htu:`${o.origin}${o.pathname}`,ath:a?uc(await crypto.subtle.digest("SHA-256",ui(a))):undefined};this.#w?.(this.#h,l);i.set("dpop",await u0(this.#h,l,this.#g))}cacheNonce(o){try{const i=o.headers.get("dpop-nonce");if(i){this.#E(new URL(o.url).origin,i)}}catch{}}}function lt(o){if(o instanceof li){const{0:i,length:s}=o.cause;return s===1&&i.scheme==="dpop"&&i.parameters.error==="use_dpop_nonce"}if(o instanceof lr){return o.error==="use_dpop_nonce"}return false}function ln(o,i,s){return new le(o,i,s)}class lr extends Error{cause;code;error;status;error_description;response;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=l5;this.cause=i.cause;this.error=i.cause.error;this.status=i.response.status;this.error_description=i.cause.error_description;Object.defineProperty(this,"response",{enumerable:false,value:i.response});Error.captureStackTrace?.(this,this.constructor)}}class lo extends Error{cause;code;error;error_description;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=l6;this.cause=i.cause;this.error=i.cause.get("error");this.error_description=i.cause.get("error_description")??undefined;Error.captureStackTrace?.(this,this.constructor)}}class li extends Error{cause;code;response;status;constructor(o,i){super(o,i);this.name=this.constructor.name;this.code=l2;this.cause=i.cause;this.status=i.response.status;this.response=i.response;Object.defineProperty(this,"response",{enumerable:false});Error.captureStackTrace?.(this,this.constructor)}}const ls="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+";const la="[a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2}";const lc='"((?:[^"\\\\]|\\\\.)*)"';const lu="("+ls+")\\s*=\\s*"+lc;const ll="("+ls+")\\s*=\\s*("+ls+")";const ld=new RegExp("^[,\\s]*("+ls+")\\s(.*)");const lf=new RegExp("^[,\\s]*"+lu+"[,\\s]*(.*)");const lp=new RegExp("^[,\\s]*"+ll+"[,\\s]*(.*)");const lh=new RegExp("^("+la+")(?:$|[,\\s])(.*)");function lg(o){if(!c2(o,Response)){throw c6('"response" must be an instance of Response',c3)}const i=o.headers.get("www-authenticate");if(i===null){return undefined}const s=[];let a=i;while(a){let o=a.match(ld);const i=o?.["1"].toLowerCase();a=o?.["2"];if(!i){return undefined}const c={};let u;while(a){let i;let s;if(o=a.match(lf)){;[,i,s,a]=o;if(s.includes("\\")){try{s=JSON.parse(`"${s}"`)}catch{}}c[i.toLowerCase()]=s;continue}if(o=a.match(lp)){;[,i,s,a]=o;c[i.toLowerCase()]=s;continue}if(o=a.match(lh)){if(Object.keys(c).length){break};[,u,a]=o;break}return undefined}const l={scheme:i,parameters:c};if(u){l.token68=u}s.push(l)}if(!s.length){return undefined}return s}async function lm(o,i,s){uB(o);uJ(i);if(!c2(s,Response)){throw c6('"response" must be an instance of Response',c3)}lM(s);await lw(s,201,"Pushed Authorization Request Endpoint");dw(s);const a=await d9(s);ux(a.request_uri,'"response" body "request_uri" property',l9,{body:a});let c=typeof a.expires_in!=="number"?parseFloat(a.expires_in):a.expires_in;uk(c,true,'"response" body "expires_in" property',l9,{body:a});a.expires_in=c;return a}async function ly(o){if(o.status>399&&o.status<500){dw(o);uP(o);try{const i=await o.clone().json();if(um(i)&&typeof i.error==="string"&&i.error.length){return i}}catch{}}return undefined}async function lw(o,i,s){if(o.status!==i){let i;if(i=await ly(o)){await o.body?.cancel();throw new lr("server responded with an error in the response body",{cause:i,response:o})}throw ud(`"response" is not a conform ${s} response (unexpected HTTP status code)`,dt,o)}}function lb(o){if(!lJ.has(o)){throw c6('"options.DPoP" is not a valid DPoPHandle',c5)}}async function l_(o,i,s,a,c,u){ux(o,'"accessToken"');if(!(s instanceof URL)){throw c6('"url" must be an instance of URL',c3)}u4(s,u?.[c4]!==true);a=uy(a);if(u?.DPoP){lb(u.DPoP);await u.DPoP.addProof(s,a,i.toUpperCase(),o)}a.set("authorization",`${a.has("dpop")?"DPoP":"Bearer"} ${o}`);const l=await (u?.[c7]||fetch)(s.href,{body:c,headers:Object.fromEntries(a.entries()),method:i,redirect:"manual",signal:uw(s,u?.signal)});u?.DPoP?.cacheNonce(l);return l}async function lv(o,i,s,a,c,u){const l=await l_(o,i,s,a,c,u);lM(l);return l}async function lE(o,i,s,a){uB(o);uJ(i);const c=u9(o,"userinfo_endpoint",i.use_mtls_endpoint_aliases,a?.[c4]!==true);const u=uy(a?.headers);if(i.userinfo_signed_response_alg){u.set("accept","application/jwt")}else{u.set("accept","application/json");u.append("accept","application/jwt")}return l_(s,"GET",c,u,null,{...a,[c8]:uW(i)})}let lS;function lk(o,i,s,a){lS||=new WeakMap;lS.set(o,{jwks:i,uat:s,get age(){return uq()-this.uat}});if(a){Object.assign(a,{jwks:structuredClone(i),uat:s})}}function lx(o){if(typeof o!=="object"||o===null){return false}if(!("uat"in o)||typeof o.uat!=="number"||uq()-o.uat>=300){return false}if(!("jwks"in o)||!um(o.jwks)||!Array.isArray(o.jwks.keys)||!Array.prototype.every.call(o.jwks.keys,um)){return false}return true}function lR(o,i){lS?.delete(o);delete i?.jwks;delete i?.uat}async function lP(o,i,s){const{alg:a,kid:c}=s;dk(s);if(!lS?.has(o)&&lx(i?.[un])){lk(o,i?.[un].jwks,i?.[un].uat)}let u;let l;if(lS?.has(o)){;({jwks:u,age:l}=lS.get(o));if(l>=300){lR(o,i?.[un]);return lP(o,i,s)}}else{u=await dv(o,i).then(dE);l=0;lk(o,u,uq(),i?.[un])}let d;switch(a.slice(0,2)){case"RS":case"PS":d="RSA";break;case"ES":d="EC";break;case"Ed":d="OKP";break;default:throw new uu("unsupported JWS algorithm",{cause:{alg:a}})}const f=u.keys.filter(o=>{if(o.kty!==d){return false}if(c!==undefined&&c!==o.kid){return false}if(o.alg!==undefined&&a!==o.alg){return false}if(o.use!==undefined&&o.use!=="sig"){return false}if(o.key_ops?.includes("verify")===false){return false}switch(true){case a==="ES256"&&o.crv!=="P-256":case a==="ES384"&&o.crv!=="P-384":case a==="ES512"&&o.crv!=="P-521":case a==="Ed25519"&&o.crv!=="Ed25519":case a==="EdDSA"&&o.crv!=="Ed25519":return false}return true});const{0:p,length:h}=f;if(!h){if(l>=60){lR(o,i?.[un]);return lP(o,i,s)}throw ud("error when selecting a JWT verification key, no applicable keys found",dc,{header:s,candidates:f,jwks_uri:new URL(o.jwks_uri)})}if(h!==1){throw ud('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',dc,{header:s,candidates:f,jwks_uri:new URL(o.jwks_uri)})}return dB(a,p)}const lA=Symbol();function lT(o){return o.headers.get("content-type")?.split(";")[0]}async function lO(o,i,s,a,c){uB(o);uJ(i);if(!c2(a,Response)){throw c6('"response" must be an instance of Response',c3)}lM(a);if(a.status!==200){throw ud('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',dt,a)}dw(a);let u;if(lT(a)==="application/jwt"){const{claims:s,jwt:l}=await dT(await a.text(),dL.bind(undefined,i.userinfo_signed_response_alg,o.userinfo_signing_alg_values_supported,undefined),uW(i),uK(i),c?.[ut]).then(lW.bind(undefined,i.client_id)).then(lq.bind(undefined,o));lD.set(a,l);u=s}else{if(i.userinfo_signed_response_alg){throw ud("JWT UserInfo Response expected",l4,a)}u=await d9(a)}ux(u.sub,'"response" body "sub" property',l9,{body:u});switch(s){case lA:break;default:ux(s,'"expectedSubject"');if(u.sub!==s){throw ud('unexpected "response" body "sub" property value',da,{expected:s,body:u,attribute:"sub"})}}return u}async function lC(o,i,s,a,c,u,l){await s(o,i,c,u);u.set("content-type","application/x-www-form-urlencoded;charset=UTF-8");return(l?.[c7]||fetch)(a.href,{body:c,headers:Object.fromEntries(u.entries()),method:"POST",redirect:"manual",signal:uw(a,l?.signal)})}async function lI(o,i,s,a,c,u){const l=u9(o,"token_endpoint",i.use_mtls_endpoint_aliases,u?.[c4]!==true);c.set("grant_type",a);const d=uy(u?.headers);d.set("accept","application/json");if(u?.DPoP!==undefined){lb(u.DPoP);await u.DPoP.addProof(l,d,"POST")}const f=await lC(o,i,s,l,c,d,u);u?.DPoP?.cacheNonce(f);return f}async function lj(o,i,s,a,c){uB(o);uJ(i);ux(a,'"refreshToken"');const u=new URLSearchParams(c?.additionalParameters);u.set("refresh_token",a);return lI(o,i,s,"refresh_token",u,c)}const lN=new WeakMap;const lD=new WeakMap;function lU(o){if(!o.id_token){return undefined}const i=lN.get(o);if(!i){throw c6('"ref" was already garbage collected or did not resolve from the proper sources',c5)}return i}async function l$(o,i,s){uB(o);if(!lD.has(i)){throw c6('"ref" does not contain a processed JWT Response to verify the signature of',c5)}const{0:a,1:c,2:u}=lD.get(i).split(".");const l=JSON.parse(ui(uc(a)));if(l.alg.startsWith("HS")){throw new uu("unsupported JWS algorithm",{cause:{alg:l.alg}})}let d;d=await lP(o,s,l);await dA(a,c,d,uc(u))}async function lL(o,i,s,a,c){uB(o);uJ(i);if(!c2(s,Response)){throw c6('"response" must be an instance of Response',c3)}lM(s);await lw(s,200,"Token Endpoint");dw(s);const u=await d9(s);ux(u.access_token,'"response" body "access_token" property',l9,{body:u});ux(u.token_type,'"response" body "token_type" property',l9,{body:u});u.token_type=u.token_type.toLowerCase();if(u.token_type!=="dpop"&&u.token_type!=="bearer"){throw new uu("unsupported `token_type` value",{cause:{body:u}})}if(u.expires_in!==undefined){let o=typeof u.expires_in!=="number"?parseFloat(u.expires_in):u.expires_in;uk(o,true,'"response" body "expires_in" property',l9,{body:u});u.expires_in=o}if(u.refresh_token!==undefined){ux(u.refresh_token,'"response" body "refresh_token" property',l9,{body:u})}if(u.scope!==undefined&&typeof u.scope!=="string"){throw ud('"response" body "scope" property must be a string',l9,{body:u})}if(u.id_token!==undefined){ux(u.id_token,'"response" body "id_token" property',l9,{body:u});const l=["aud","exp","iat","iss","sub"];if(i.require_auth_time===true){l.push("auth_time")}if(i.default_max_age!==undefined){uk(i.default_max_age,true,'"client.default_max_age"');l.push("auth_time")}if(a?.length){l.push(...a)}const{claims:d,jwt:f}=await dT(u.id_token,dL.bind(undefined,i.id_token_signed_response_alg,o.id_token_signing_alg_values_supported,"RS256"),uW(i),uK(i),c?.[ut]).then(lX.bind(undefined,l)).then(lB.bind(undefined,o)).then(lK.bind(undefined,i.client_id));if(Array.isArray(d.aud)&&d.aud.length!==1){if(d.azp===undefined){throw ud('ID Token "aud" (audience) claim includes additional untrusted audiences',ds,{claims:d,claim:"aud"})}if(d.azp!==i.client_id){throw ud('unexpected ID Token "azp" (authorized party) claim value',ds,{expected:i.client_id,claims:d,claim:"azp"})}}if(d.auth_time!==undefined){uk(d.auth_time,false,'ID Token "auth_time" (authentication time)',l9,{claims:d})}lD.set(s,f);lN.set(u,d)}return u}function lM(o){let i;if(i=lg(o)){throw new li("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:i,response:o})}}async function lH(o,i,s,a){return lL(o,i,s,undefined,a)}function lW(o,i){if(i.claims.aud!==undefined){return lK(o,i)}return i}function lK(o,i){if(Array.isArray(i.claims.aud)){if(!i.claims.aud.includes(o)){throw ud('unexpected JWT "aud" (audience) claim value',ds,{expected:o,claims:i.claims,claim:"aud"})}}else if(i.claims.aud!==o){throw ud('unexpected JWT "aud" (audience) claim value',ds,{expected:o,claims:i.claims,claim:"aud"})}return i}function lq(o,i){if(i.claims.iss!==undefined){return lB(o,i)}return i}function lB(o,i){const s=o[ft]?.(i)??o.issuer;if(i.claims.iss!==s){throw ud('unexpected JWT "iss" (issuer) claim value',ds,{expected:s,claims:i.claims,claim:"iss"})}return i}const lJ=new WeakSet;function lz(o){lJ.add(o);return o}const lV=Symbol();async function lG(o,i,s,a,c,u,l){uB(o);uJ(i);if(!lJ.has(a)){throw c6('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',c5)}ux(c,'"redirectUri"');const d=dM(a,"code");if(!d){throw ud('no authorization code in "callbackParameters"',l9)}const f=new URLSearchParams(l?.additionalParameters);f.set("redirect_uri",c);f.set("code",d);if(u!==lV){ux(u,'"codeVerifier"');f.set("code_verifier",u)}return lI(o,i,s,"authorization_code",f,l)}const lF={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function lX(o,i){for(const s of o){if(i.claims[s]===undefined){throw ud(`JWT "${s}" (${lF[s]}) claim missing`,l9,{claims:i.claims})}}return i}const lY=Symbol();const lZ=Symbol();async function lQ(o,i,s,a){if(typeof a?.expectedNonce==="string"||typeof a?.maxAge==="number"||a?.requireIdToken){return l0(o,i,s,a.expectedNonce,a.maxAge,{[ut]:a[ut]})}return l1(o,i,s,a)}async function l0(o,i,s,a,c,u){const l=[];switch(a){case undefined:a=lY;break;case lY:break;default:ux(a,'"expectedNonce" argument');l.push("nonce")}c??=i.default_max_age;switch(c){case undefined:c=lZ;break;case lZ:break;default:uk(c,true,'"maxAge" argument');l.push("auth_time")}const d=await lL(o,i,s,l,u);ux(d.id_token,'"response" body "id_token" property',l9,{body:d});const f=lU(d);if(c!==lZ){const o=uq()+uW(i);const s=uK(i);if(f.auth_time+c<o-s){throw ud("too much time has elapsed since the last End-User authentication",di,{claims:f,now:o,tolerance:s,claim:"auth_time"})}}if(a===lY){if(f.nonce!==undefined){throw ud('unexpected ID Token "nonce" claim value',ds,{expected:undefined,claims:f,claim:"nonce"})}}else if(f.nonce!==a){throw ud('unexpected ID Token "nonce" claim value',ds,{expected:a,claims:f,claim:"nonce"})}return d}async function l1(o,i,s,a){const c=await lL(o,i,s,undefined,a);const u=lU(c);if(u){if(i.default_max_age!==undefined){uk(i.default_max_age,true,'"client.default_max_age"');const o=uq()+uW(i);const s=uK(i);if(u.auth_time+i.default_max_age<o-s){throw ud("too much time has elapsed since the last End-User authentication",di,{claims:u,now:o,tolerance:s,claim:"auth_time"})}}if(u.nonce!==undefined){throw ud('unexpected ID Token "nonce" claim value',ds,{expected:undefined,claims:u,claim:"nonce"})}}return c}const l2="OAUTH_WWW_AUTHENTICATE_CHALLENGE";const l5="OAUTH_RESPONSE_BODY_ERROR";const l3="OAUTH_UNSUPPORTED_OPERATION";const l6="OAUTH_AUTHORIZATION_RESPONSE_ERROR";const l4="OAUTH_JWT_USERINFO_EXPECTED";const l8="OAUTH_PARSE_ERROR";const l9="OAUTH_INVALID_RESPONSE";const l7="OAUTH_INVALID_REQUEST";const de="OAUTH_RESPONSE_IS_NOT_JSON";const dt="OAUTH_RESPONSE_IS_NOT_CONFORM";const dn="OAUTH_HTTP_REQUEST_FORBIDDEN";const dr="OAUTH_REQUEST_PROTOCOL_FORBIDDEN";const di="OAUTH_JWT_TIMESTAMP_CHECK_FAILED";const ds="OAUTH_JWT_CLAIM_COMPARISON_FAILED";const da="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED";const dc="OAUTH_KEY_SELECTION_FAILED";const du="OAUTH_MISSING_SERVER_METADATA";const dl="OAUTH_INVALID_SERVER_METADATA";function dd(o,i){if(typeof i.header.typ!=="string"||ug(i.header.typ)!==o){throw ud('unexpected JWT "typ" header parameter value',l9,{header:i.header})}return i}async function df(o,i,s,a,c){uB(o);uJ(i);return lI(o,i,s,"client_credentials",new URLSearchParams(a),c)}async function dp(o,i,s,a,c,u){uB(o);uJ(i);ux(a,'"grantType"');return lI(o,i,s,a,new URLSearchParams(c),u)}async function dh(o,i,s,a){return lL(o,i,s,undefined,a)}async function dg(o,i,s,a){return lL(o,i,s,undefined,a)}async function dm(o,i,s,a,c){uB(o);uJ(i);ux(a,'"token"');const u=u9(o,"revocation_endpoint",i.use_mtls_endpoint_aliases,c?.[c4]!==true);const l=new URLSearchParams(c?.additionalParameters);l.set("token",a);const d=uy(c?.headers);d.delete("accept");return lC(o,i,s,u,l,d,c)}async function dy(o){if(!c2(o,Response)){throw c6('"response" must be an instance of Response',c3)}lM(o);await lw(o,200,"Revocation Endpoint");return undefined}function dw(o){if(o.bodyUsed){throw c6('"response" body has been used already',c5)}}async function db(o,i,s,a,c){uB(o);uJ(i);ux(a,'"token"');const u=u9(o,"introspection_endpoint",i.use_mtls_endpoint_aliases,c?.[c4]!==true);const l=new URLSearchParams(c?.additionalParameters);l.set("token",a);const d=uy(c?.headers);if(c?.requestJwtResponse??i.introspection_signed_response_alg){d.set("accept","application/token-introspection+jwt")}else{d.set("accept","application/json")}return lC(o,i,s,u,l,d,c)}async function d_(o,i,s,a){uB(o);uJ(i);if(!c2(s,Response)){throw c6('"response" must be an instance of Response',c3)}lM(s);await lw(s,200,"Introspection Endpoint");let c;if(lT(s)==="application/token-introspection+jwt"){dw(s);const{claims:u,jwt:l}=await dT(await s.text(),dL.bind(undefined,i.introspection_signed_response_alg,o.introspection_signing_alg_values_supported,"RS256"),uW(i),uK(i),a?.[ut]).then(dd.bind(undefined,"token-introspection+jwt")).then(lX.bind(undefined,["aud","iat","iss"])).then(lB.bind(undefined,o)).then(lK.bind(undefined,i.client_id));lD.set(s,l);if(!um(u.token_introspection)){throw ud('JWT "token_introspection" claim must be a JSON object',l9,{claims:u})}c=u.token_introspection}else{dw(s);c=await d9(s)}if(typeof c.active!=="boolean"){throw ud('"response" body "active" property must be a boolean',l9,{body:c})}return c}async function dv(o,i){uB(o);const s=u9(o,"jwks_uri",false,i?.[c4]!==true);const a=uy(i?.headers);a.set("accept","application/json");a.append("accept","application/jwk-set+json");return(i?.[c7]||fetch)(s.href,{body:undefined,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:uw(s,i?.signal)})}async function dE(o){if(!c2(o,Response)){throw c6('"response" must be an instance of Response',c3)}if(o.status!==200){throw ud('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',dt,o)}dw(o);const i=await d9(o,o=>uT(o,"application/json","application/jwk-set+json"));if(!Array.isArray(i.keys)){throw ud('"response" body "keys" property must be an array',l9,{body:i})}if(!Array.prototype.every.call(i.keys,um)){throw ud('"response" body "keys" property members must be JWK formatted objects',l9,{body:i})}return i}function dS(o){switch(o){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return true;default:return false}}function dk(o){if(!dS(o.alg)){throw new uu('unsupported JWS "alg" identifier',{cause:{alg:o.alg}})}}function dx(o){const{algorithm:i}=o;if(typeof i.modulusLength!=="number"||i.modulusLength<2048){throw new uu(`unsupported ${i.name} modulusLength`,{cause:o})}}function dR(o){const{algorithm:i}=o;switch(i.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new uu("unsupported ECDSA namedCurve",{cause:o})}}function dP(o){switch(o.algorithm.name){case"ECDSA":return{name:o.algorithm.name,hash:dR(o)};case"RSA-PSS":{dx(o);switch(o.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:o.algorithm.name,saltLength:parseInt(o.algorithm.hash.name.slice(-3),10)>>3};default:throw new uu("unsupported RSA-PSS hash name",{cause:o})}}case"RSASSA-PKCS1-v1_5":dx(o);return o.algorithm.name;case"Ed25519":return o.algorithm.name}throw new uu("unsupported CryptoKey algorithm name",{cause:o})}async function dA(o,i,s,a){const c=ui(`${o}.${i}`);const u=dP(s);const l=await crypto.subtle.verify(u,s,a,c);if(!l){throw ud("JWT signature verification failed",l9,{key:s,data:c,signature:a,algorithm:u})}}async function dT(o,i,s,a,c){let{0:u,1:l,length:d}=o.split(".");if(d===5){if(c!==undefined){o=await c(o);({0:u,1:l,length:d}=o.split("."))}else{throw new uu("JWE decryption is not configured",{cause:o})}}if(d!==3){throw ud("Invalid JWT",l9,o)}let f;try{f=JSON.parse(ui(uc(u)))}catch(o){throw ud("failed to parse JWT Header body as base64url encoded JSON",l8,o)}if(!um(f)){throw ud("JWT Header must be a top level object",l9,o)}i(f);if(f.crit!==undefined){throw new uu('no JWT "crit" header parameter extensions are supported',{cause:{header:f}})}let p;try{p=JSON.parse(ui(uc(l)))}catch(o){throw ud("failed to parse JWT Payload body as base64url encoded JSON",l8,o)}if(!um(p)){throw ud("JWT Payload must be a top level object",l9,o)}const h=uq()+s;if(p.exp!==undefined){if(typeof p.exp!=="number"){throw ud('unexpected JWT "exp" (expiration time) claim type',l9,{claims:p})}if(p.exp<=h-a){throw ud('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',di,{claims:p,now:h,tolerance:a,claim:"exp"})}}if(p.iat!==undefined){if(typeof p.iat!=="number"){throw ud('unexpected JWT "iat" (issued at) claim type',l9,{claims:p})}}if(p.iss!==undefined){if(typeof p.iss!=="string"){throw ud('unexpected JWT "iss" (issuer) claim type',l9,{claims:p})}}if(p.nbf!==undefined){if(typeof p.nbf!=="number"){throw ud('unexpected JWT "nbf" (not before) claim type',l9,{claims:p})}if(p.nbf>h+a){throw ud('unexpected JWT "nbf" (not before) claim value',di,{claims:p,now:h,tolerance:a,claim:"nbf"})}}if(p.aud!==undefined){if(typeof p.aud!=="string"&&!Array.isArray(p.aud)){throw ud('unexpected JWT "aud" (audience) claim type',l9,{claims:p})}}return{header:f,claims:p,jwt:o}}async function dO(o,i,s,a,c){uB(o);uJ(i);if(s instanceof URL){s=s.searchParams}if(!(s instanceof URLSearchParams)){throw c6('"parameters" must be an instance of URLSearchParams, or URL',c3)}const u=dM(s,"response");if(!u){throw ud('"parameters" does not contain a JARM response',l9)}const{claims:l,header:d,jwt:f}=await dT(u,dL.bind(undefined,i.authorization_signed_response_alg,o.authorization_signing_alg_values_supported,"RS256"),uW(i),uK(i),c?.[ut]).then(lX.bind(undefined,["aud","exp","iss"])).then(lB.bind(undefined,o)).then(lK.bind(undefined,i.client_id));const{0:p,1:h,2:g}=f.split(".");const m=uc(g);const y=await lP(o,c,d);await dA(p,h,y,m);const w=new URLSearchParams;for(const[o,i]of Object.entries(l)){if(typeof i==="string"&&o!=="aud"){w.set(o,i)}}return dK(o,i,w,a)}async function dC(o,i,s){let a;switch(i.alg){case"RS256":case"PS256":case"ES256":a="SHA-256";break;case"RS384":case"PS384":case"ES384":a="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":a="SHA-512";break;default:throw new uu(`unsupported JWS algorithm for ${s} calculation`,{cause:{alg:i.alg}})}const c=await crypto.subtle.digest(a,ui(o));return uc(c.slice(0,c.byteLength/2))}async function dI(o,i,s,a){const c=await dC(o,s,a);return i===c}async function dj(o,i,s,a,c,u,l){return d$(o,i,s,a,c,u,l,true)}async function dN(o,i,s,a,c,u,l){return d$(o,i,s,a,c,u,l,false)}async function dD(o){if(o.bodyUsed){throw c6("form_post Request instances must contain a readable body",c5,{cause:o})}return o.text()}async function dU(o){if(o.method!=="POST"){throw c6("form_post responses are expected to use the POST method",c5,{cause:o})}if(lT(o)!=="application/x-www-form-urlencoded"){throw c6("form_post responses are expected to use the application/x-www-form-urlencoded content-type",c5,{cause:o})}return dD(o)}async function d$(o,i,s,a,c,u,l,d){uB(o);uJ(i);if(s instanceof URL){if(!s.hash.length){throw c6('"parameters" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters',c5)}s=new URLSearchParams(s.hash.slice(1))}else if(c2(s,Request)){s=new URLSearchParams(await dU(s))}else if(s instanceof URLSearchParams){s=new URLSearchParams(s)}else{throw c6('"parameters" must be an instance of URLSearchParams, URL, or Response',c3)}const f=dM(s,"id_token");s.delete("id_token");switch(c){case undefined:case dW:break;default:ux(c,'"expectedState" argument')}const p=dK({...o,authorization_response_iss_parameter_supported:false},i,s,c);if(!f){throw ud('"parameters" does not contain an ID Token',l9)}const h=dM(s,"code");if(!h){throw ud('"parameters" does not contain an Authorization Code',l9)}const g=["aud","exp","iat","iss","sub","nonce","c_hash"];const m=s.get("state");if(d&&(typeof c==="string"||m!==null)){g.push("s_hash")}if(u!==undefined){uk(u,true,'"maxAge" argument')}else if(i.default_max_age!==undefined){uk(i.default_max_age,true,'"client.default_max_age"')}u??=i.default_max_age??lZ;if(i.require_auth_time||u!==lZ){g.push("auth_time")}const{claims:y,header:w,jwt:b}=await dT(f,dL.bind(undefined,i.id_token_signed_response_alg,o.id_token_signing_alg_values_supported,"RS256"),uW(i),uK(i),l?.[ut]).then(lX.bind(undefined,g)).then(lB.bind(undefined,o)).then(lK.bind(undefined,i.client_id));const _=uW(i);const v=uq()+_;if(y.iat<v-3600){throw ud('unexpected JWT "iat" (issued at) claim value, it is too far in the past',di,{now:v,claims:y,claim:"iat"})}ux(y.c_hash,'ID Token "c_hash" (code hash) claim value',l9,{claims:y});if(y.auth_time!==undefined){uk(y.auth_time,false,'ID Token "auth_time" (authentication time)',l9,{claims:y})}if(u!==lZ){const o=uq()+uW(i);const s=uK(i);if(y.auth_time+u<o-s){throw ud("too much time has elapsed since the last End-User authentication",di,{claims:y,now:o,tolerance:s,claim:"auth_time"})}}ux(a,'"expectedNonce" argument');if(y.nonce!==a){throw ud('unexpected ID Token "nonce" claim value',ds,{expected:a,claims:y,claim:"nonce"})}if(Array.isArray(y.aud)&&y.aud.length!==1){if(y.azp===undefined){throw ud('ID Token "aud" (audience) claim includes additional untrusted audiences',ds,{claims:y,claim:"aud"})}if(y.azp!==i.client_id){throw ud('unexpected ID Token "azp" (authorized party) claim value',ds,{expected:i.client_id,claims:y,claim:"azp"})}}const{0:E,1:S,2:k}=b.split(".");const x=uc(k);const R=await lP(o,l,w);await dA(E,S,R,x);if(await dI(h,y.c_hash,w,"c_hash")!==true){throw ud('invalid ID Token "c_hash" (code hash) claim value',ds,{code:h,alg:w.alg,claim:"c_hash",claims:y})}if(d&&m!==null||y.s_hash!==undefined){ux(y.s_hash,'ID Token "s_hash" (state hash) claim value',l9,{claims:y});ux(m,'"state" response parameter',l9,{parameters:s});if(await dI(m,y.s_hash,w,"s_hash")!==true){throw ud('invalid ID Token "s_hash" (state hash) claim value',ds,{state:m,alg:w.alg,claim:"s_hash",claims:y})}}return p}function dL(o,i,s,a){if(o!==undefined){if(typeof o==="string"?a.alg!==o:!o.includes(a.alg)){throw ud('unexpected JWT "alg" header parameter',l9,{header:a,expected:o,reason:"client configuration"})}return}if(Array.isArray(i)){if(!i.includes(a.alg)){throw ud('unexpected JWT "alg" header parameter',l9,{header:a,expected:i,reason:"authorization server metadata"})}return}if(s!==undefined){if(typeof s==="string"?a.alg!==s:typeof s==="function"?!s(a.alg):!s.includes(a.alg)){throw ud('unexpected JWT "alg" header parameter',l9,{header:a,expected:s,reason:"default value"})}return}throw ud('missing client or server configuration to verify used JWT "alg" header parameter',undefined,{client:o,issuer:i,fallback:s})}function dM(o,i){const{0:s,length:a}=o.getAll(i);if(a>1){throw ud(`"${i}" parameter must be provided only once`,l9)}return s}const dH=Symbol();const dW=Symbol();function dK(o,i,s,a){uB(o);uJ(i);if(s instanceof URL){s=s.searchParams}if(!(s instanceof URLSearchParams)){throw c6('"parameters" must be an instance of URLSearchParams, or URL',c3)}if(dM(s,"response")){throw ud('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',l9,{parameters:s})}const c=dM(s,"iss");const u=dM(s,"state");if(!c&&o.authorization_response_iss_parameter_supported){throw ud('response parameter "iss" (issuer) missing',l9,{parameters:s})}if(c&&c!==o.issuer){throw ud('unexpected "iss" (issuer) response parameter value',l9,{expected:o.issuer,parameters:s})}switch(a){case undefined:case dW:if(u!==undefined){throw ud('unexpected "state" response parameter encountered',l9,{expected:undefined,parameters:s})}break;case dH:break;default:ux(a,'"expectedState" argument');if(u!==a){throw ud(u===undefined?'response parameter "state" missing':'unexpected "state" response parameter value',l9,{expected:a,parameters:s})}}const l=dM(s,"error");if(l){throw new lo("authorization response from the server is an error",{cause:s})}const d=dM(s,"id_token");const f=dM(s,"token");if(d!==undefined||f!==undefined){throw new uu("implicit and hybrid flows are not supported")}return lz(new URLSearchParams(s))}function dq(o){switch(o){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${o.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${o.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${o.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new uu("unsupported JWS algorithm",{cause:{alg:o}})}}async function dB(o,i){const{ext:s,key_ops:a,use:c,...u}=i;return crypto.subtle.importKey("jwk",u,dq(o),true,["verify"])}async function dJ(o,i,s,a,c){uB(o);uJ(i);const u=u9(o,"device_authorization_endpoint",i.use_mtls_endpoint_aliases,c?.[c4]!==true);const l=new URLSearchParams(a);l.set("client_id",i.client_id);const d=uy(c?.headers);d.set("accept","application/json");return lC(o,i,s,u,l,d,c)}async function dz(o,i,s){uB(o);uJ(i);if(!c2(s,Response)){throw c6('"response" must be an instance of Response',c3)}lM(s);await lw(s,200,"Device Authorization Endpoint");dw(s);const a=await d9(s);ux(a.device_code,'"response" body "device_code" property',l9,{body:a});ux(a.user_code,'"response" body "user_code" property',l9,{body:a});ux(a.verification_uri,'"response" body "verification_uri" property',l9,{body:a});let c=typeof a.expires_in!=="number"?parseFloat(a.expires_in):a.expires_in;uk(c,true,'"response" body "expires_in" property',l9,{body:a});a.expires_in=c;if(a.verification_uri_complete!==undefined){ux(a.verification_uri_complete,'"response" body "verification_uri_complete" property',l9,{body:a})}if(a.interval!==undefined){uk(a.interval,false,'"response" body "interval" property',l9,{body:a})}return a}async function dV(o,i,s,a,c){uB(o);uJ(i);ux(a,'"deviceCode"');const u=new URLSearchParams(c?.additionalParameters);u.set("device_code",a);return lI(o,i,s,"urn:ietf:params:oauth:grant-type:device_code",u,c)}async function dG(o,i,s,a){return lL(o,i,s,undefined,a)}async function dF(o,i){ux(o,'"alg"');const s=dq(o);if(o.startsWith("PS")||o.startsWith("RS")){Object.assign(s,{modulusLength:i?.modulusLength??2048,publicExponent:new Uint8Array([1,0,1])})}return crypto.subtle.generateKey(s,i?.extractable??false,["sign","verify"])}function dX(o){const i=new URL(o);i.search="";i.hash="";return i.href}async function dY(o,i,s,a){const c=o.headers.get("dpop");if(c===null){throw ud("operation indicated DPoP use but the request has no DPoP HTTP Header",l7,{headers:o.headers})}if(o.headers.get("authorization")?.toLowerCase().startsWith("dpop ")===false){throw ud(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`,l7,{headers:o.headers})}if(typeof s.cnf?.jkt!=="string"){throw ud("operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim",l7,{claims:s})}const u=uW(a);const l=await dT(c,dL.bind(undefined,a?.signingAlgorithms,undefined,dS),u,uK(a),undefined).then(dd.bind(undefined,"dpop+jwt")).then(lX.bind(undefined,["iat","jti","ath","htm","htu"]));const d=uq()+u;const f=Math.abs(d-l.claims.iat);if(f>300){throw ud("DPoP Proof iat is not recent enough",di,{now:d,claims:l.claims,claim:"iat"})}if(l.claims.htm!==o.method){throw ud("DPoP Proof htm mismatch",ds,{expected:o.method,claims:l.claims,claim:"htm"})}if(typeof l.claims.htu!=="string"||dX(l.claims.htu)!==dX(o.url)){throw ud("DPoP Proof htu mismatch",ds,{expected:dX(o.url),claims:l.claims,claim:"htu"})}{const o=uc(await crypto.subtle.digest("SHA-256",ui(i)));if(l.claims.ath!==o){throw ud("DPoP Proof ath mismatch",ds,{expected:o,claims:l.claims,claim:"ath"})}}{let o;switch(l.header.jwk.kty){case"EC":o={crv:l.header.jwk.crv,kty:l.header.jwk.kty,x:l.header.jwk.x,y:l.header.jwk.y};break;case"OKP":o={crv:l.header.jwk.crv,kty:l.header.jwk.kty,x:l.header.jwk.x};break;case"RSA":o={e:l.header.jwk.e,kty:l.header.jwk.kty,n:l.header.jwk.n};break;default:throw new uu("unsupported JWK key type",{cause:l.header.jwk})}const i=uc(await crypto.subtle.digest("SHA-256",ui(JSON.stringify(o))));if(s.cnf.jkt!==i){throw ud("JWT Access Token confirmation mismatch",ds,{expected:i,claims:s,claim:"cnf.jkt"})}}const{0:p,1:h,2:g}=c.split(".");const m=uc(g);const{jwk:y,alg:w}=l.header;if(!y){throw ud("DPoP Proof is missing the jwk header parameter",l7,{header:l.header})}const b=await dB(w,y);if(b.type!=="public"){throw ud("DPoP Proof jwk header parameter must contain a public key",l7,{header:l.header})}await dA(p,h,b,m)}async function dZ(o,i,s,a){uB(o);if(!c2(i,Request)){throw c6('"request" must be an instance of Request',c3)}ux(s,'"expectedAudience"');const c=i.headers.get("authorization");if(c===null){throw ud('"request" is missing an Authorization HTTP Header',l7,{headers:i.headers})}let{0:u,1:l,length:d}=c.split(" ");u=u.toLowerCase();switch(u){case"dpop":case"bearer":break;default:throw new uu("unsupported Authorization HTTP Header scheme",{cause:{headers:i.headers}})}if(d!==2){throw ud("invalid Authorization HTTP Header format",l7,{headers:i.headers})}const f=["iss","exp","aud","sub","iat","jti","client_id"];if(a?.requireDPoP||u==="dpop"||i.headers.has("dpop")){f.push("cnf")}const{claims:p,header:h}=await dT(l,dL.bind(undefined,a?.signingAlgorithms,undefined,dS),uW(a),uK(a),undefined).then(dd.bind(undefined,"at+jwt")).then(lX.bind(undefined,f)).then(lB.bind(undefined,o)).then(lK.bind(undefined,s)).catch(dQ);for(const o of["client_id","jti","sub"]){if(typeof p[o]!=="string"){throw ud(`unexpected JWT "${o}" claim type`,l7,{claims:p})}}if("cnf"in p){if(!um(p.cnf)){throw ud('unexpected JWT "cnf" (confirmation) claim value',l7,{claims:p})}const{0:o,length:i}=Object.keys(p.cnf);if(i){if(i!==1){throw new uu("multiple confirmation claims are not supported",{cause:{claims:p}})}if(o!=="jkt"){throw new uu("unsupported JWT Confirmation method",{cause:{claims:p}})}}}const{0:g,1:m,2:y}=l.split(".");const w=uc(y);const b=await lP(o,a,h);await dA(g,m,b,w);if(a?.requireDPoP||u==="dpop"||p.cnf?.jkt!==undefined||i.headers.has("dpop")){await dY(i,l,p,a).catch(dQ)}return p}function dQ(o){if(o instanceof ul&&o?.code===l7){o.code=l9}throw o}async function d0(o,i,s,a,c){uB(o);uJ(i);const u=u9(o,"backchannel_authentication_endpoint",i.use_mtls_endpoint_aliases,c?.[c4]!==true);const l=new URLSearchParams(a);l.set("client_id",i.client_id);const d=uy(c?.headers);d.set("accept","application/json");return lC(o,i,s,u,l,d,c)}async function d1(o,i,s){uB(o);uJ(i);if(!c2(s,Response)){throw c6('"response" must be an instance of Response',c3)}lM(s);await lw(s,200,"Backchannel Authentication Endpoint");dw(s);const a=await d9(s);ux(a.auth_req_id,'"response" body "auth_req_id" property',l9,{body:a});let c=typeof a.expires_in!=="number"?parseFloat(a.expires_in):a.expires_in;uk(c,true,'"response" body "expires_in" property',l9,{body:a});a.expires_in=c;if(a.interval!==undefined){uk(a.interval,false,'"response" body "interval" property',l9,{body:a})}return a}async function d2(o,i,s,a,c){uB(o);uJ(i);ux(a,'"authReqId"');const u=new URLSearchParams(c?.additionalParameters);u.set("auth_req_id",a);return lI(o,i,s,"urn:openid:params:grant-type:ciba",u,c)}async function d5(o,i,s,a){return lL(o,i,s,undefined,a)}async function d3(o,i,s){uB(o);const a=u9(o,"registration_endpoint",i.use_mtls_endpoint_aliases,s?.[c4]!==true);const c=uy(s?.headers);c.set("accept","application/json");c.set("content-type","application/json");const u="POST";if(s?.DPoP){lb(s.DPoP);await s.DPoP.addProof(a,c,u,s.initialAccessToken)}if(s?.initialAccessToken){c.set("authorization",`${c.has("dpop")?"DPoP":"Bearer"} ${s.initialAccessToken}`)}const l=await (s?.[c7]||fetch)(a.href,{body:JSON.stringify(i),headers:Object.fromEntries(c.entries()),method:u,redirect:"manual",signal:uw(a,s?.signal)});s?.DPoP?.cacheNonce(l);return l}async function d6(o){if(!c2(o,Response)){throw c6('"response" must be an instance of Response',c3)}lM(o);await lw(o,201,"Dynamic Client Registration Endpoint");dw(o);const i=await d9(o);ux(i.client_id,'"response" body "client_id" property',l9,{body:i});if(i.client_secret!==undefined){ux(i.client_secret,'"response" body "client_secret" property',l9,{body:i})}if(i.client_secret){uk(i.client_secret_expires_at,true,'"response" body "client_secret_expires_at" property',l9,{body:i})}return i}async function d4(o,i){return uE(o,"resourceIdentifier",o=>{u_(o,".well-known/oauth-protected-resource",true);return o},i)}async function d8(o,i){const s=o;if(!(s instanceof URL)&&s!==fe){throw c6('"expectedResourceIdentifier" must be an instance of URL',c3)}if(!c2(i,Response)){throw c6('"response" must be an instance of Response',c3)}if(i.status!==200){throw ud('"response" is not a conform Resource Server Metadata response (unexpected HTTP status code)',dt,i)}dw(i);const a=await d9(i);ux(a.resource,'"response" body "resource" property',l9,{body:a});if(s!==fe&&new URL(a.resource).href!==s.href){throw ud('"response" body "resource" property does not match the expected value',da,{expected:s.href,body:a,attribute:"resource"})}return a}async function d9(o,i=uP){let s;try{s=await o.json()}catch(s){i(o);throw ud('failed to parse "response" body as JSON',l8,s)}if(!um(s)){throw ud('"response" body must be a top level object',l9,{body:s})}return s}const d7=null&&lV;const fe=Symbol();const ft=Symbol();const fn=60*15;async function fr(o,i,s){const{cookies:a,logger:c}=s;const u=a[o];const l=new Date;l.setTime(l.getTime()+fn*1e3);c.debug(`CREATE_${o.toUpperCase()}`,{name:u.name,payload:i,COOKIE_TTL:fn,expires:l});const d=await s$({...s.jwt,maxAge:fn,token:{value:i},salt:u.name});const f={...u.options,expires:l};return{name:u.name,value:d,options:f}}async function fo(o,i,s){try{const{logger:a,cookies:c,jwt:u}=s;a.debug(`PARSE_${o.toUpperCase()}`,{cookie:i});if(!i)throw new rH(`${o} cookie was missing`);const l=await sL({...u,token:i,salt:c[o].name});if(l?.value)return l.value;throw new Error("Invalid cookie")}catch(i){throw new rH(`${o} value could not be parsed`,{cause:i})}}function fi(o,i,s){const{logger:a,cookies:c}=i;const u=c[o];a.debug(`CLEAR_${o.toUpperCase()}`,{cookie:u});s.push({name:u.name,value:"",options:{...c[o].options,maxAge:0}})}function fs(o,i){return async function(s,a,c){const{provider:u,logger:l}=c;if(!u?.checks?.includes(o))return;const d=s?.[c.cookies[i].name];l.debug(`USE_${i.toUpperCase()}`,{value:d});const f=await fo(i,d,c);fi(i,c,a);return f}}const fa={async create(o){const i=uI();const s=await uD(i);const a=await fr("pkceCodeVerifier",i,o);return{cookie:a,value:s}},use:fs("pkce","pkceCodeVerifier")};const fc=60*15;const fu="encodedState";const fl={async create(o,i){const{provider:s}=o;if(!s.checks.includes("state")){if(i){throw new rH("State data was provided but the provider is not configured to use state")}return}const a={origin:i,random:uj()};const c=await s$({secret:o.jwt.secret,token:a,salt:fu,maxAge:fc});const u=await fr("state",c,o);return{cookie:u,value:c}},use:fs("state","state"),async decode(o,i){try{i.logger.debug("DECODE_STATE",{state:o});const s=await sL({secret:i.jwt.secret,token:o,salt:fu});if(s)return s;throw new Error("Invalid state")}catch(o){throw new rH("State could not be decoded",{cause:o})}}};const fd={async create(o){if(!o.provider.checks.includes("nonce"))return;const i=uN();const s=await fr("nonce",i,o);return{cookie:s,value:i}},use:fs("nonce","nonce")};const ff=60*15;const fp="encodedWebauthnChallenge";const fh={async create(o,i,s){return{cookie:await fr("webauthnChallenge",await s$({secret:o.jwt.secret,token:{challenge:i,registerData:s},salt:fp,maxAge:ff}),o)}},async use(o,i,s){const a=i?.[o.cookies.webauthnChallenge.name];const c=await fo("webauthnChallenge",a,o);const u=await sL({secret:o.jwt.secret,token:c,salt:fp});fi("webauthnChallenge",o,s);if(!u)throw new rH("WebAuthn challenge was missing");return u}};function fg(o){if(typeof o!=="string")throw new oH("JWTs must use Compact JWS serialization, JWT must be a string");const{1:i,length:s}=o.split(".");if(s===5)throw new oH("Only JWTs using Compact JWS serialization can be decoded");if(s!==3)throw new oH("Invalid JWT");if(!i)throw new oH("JWTs must contain a payload");let a;try{a=oO(i)}catch{throw new oH("Failed to base64url decode the payload")}let c;try{c=JSON.parse(oE.decode(a))}catch{throw new oH("Failed to parse the decoded payload as JSON")}if(!oZ(c))throw new oH("Invalid JWT Claims Set");return c};function fm(o){return encodeURIComponent(o).replace(/%20/g,"+")}function fy(o,i){const s=fm(o);const a=fm(i);const c=btoa(`${s}:${a}`);return`Basic ${c}`}async function fw(o,i,s){const{logger:a,provider:c}=s;let u;const{token:l,userinfo:d}=c;if((!l?.url||l.url.host==="authjs.dev")&&(!d?.url||d.url.host==="authjs.dev")){const o=new URL(c.issuer);const i=await uS(o,{[c4]:true,[c7]:c[at]});u=await uR(o,i);if(!u.token_endpoint)throw new TypeError("TODO: Authorization server did not provide a token endpoint.");if(!u.userinfo_endpoint)throw new TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}else{u={issuer:c.issuer??"https://authjs.dev",token_endpoint:l?.url.toString(),userinfo_endpoint:d?.url.toString()}}const f={client_id:c.clientId,...c.client};let p;switch(f.token_endpoint_auth_method){case undefined:case"client_secret_basic":p=(o,i,s,a)=>{a.set("authorization",fy(c.clientId,c.clientSecret))};break;case"client_secret_post":p=uV(c.clientSecret);break;case"client_secret_jwt":p=uY(c.clientSecret);break;case"private_key_jwt":p=uX(c.token.clientPrivateKey,{[ue](o,i){i.aud=[u.issuer,u.token_endpoint]}});break;case"none":p=uZ();break;default:throw new Error("unsupported client authentication method")}const h=[];const g=await fl.use(i,h,s);let m;try{m=dK(u,f,new URLSearchParams(o),c.checks.includes("state")?g:dH)}catch(o){if(o instanceof lo){const i={providerId:c.id,...Object.fromEntries(o.cause.entries())};a.debug("OAuthCallbackError",i);throw new rV("OAuth Provider returned an error",i)}throw o}const y=await fa.use(i,h,s);let w=c.callbackUrl;if(!s.isOnRedirectProxy&&c.redirectProxyUrl){w=c.redirectProxyUrl}let b=await lG(u,f,p,m,w,y??"decoy",{[c4]:true,[c7]:(...o)=>{if(!c.checks.includes("pkce")){o[1].body.delete("code_verifier")}return(c[at]??fetch)(...o)}});if(c.token?.conform){b=await c.token.conform(b.clone())??b}let _={};const v=au(c);if(c[an]){switch(c.id){case"microsoft-entra-id":case"azure-ad":{const o=await b.clone().json();if(o.error){const i={providerId:c.id,...o};throw new rV(`OAuth Provider returned an error: ${o.error}`,i)}const{tid:i}=fg(o.id_token);if(typeof i==="string"){const o=/microsoftonline\.com\/(\w+)\/v2\.0/;const s=u.issuer?.match(o)?.[1]??"common";const a=new URL(u.issuer.replace(s,i));const l=await uS(a,{[c7]:c[at]});u=await uR(a,l)}break}default:break}}const E=await lQ(u,f,b,{expectedNonce:await fd.use(i,h,s),requireIdToken:v});const S=E;if(v){const i=lU(E);_=i;if(c[an]&&c.id==="apple"){try{_.user=JSON.parse(o?.user)}catch{}}if(c.idToken===false){const o=await lE(u,f,E.access_token,{[c7]:c[at],[c4]:true});_=await lO(u,f,i.sub,o)}}else{if(d?.request){const o=await d.request({tokens:S,provider:c});if(o instanceof Object)_=o}else if(d?.url){const o=await lE(u,f,E.access_token,{[c7]:c[at],[c4]:true});_=await o.json()}else{throw new TypeError("No userinfo endpoint configured")}}if(S.expires_in){S.expires_at=Math.floor(Date.now()/1e3)+Number(S.expires_in)}const k=await fb(_,c,S,a);return{...k,profile:_,cookies:h}}async function fb(o,i,s,a){try{const a=await i.profile(o,s);const c={...a,id:crypto.randomUUID(),email:a.email?.toLowerCase()};return{user:c,account:{...s,provider:i.id,type:i.type,providerAccountId:a.id??crypto.randomUUID()}}}catch(s){a.debug("getProfile error details",o);a.error(new rG(s,{provider:i.id}))}};var f_=s(356)["Buffer"];function fv(o,i,s){const{user:a,exists:c=false}=s??{};switch(o){case"authenticate":{return"authenticate"}case"register":{if(a&&i===c)return"register";break}case undefined:{if(!i){if(a){if(c){return"authenticate"}else{return"register"}}else{return"authenticate"}}break}}return null}async function fE(o,i,s,a){const c=await fP(o,i,s);const{cookie:u}=await fh.create(o,c.challenge,s);return{status:200,cookies:[...a??[],u],body:{action:"register",options:c},headers:{"Content-Type":"application/json"}}}async function fS(o,i,s,a){const c=await fR(o,i,s);const{cookie:u}=await fh.create(o,c.challenge);return{status:200,cookies:[...a??[],u],body:{action:"authenticate",options:c},headers:{"Content-Type":"application/json"}}}async function fk(o,i,s){const{adapter:a,provider:c}=o;const u=i.body&&typeof i.body.data==="string"?JSON.parse(i.body.data):undefined;if(!u||typeof u!=="object"||!("id"in u)||typeof u.id!=="string"){throw new rO("Invalid WebAuthn Authentication response")}const l=fC(fO(u.id));const d=await a.getAuthenticator(l);if(!d){throw new rO(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:l})}`)}const{challenge:f}=await fh.use(o,i.cookies,s);let p;try{const s=c.getRelayingParty(o,i);p=await c.simpleWebAuthn.verifyAuthenticationResponse({...c.verifyAuthenticationOptions,expectedChallenge:f,response:u,authenticator:fT(d),expectedOrigin:s.origin,expectedRPID:s.id})}catch(o){throw new r7(o)}const{verified:h,authenticationInfo:g}=p;if(!h){throw new r7("WebAuthn authentication response could not be verified")}try{const{newCounter:o}=g;await a.updateAuthenticatorCounter(d.credentialID,o)}catch(o){throw new rI(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:l,oldCounter:d.counter,newCounter:g.newCounter})}`,o)}const m=await a.getAccount(d.providerAccountId,c.id);if(!m){throw new rO(`WebAuthn account not found in database: ${JSON.stringify({credentialID:l,providerAccountId:d.providerAccountId})}`)}const y=await a.getUser(m.userId);if(!y){throw new rO(`WebAuthn user not found in database: ${JSON.stringify({credentialID:l,providerAccountId:d.providerAccountId,userID:m.userId})}`)}return{account:m,user:y}}async function fx(o,i,s){const{provider:a}=o;const c=i.body&&typeof i.body.data==="string"?JSON.parse(i.body.data):undefined;if(!c||typeof c!=="object"||!("id"in c)||typeof c.id!=="string"){throw new rO("Invalid WebAuthn Registration response")}const{challenge:u,registerData:l}=await fh.use(o,i.cookies,s);if(!l){throw new rO("Missing user registration data in WebAuthn challenge cookie")}let d;try{const s=a.getRelayingParty(o,i);d=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:u,response:c,expectedOrigin:s.origin,expectedRPID:s.id})}catch(o){throw new r7(o)}if(!d.verified||!d.registrationInfo){throw new r7("WebAuthn registration response could not be verified")}const f={providerAccountId:fC(d.registrationInfo.credentialID),provider:o.provider.id,type:a.type};const p={providerAccountId:f.providerAccountId,counter:d.registrationInfo.counter,credentialID:fC(d.registrationInfo.credentialID),credentialPublicKey:fC(d.registrationInfo.credentialPublicKey),credentialBackedUp:d.registrationInfo.credentialBackedUp,credentialDeviceType:d.registrationInfo.credentialDeviceType,transports:fI(c.response.transports)};return{user:l,account:f,authenticator:p}}async function fR(o,i,s){const{provider:a,adapter:c}=o;const u=s&&s["id"]?await c.listAuthenticatorsByUserId(s.id):null;const l=a.getRelayingParty(o,i);return await a.simpleWebAuthn.generateAuthenticationOptions({...a.authenticationOptions,rpID:l.id,allowCredentials:u?.map(o=>({id:fO(o.credentialID),type:"public-key",transports:fj(o.transports)}))})}async function fP(o,i,s){const{provider:a,adapter:c}=o;const u=s["id"]?await c.listAuthenticatorsByUserId(s.id):null;const l=s5(32);const d=a.getRelayingParty(o,i);return await a.simpleWebAuthn.generateRegistrationOptions({...a.registrationOptions,userID:l,userName:s.email,userDisplayName:s.name??undefined,rpID:d.id,rpName:d.name,excludeCredentials:u?.map(o=>({id:fO(o.credentialID),type:"public-key",transports:fj(o.transports)}))})}function fA(o){const{provider:i,adapter:s}=o;if(!s)throw new rK("An adapter is required for the WebAuthn provider");if(!i||i.type!=="webauthn"){throw new r1("Provider must be WebAuthn")}return{...o,provider:i,adapter:s}}function fT(o){return{...o,credentialDeviceType:o.credentialDeviceType,transports:fj(o.transports),credentialID:fO(o.credentialID),credentialPublicKey:fO(o.credentialPublicKey)}}function fO(o){return new Uint8Array(f_.from(o,"base64"))}function fC(o){return f_.from(o).toString("base64")}function fI(o){return o?.join(",")}function fj(o){return o?o.split(","):undefined};async function fN(o,i,s,a){if(!i.provider)throw new r1("Callback route called without provider");const{query:c,body:u,method:l,headers:d}=o;const{provider:f,adapter:p,url:h,callbackUrl:g,pages:m,jwt:y,events:w,callbacks:b,session:{strategy:_,maxAge:v},logger:E}=i;const S=_==="jwt";try{if(f.type==="oauth"||f.type==="oidc"){const l=f.authorization?.url.searchParams.get("response_mode")==="form_post"?u:c;if(i.isOnRedirectProxy&&l?.state){const o=await fl.decode(l.state,i);const s=o?.origin&&new URL(o.origin).origin!==i.url.origin;if(s){const i=`${o.origin}?${new URLSearchParams(l)}`;E.debug("Proxy redirecting to",i);return{redirect:i,cookies:a}}}const d=await fw(l,o.cookies,i);if(d.cookies.length){a.push(...d.cookies)}E.debug("authorization result",d);const{user:_,account:k,profile:x}=d;if(!_||!k||!x){return{redirect:`${h}/signin`,cookies:a}}let R;if(p){const{getUserByAccount:o}=p;R=await o({providerAccountId:k.providerAccountId,provider:f.id})}const P=await fD({user:R??_,account:k,profile:x},i);if(P)return{redirect:P,cookies:a};const{user:A,session:T,isNewUser:O}=await c0(s.value,_,k,i);if(S){const o={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()};const c=await b.jwt({token:o,user:A,account:k,profile:x,isNewUser:O,trigger:O?"signUp":"signIn"});if(c===null){a.push(...s.clean())}else{const o=i.cookies.sessionToken.name;const u=await y.encode({...y,token:c,salt:o});const l=new Date;l.setTime(l.getTime()+v*1e3);const d=s.chunk(u,{expires:l});a.push(...d)}}else{a.push({name:i.cookies.sessionToken.name,value:T.sessionToken,options:{...i.cookies.sessionToken.options,expires:T.expires}})}await w.signIn?.({user:A,account:k,profile:x,isNewUser:O});if(O&&m.newUser){return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:g})}`,cookies:a}}return{redirect:g,cookies:a}}else if(f.type==="email"){const o=c?.token;const u=c?.email;if(!o){const i=new TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!o}});i.name="Configuration";throw i}const l=f.secret??i.secret;const d=await p.useVerificationToken({identifier:u,token:await s2(`${o}${l}`)});const h=!!d;const _=h&&d.expires.valueOf()<Date.now();const E=!h||_||u&&d.identifier!==u;if(E)throw new r5({hasInvite:h,expired:_});const{identifier:k}=d;const x=await p.getUserByEmail(k)??{id:crypto.randomUUID(),email:k,emailVerified:null};const R={providerAccountId:x.email,userId:x.id,type:"email",provider:f.id};const P=await fD({user:x,account:R},i);if(P)return{redirect:P,cookies:a};const{user:A,session:T,isNewUser:O}=await c0(s.value,x,R,i);if(S){const o={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()};const c=await b.jwt({token:o,user:A,account:R,isNewUser:O,trigger:O?"signUp":"signIn"});if(c===null){a.push(...s.clean())}else{const o=i.cookies.sessionToken.name;const u=await y.encode({...y,token:c,salt:o});const l=new Date;l.setTime(l.getTime()+v*1e3);const d=s.chunk(u,{expires:l});a.push(...d)}}else{a.push({name:i.cookies.sessionToken.name,value:T.sessionToken,options:{...i.cookies.sessionToken.options,expires:T.expires}})}await w.signIn?.({user:A,account:R,isNewUser:O});if(O&&m.newUser){return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:g})}`,cookies:a}}return{redirect:g,cookies:a}}else if(f.type==="credentials"&&l==="POST"){const o=u??{};Object.entries(c??{}).forEach(([o,i])=>h.searchParams.set(o,i));const p=await f.authorize(o,new Request(h,{headers:d,method:l,body:JSON.stringify(u)}));const m=p;if(!m)throw new rL;else m.id=m.id?.toString()??crypto.randomUUID();const _={providerAccountId:m.id,type:"credentials",provider:f.id};const E=await fD({user:m,account:_,credentials:o},i);if(E)return{redirect:E,cookies:a};const S={name:m.name,email:m.email,picture:m.image,sub:m.id};const k=await b.jwt({token:S,user:m,account:_,isNewUser:false,trigger:"signIn"});if(k===null){a.push(...s.clean())}else{const o=i.cookies.sessionToken.name;const c=await y.encode({...y,token:k,salt:o});const u=new Date;u.setTime(u.getTime()+v*1e3);const l=s.chunk(c,{expires:u});a.push(...l)}await w.signIn?.({user:m,account:_});return{redirect:g,cookies:a}}else if(f.type==="webauthn"&&l==="POST"){const c=o.body?.action;if(typeof c!=="string"||c!=="authenticate"&&c!=="register"){throw new rO("Invalid action parameter")}const u=fA(i);let l;let d;let f;switch(c){case"authenticate":{const i=await fk(u,o,a);l=i.user;d=i.account;break}case"register":{const s=await fx(i,o,a);l=s.user;d=s.account;f=s.authenticator;break}}await fD({user:l,account:d},i);const{user:p,isNewUser:h,session:_,account:E}=await c0(s.value,l,d,i);if(!E){throw new rO("Error creating or finding account")}if(f&&p.id){await u.adapter.createAuthenticator({...f,userId:p.id})}if(S){const o={name:p.name,email:p.email,picture:p.image,sub:p.id?.toString()};const c=await b.jwt({token:o,user:p,account:E,isNewUser:h,trigger:h?"signUp":"signIn"});if(c===null){a.push(...s.clean())}else{const o=i.cookies.sessionToken.name;const u=await y.encode({...y,token:c,salt:o});const l=new Date;l.setTime(l.getTime()+v*1e3);const d=s.chunk(u,{expires:l});a.push(...d)}}else{a.push({name:i.cookies.sessionToken.name,value:_.sessionToken,options:{...i.cookies.sessionToken.options,expires:_.expires}})}await w.signIn?.({user:p,account:E,isNewUser:h});if(h&&m.newUser){return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:g})}`,cookies:a}}return{redirect:g,cookies:a}}throw new r1(`Callback for provider type (${f.type}) is not supported`)}catch(i){if(i instanceof rO)throw i;const o=new rN(i,{provider:f.id});E.debug("callback route error details",{method:l,query:c,body:u});throw o}}async function fD(o,i){let s;const{signIn:a,redirect:c}=i.callbacks;try{s=await a(o)}catch(o){if(o instanceof rO)throw o;throw new rj(o)}if(!s)throw new rj("AccessDenied");if(typeof s!=="string")return;return await c({url:s,baseUrl:i.url.origin})};async function fU(o,i,s,a,c){const{adapter:u,jwt:l,events:d,callbacks:f,logger:p,session:{strategy:h,maxAge:g}}=o;const m={body:null,headers:{"Content-Type":"application/json",...!a&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:s};const y=i.value;if(!y)return m;if(h==="jwt"){try{const s=o.cookies.sessionToken.name;const u=await l.decode({...l,token:y,salt:s});if(!u)throw new Error("Invalid JWT");const p=await f.jwt({token:u,...a&&{trigger:"update"},session:c});const h=cQ(g);if(p!==null){const o={user:{name:p.name,email:p.email,image:p.picture},expires:h.toISOString()};const a=await f.session({session:o,token:p});m.body=a;const c=await l.encode({...l,token:p,salt:s});const u=i.chunk(c,{expires:h});m.cookies?.push(...u);await d.session?.({session:a,token:p})}else{m.cookies?.push(...i.clean())}}catch(o){p.error(new rW(o));m.cookies?.push(...i.clean())}return m}try{const{getSessionAndUser:s,deleteSession:l,updateSession:p}=u;let h=await s(y);if(h&&h.session.expires.valueOf()<Date.now()){await l(y);h=null}if(h){const{user:i,session:s}=h;const u=o.session.updateAge;const l=s.expires.valueOf()-g*1e3+u*1e3;const w=cQ(g);if(l<=Date.now()){await p({sessionToken:y,expires:w})}const b=await f.session({session:{...s,user:i},user:i,newSession:c,...a?{trigger:"update"}:{}});m.body=b;m.cookies?.push({name:o.cookies.sessionToken.name,value:y,options:{...o.cookies.sessionToken.options,expires:w}});await d.session?.({session:b})}else if(y){m.cookies?.push(...i.clean())}}catch(o){p.error(new rF(o))}return m};async function f$(o,i){const{logger:s,provider:a}=i;let c=a.authorization?.url;let u;if(!c||c.host==="authjs.dev"){const o=new URL(a.issuer);const i=await uS(o,{[c7]:a[at],[c4]:true});const s=await uR(o,i).catch(i=>{if(!(i instanceof TypeError)||i.message!=="Invalid URL")throw i;throw new TypeError(`Discovery request responded with an invalid issuer. expected: ${o}`)});if(!s.authorization_endpoint){throw new TypeError("Authorization server did not provide an authorization endpoint.")}c=new URL(s.authorization_endpoint)}const l=c.searchParams;let d=a.callbackUrl;let f;if(!i.isOnRedirectProxy&&a.redirectProxyUrl){d=a.redirectProxyUrl;f=a.callbackUrl;s.debug("using redirect proxy",{redirect_uri:d,data:f})}const p=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:d,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),o);for(const o in p)l.set(o,p[o]);const h=[];if(a.authorization?.url.searchParams.get("response_mode")==="form_post"){i.cookies.state.options.sameSite="none";i.cookies.state.options.secure=true;i.cookies.nonce.options.sameSite="none";i.cookies.nonce.options.secure=true}const g=await fl.create(i,f);if(g){l.set("state",g.value);h.push(g.cookie)}if(a.checks?.includes("pkce")){if(u&&!u.code_challenge_methods_supported?.includes("S256")){if(a.type==="oidc")a.checks=["nonce"]}else{const{value:o,cookie:s}=await fa.create(i);l.set("code_challenge",o);l.set("code_challenge_method","S256");h.push(s)}}const m=await fd.create(i);if(m){l.set("nonce",m.value);h.push(m.cookie)}if(a.type==="oidc"&&!c.searchParams.has("scope")){c.searchParams.set("scope","openid profile email")}s.debug("authorization url is ready",{url:c,cookies:h,provider:a});return{redirect:c.toString(),cookies:h}};async function fL(o,i){const{body:s}=o;const{provider:a,callbacks:c,adapter:u}=i;const l=a.normalizeIdentifier??fM;const d=l(s?.email);const f={id:crypto.randomUUID(),email:d,emailVerified:null};const p=await u.getUserByEmail(d)??f;const h={providerAccountId:d,userId:p.id,type:"email",provider:a.id};let g;try{g=await c.signIn({user:p,account:h,email:{verificationRequest:true}})}catch(o){throw new rj(o)}if(!g)throw new rj("AccessDenied");if(typeof g==="string"){return{redirect:await c.redirect({url:g,baseUrl:i.url.origin})}}const{callbackUrl:m,theme:y}=i;const w=await a.generateVerificationToken?.()??s5(32);const b=86400;const _=new Date(Date.now()+(a.maxAge??b)*1e3);const v=a.secret??i.secret;const E=new URL(i.basePath,i.url.origin);const S=a.sendVerificationRequest({identifier:d,token:w,expires:_,url:`${E}/callback/${a.id}?${new URLSearchParams({callbackUrl:m,token:w,email:d})}`,provider:a,theme:y,request:s0(o)});const k=u.createVerificationToken?.({identifier:d,token:await s2(`${w}${v}`),expires:_});await Promise.all([S,k]);return{redirect:`${E}/verify-request?${new URLSearchParams({provider:a.id,type:a.type})}`}}function fM(o){if(!o)throw new Error("Missing email from request body.");let[i,s]=o.toLowerCase().trim().split("@");s=s.split(",")[0];return`${i}@${s}`};async function fH(o,i,s){const a=`${s.url.origin}${s.basePath}/signin`;if(!s.provider)return{redirect:a,cookies:i};switch(s.provider.type){case"oauth":case"oidc":{const{redirect:a,cookies:c}=await f$(o.query,s);if(c)i.push(...c);return{redirect:a,cookies:i}}case"email":{const a=await fL(o,s);return{...a,cookies:i}}default:return{redirect:a,cookies:i}}};async function fW(o,i,s){const{jwt:a,events:c,callbackUrl:u,logger:l,session:d}=s;const f=i.value;if(!f)return{redirect:u,cookies:o};try{if(d.strategy==="jwt"){const o=s.cookies.sessionToken.name;const i=await a.decode({...a,token:f,salt:o});await c.signOut?.({token:i})}else{const o=await s.adapter?.deleteSession(f);await c.signOut?.({session:o})}}catch(o){l.error(new rZ(o))}o.push(...i.clean());return{redirect:u,cookies:o}};async function fK(o,i){const{adapter:s,jwt:a,session:{strategy:c}}=o;const u=i.value;if(!u)return null;if(c==="jwt"){const i=o.cookies.sessionToken.name;const s=await a.decode({...a,token:u,salt:i});if(s&&s.sub){return{id:s.sub,name:s.name,email:s.email,image:s.picture}}}else{const o=await s?.getSessionAndUser(u);if(o){return o.user}}return null};async function fq(o,i,s,a){const c=fA(i);const{provider:u}=c;const{action:l}=o.query??{};if(l!=="register"&&l!=="authenticate"&&typeof l!=="undefined"){return{status:400,body:{error:"Invalid action"},cookies:a,headers:{"Content-Type":"application/json"}}}const d=await fK(i,s);const f=d?{user:d,exists:true}:await u.getUserInfo(i,o);const p=f?.user;const h=fv(l,!!d,f);switch(h){case"authenticate":return fS(c,o,p,a);case"register":if(typeof p?.email==="string"){return fE(c,o,p,a)}break;default:return{status:400,body:{error:"Invalid request"},cookies:a,headers:{"Content-Type":"application/json"}}}};;async function fB(o,i){const{action:s,providerId:a,error:c,method:u}=o;const l=i.skipCSRFCheck===s7;const{options:d,cookies:f}=await ap({authOptions:i,action:s,providerId:a,url:o.url,callbackUrl:o.body?.callbackUrl??o.query?.callbackUrl,csrfToken:o.body?.csrfToken,cookies:o.cookies,isPost:u==="POST",csrfDisabled:l});const p=new rT(d.cookies.sessionToken,o.cookies,d.logger);if(u==="GET"){const i=cZ({...d,query:o.query,cookies:f});switch(s){case"callback":return await fN(o,d,p,f);case"csrf":return i.csrf(l,d,f);case"error":return i.error(c);case"providers":return i.providers(d.providers);case"session":return await fU(d,p,f);case"signin":return i.signin(a,c);case"signout":return i.signout();case"verify-request":return i.verifyRequest();case"webauthn-options":return await fq(o,d,p,f);default:}}else{const{csrfTokenVerified:i}=d;switch(s){case"callback":if(d.provider.type==="credentials")s4(s,i);return await fN(o,d,p,f);case"session":s4(s,i);return await fU(d,p,f,true,o.body?.data);case"signin":s4(s,i);return await fH(o,f,d);case"signout":s4(s,i);return await fW(f,p,d);default:}}throw new rQ(`Cannot handle action: ${s}`)};function fJ(o,i,s=false){try{const a=o.AUTH_URL;if(a){if(i.basePath){if(!s){const o=sV(i);o.warn("env-url-basepath-redundant")}}else{i.basePath=new URL(a).pathname}}}catch{}finally{i.basePath??(i.basePath=`/auth`)}if(!i.secret?.length){i.secret=[];const s=o.AUTH_SECRET;if(s)i.secret.push(s);for(const s of[1,2,3]){const a=o[`AUTH_SECRET_${s}`];if(a)i.secret.unshift(a)}}i.redirectProxyUrl??(i.redirectProxyUrl=o.AUTH_REDIRECT_PROXY_URL);i.trustHost??(i.trustHost=!!(o.AUTH_URL??o.AUTH_TRUST_HOST??o.VERCEL??o.CF_PAGES??o.NODE_ENV!=="production"));i.providers=i.providers.map(i=>{const{id:s}=typeof i==="function"?i({}):i;const a=s.toUpperCase().replace(/-/g,"_");const c=o[`AUTH_${a}_ID`];const u=o[`AUTH_${a}_SECRET`];const l=o[`AUTH_${a}_ISSUER`];const d=o[`AUTH_${a}_KEY`];const f=typeof i==="function"?i({clientId:c,clientSecret:u,issuer:l,apiKey:d}):i;if(f.type==="oauth"||f.type==="oidc"){f.clientId??(f.clientId=c);f.clientSecret??(f.clientSecret=u);f.issuer??(f.issuer=l)}else if(f.type==="email"){f.apiKey??(f.apiKey=d)}return f})}function fz(o,i,s,a,c){const u=c?.basePath;const l=a.AUTH_URL??a.NEXTAUTH_URL;let d;if(l){d=new URL(l);if(u&&u!=="/"&&d.pathname!=="/"){if(d.pathname!==u){const o=sV(c);o.warn("env-url-basepath-mismatch")}d.pathname="/"}}else{const o=s.get("x-forwarded-host")??s.get("host");const a=s.get("x-forwarded-proto")??i??"https";const c=a.endsWith(":")?a:a+":";d=new URL(`${c}//${o}`)}const f=d.toString().replace(/\/$/,"");if(u){const i=u?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${f}/${i}/${o}`)}return new URL(`${f}/${o}`)};async function fV(o,i){const s=sV(i);const a=await sQ(o,i);if(!a)return Response.json(`Bad request.`,{status:400});const c=od(a,i);if(Array.isArray(c)){c.forEach(s.warn)}else if(c){s.error(c);const o=new Set(["signin","signout","error","verify-request"]);if(!o.has(a.action)||a.method!=="GET"){const o="There was a problem with the server configuration. Check the server logs for more information.";return Response.json({message:o},{status:500})}const{pages:u,theme:l}=i;const d=u?.error&&a.url.searchParams.get("callbackUrl")?.startsWith(u.error);if(!u?.error||d){if(d){s.error(new rD(`The error page ${u?.error} should not require authentication`))}const o=cZ({theme:l}).error("Configuration");return s1(o)}const f=`${a.url.origin}${u.error}?error=Configuration`;return Response.redirect(f)}const u=o.headers?.has("X-Auth-Return-Redirect");const l=i.raw===ae;try{const o=await fB(a,i);if(l)return o;const s=s1(o);const c=s.headers.get("Location");if(!u||!c)return s;return Response.json({url:c},{headers:s.headers})}catch(w){const c=w;s.error(c);const d=c instanceof rO;if(d&&l&&!u)throw c;if(o.method==="POST"&&a.action==="session")return Response.json(null,{status:400});const f=r4(c);const p=f?c.type:"Configuration";const h=new URLSearchParams({error:p});if(c instanceof rL)h.set("code",c.code);const g=d&&c.kind||"error";const m=i.pages?.[g]??`${i.basePath}/${g.toLowerCase()}`;const y=`${a.url.origin}${m}?${h}`;if(u)return Response.json({url:y});return Response.redirect(y)}}var fG=s(280);function fF(o){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(o)}function fX(o){return{...parseua(o),isBot:o===undefined?false:fF(o)}}function fY({headers:o}){return fX(o.get("user-agent")||undefined)};const fZ=typeof URLPattern==="undefined"?undefined:URLPattern;function fQ(o){const i=workAsyncStorage.getStore();if(!i){throw Object.defineProperty(new Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:false,configurable:true})}const{afterContext:s}=i;return s.after(o)};var f0=s(557);var f1=s(602);var f2=s(801);function f5(o,i){throw Object.defineProperty(new StaticGenBailoutError(`Route ${o} couldn't be rendered statically because it used ${i}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:false,configurable:true})}function f3(o,i){throw Object.defineProperty(new StaticGenBailoutError(`Route ${o} with \`dynamic = "error"\` couldn't be rendered statically because it used ${i}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:false,configurable:true})}function f6(o){const i=Object.defineProperty(new Error(`Route ${o.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:false,configurable:true});o.invalidUsageError??=i;throw i}function f4(){const o=n5.getStore();return(o==null?void 0:o.rootTaskSpawnPhase)==="action"};function f8(){const o=workAsyncStorage.getStore();const i=workUnitAsyncStorage.getStore();if(o){if(i&&i.phase==="after"&&!isRequestAPICallableInsideAfter()){throw Object.defineProperty(new Error(`Route ${o.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:false,configurable:true})}if(o.forceStatic){return Promise.resolve(undefined)}if(i){if(i.type==="cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:false,configurable:true})}else if(i.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:false,configurable:true})}}if(o.dynamicShouldError){throw Object.defineProperty(new StaticGenBailoutError(`Route ${o.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:false,configurable:true})}if(i){if(i.type==="prerender"){return makeHangingPromise(i.renderSignal,"`connection()`")}else if(i.type==="prerender-ppr"){postponeWithTracking(o.route,"connection",i.dynamicTracking)}else if(i.type==="prerender-legacy"){throwToInterruptStaticGeneration("connection",o,i)}}trackDynamicDataInDynamicRender(o,i)}return Promise.resolve(undefined)};const f9=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function f7(o,i){if(f9.test(i)){return"`"+o+"."+i+"`"}return"`"+o+"["+JSON.stringify(i)+"]`"}function pe(o,i){const s=JSON.stringify(i);return"`Reflect.has("+o+", "+s+")`, `"+s+" in "+o+"`, or similar"}const pt=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"]);const pn=new WeakMap;async function pr(){const o=workAsyncStorage.getStore();if(!o){throw Object.defineProperty(new InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:false,configurable:true})}const i=workUnitAsyncStorage.getStore();if(!i){throw Object.defineProperty(new Error(`Route ${o.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:false,configurable:true})}switch(i.type){case"unstable-cache":case"cache":{throw Object.defineProperty(new Error(`Route ${o.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:false,configurable:true})}case"prerender":case"prerender-ppr":case"prerender-legacy":return po(i.rootParams,o,i);default:return Promise.resolve(i.rootParams)}}function po(o,i,s){const a=i.fallbackRouteParams;if(a){let c=false;for(const i in o){if(a.has(i)){c=true;break}}if(c){if(s.type==="prerender"){const i=pn.get(o);if(i){return i}const a=makeHangingPromise(s.renderSignal,"`unstable_rootParams`");pn.set(o,a);return a}return pi(o,a,i,s)}}return Promise.resolve(o)}function pi(o,i,s,a){const c=pn.get(o);if(c){return c}const u={...o};const l=Promise.resolve(u);pn.set(o,l);Object.keys(o).forEach(c=>{if(wellKnownProperties.has(c)){}else{if(i.has(c)){Object.defineProperty(u,c,{get(){const o=describeStringPropertyAccess("unstable_rootParams",c);if(a.type==="prerender-ppr"){postponeWithTracking(s.route,o,a.dynamicTracking)}else{throwToInterruptStaticGeneration(o,s,a)}},enumerable:true})}else{;l[c]=o[c]}}});return l};;;function ps(o){const i=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!i)return o;const{origin:s}=new URL(i);const{href:a,origin:c}=o.nextUrl;return new eX(a.replace(c,s),o)}function pa(o){try{o.secret??(o.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);const i=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!i)return;const{pathname:s}=new URL(i);if(s==="/")return;o.basePath||(o.basePath=s)}catch{}finally{o.basePath||(o.basePath="/api/auth");fJ(process.env,o,true)}}var pc=s(815);const pu={current:null};const pl=typeof pc.cache==="function"?pc.cache:o=>o;const pd=false?0:console.warn;const pf=pl(o=>{try{pd(pu.current)}finally{pu.current=null}});function pp(o){return function i(...s){const a=o(...s);if(false){var c}else{pd(a)}}};function ph(){const o="cookies";const i=tS.J.getStore();const s=tk.FP.getStore();if(i){if(s&&s.phase==="after"&&!f4()){throw Object.defineProperty(new Error(`Route ${i.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:false,configurable:true})}if(i.forceStatic){const o=pg();return pw(o)}if(s){if(s.type==="cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:false,configurable:true})}else if(s.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:false,configurable:true})}}if(i.dynamicShouldError){throw Object.defineProperty(new f1.f(`Route ${i.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:false,configurable:true})}if(s){if(s.type==="prerender"){return py(i.route,s)}else if(s.type==="prerender-ppr"){(0,f0.Ui)(i.route,o,s.dynamicTracking)}else if(s.type==="prerender-legacy"){(0,f0.xI)(o,i,s)}}(0,f0.Pk)(i,s)}const a=(0,tk.XN)(o);let c;if(tI(a)){c=a.userspaceMutableCookies}else{c=a.cookies}if(false){}else{return pw(c)}}function pg(){return tR.seal(new eG.RequestCookies(new Headers({})))}const pm=new WeakMap;function py(o,i){const s=pm.get(i);if(s){return s}const a=(0,f2.W)(i.renderSignal,"`cookies()`");pm.set(i,a);Object.defineProperties(a,{[Symbol.iterator]:{value:function(){const s="`cookies()[Symbol.iterator]()`";const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},size:{get(){const s="`cookies().size`";const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},get:{value:function s(){let s;if(arguments.length===0){s="`cookies().get()`"}else{s=`\`cookies().get(${p_(arguments[0])})\``}const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},getAll:{value:function s(){let s;if(arguments.length===0){s="`cookies().getAll()`"}else{s=`\`cookies().getAll(${p_(arguments[0])})\``}const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},has:{value:function s(){let s;if(arguments.length===0){s="`cookies().has()`"}else{s=`\`cookies().has(${p_(arguments[0])})\``}const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},set:{value:function s(){let s;if(arguments.length===0){s="`cookies().set()`"}else{const o=arguments[0];if(o){s=`\`cookies().set(${p_(o)}, ...)\``}else{s="`cookies().set(...)`"}}const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},delete:{value:function(){let s;if(arguments.length===0){s="`cookies().delete()`"}else if(arguments.length===1){s=`\`cookies().delete(${p_(arguments[0])})\``}else{s=`\`cookies().delete(${p_(arguments[0])}, ...)\``}const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},clear:{value:function s(){const s="`cookies().clear()`";const a=pS(o,s);(0,f0.t3)(o,s,a,i)}},toString:{value:function s(){const s="`cookies().toString()`";const a=pS(o,s);(0,f0.t3)(o,s,a,i)}}});return a}function pw(o){const i=pm.get(o);if(i){return i}const s=Promise.resolve(o);pm.set(o,s);Object.defineProperties(s,{[Symbol.iterator]:{value:o[Symbol.iterator]?o[Symbol.iterator].bind(o):pk.bind(o)},size:{get(){return o.size}},get:{value:o.get.bind(o)},getAll:{value:o.getAll.bind(o)},has:{value:o.has.bind(o)},set:{value:o.set.bind(o)},delete:{value:o.delete.bind(o)},clear:{value:typeof o.clear==="function"?o.clear.bind(o):px.bind(o,s)},toString:{value:o.toString.bind(o)}});return s}function pb(o,i){const s=pm.get(o);if(s){return s}const a=new Promise(i=>scheduleImmediate(()=>i(o)));pm.set(o,a);Object.defineProperties(a,{[Symbol.iterator]:{value:function(){const s="`...cookies()` or similar iteration";pv(i,s);return o[Symbol.iterator]?o[Symbol.iterator].apply(o,arguments):pk.call(o)},writable:false},size:{get(){const s="`cookies().size`";pv(i,s);return o.size}},get:{value:function s(){let s;if(arguments.length===0){s="`cookies().get()`"}else{s=`\`cookies().get(${p_(arguments[0])})\``}pv(i,s);return o.get.apply(o,arguments)},writable:false},getAll:{value:function s(){let s;if(arguments.length===0){s="`cookies().getAll()`"}else{s=`\`cookies().getAll(${p_(arguments[0])})\``}pv(i,s);return o.getAll.apply(o,arguments)},writable:false},has:{value:function s(){let s;if(arguments.length===0){s="`cookies().has()`"}else{s=`\`cookies().has(${p_(arguments[0])})\``}pv(i,s);return o.has.apply(o,arguments)},writable:false},set:{value:function s(){let s;if(arguments.length===0){s="`cookies().set()`"}else{const o=arguments[0];if(o){s=`\`cookies().set(${p_(o)}, ...)\``}else{s="`cookies().set(...)`"}}pv(i,s);return o.set.apply(o,arguments)},writable:false},delete:{value:function(){let s;if(arguments.length===0){s="`cookies().delete()`"}else if(arguments.length===1){s=`\`cookies().delete(${p_(arguments[0])})\``}else{s=`\`cookies().delete(${p_(arguments[0])}, ...)\``}pv(i,s);return o.delete.apply(o,arguments)},writable:false},clear:{value:function s(){const s="`cookies().clear()`";pv(i,s);return typeof o.clear==="function"?o.clear.apply(o,arguments):px.call(o,a)},writable:false},toString:{value:function s(){const s="`cookies().toString()` or implicit casting";pv(i,s);return o.toString.apply(o,arguments)},writable:false}});return a}function p_(o){return typeof o==="object"&&o!==null&&typeof o.name==="string"?`'${o.name}'`:typeof o==="string"?`'${o}'`:"..."}function pv(o,i){const s=workUnitAsyncStorage.getStore();if(s&&s.type==="request"&&s.prerenderPhase===true){const o=s;trackSynchronousRequestDataAccessInDev(o)}pE(o,i)}const pE=pp(pS);function pS(o,i){const s=o?`Route "${o}" `:"This route ";return Object.defineProperty(new Error(`${s}used ${i}. `+`\`cookies()\` should be awaited before using its value. `+`Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:false,configurable:true})}function pk(){return this.getAll().map(o=>[o.name,o]).values()}function px(o){for(const o of this.getAll()){this.delete(o.name)}return o};function pR(){const o=tS.J.getStore();const i=tk.FP.getStore();if(o){if(i&&i.phase==="after"&&!f4()){throw Object.defineProperty(new Error(`Route ${o.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:false,configurable:true})}if(o.forceStatic){const o=tE.seal(new Headers({}));return pT(o)}if(i){if(i.type==="cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:false,configurable:true})}else if(i.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${o.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:false,configurable:true})}}if(o.dynamicShouldError){throw Object.defineProperty(new f1.f(`Route ${o.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:false,configurable:true})}if(i){if(i.type==="prerender"){return pA(o.route,i)}else if(i.type==="prerender-ppr"){(0,f0.Ui)(o.route,"headers",i.dynamicTracking)}else if(i.type==="prerender-legacy"){(0,f0.xI)("headers",o,i)}}(0,f0.Pk)(o,i)}const s=(0,tk.XN)("headers");if(false){}else{return pT(s.headers)}}const pP=new WeakMap;function pA(o,i){const s=pP.get(i);if(s){return s}const a=(0,f2.W)(i.renderSignal,"`headers()`");pP.set(i,a);Object.defineProperties(a,{append:{value:function s(){const s=`\`headers().append(${pC(arguments[0])}, ...)\``;const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},delete:{value:function s(){const s=`\`headers().delete(${pC(arguments[0])})\``;const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},get:{value:function s(){const s=`\`headers().get(${pC(arguments[0])})\``;const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},has:{value:function s(){const s=`\`headers().has(${pC(arguments[0])})\``;const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},set:{value:function s(){const s=`\`headers().set(${pC(arguments[0])}, ...)\``;const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},getSetCookie:{value:function s(){const s="`headers().getSetCookie()`";const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},forEach:{value:function s(){const s="`headers().forEach(...)`";const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},keys:{value:function s(){const s="`headers().keys()`";const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},values:{value:function s(){const s="`headers().values()`";const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},entries:{value:function s(){const s="`headers().entries()`";const a=pN(o,s);(0,f0.t3)(o,s,a,i)}},[Symbol.iterator]:{value:function(){const s="`headers()[Symbol.iterator]()`";const a=pN(o,s);(0,f0.t3)(o,s,a,i)}}});return a}function pT(o){const i=pP.get(o);if(i){return i}const s=Promise.resolve(o);pP.set(o,s);Object.defineProperties(s,{append:{value:o.append.bind(o)},delete:{value:o.delete.bind(o)},get:{value:o.get.bind(o)},has:{value:o.has.bind(o)},set:{value:o.set.bind(o)},getSetCookie:{value:o.getSetCookie.bind(o)},forEach:{value:o.forEach.bind(o)},keys:{value:o.keys.bind(o)},values:{value:o.values.bind(o)},entries:{value:o.entries.bind(o)},[Symbol.iterator]:{value:o[Symbol.iterator].bind(o)}});return s}function pO(o,i){const s=pP.get(o);if(s){return s}const a=new Promise(i=>scheduleImmediate(()=>i(o)));pP.set(o,a);Object.defineProperties(a,{append:{value:function s(){const s=`\`headers().append(${pC(arguments[0])}, ...)\``;pI(i,s);return o.append.apply(o,arguments)}},delete:{value:function s(){const s=`\`headers().delete(${pC(arguments[0])})\``;pI(i,s);return o.delete.apply(o,arguments)}},get:{value:function s(){const s=`\`headers().get(${pC(arguments[0])})\``;pI(i,s);return o.get.apply(o,arguments)}},has:{value:function s(){const s=`\`headers().has(${pC(arguments[0])})\``;pI(i,s);return o.has.apply(o,arguments)}},set:{value:function s(){const s=`\`headers().set(${pC(arguments[0])}, ...)\``;pI(i,s);return o.set.apply(o,arguments)}},getSetCookie:{value:function s(){const s="`headers().getSetCookie()`";pI(i,s);return o.getSetCookie.apply(o,arguments)}},forEach:{value:function s(){const s="`headers().forEach(...)`";pI(i,s);return o.forEach.apply(o,arguments)}},keys:{value:function s(){const s="`headers().keys()`";pI(i,s);return o.keys.apply(o,arguments)}},values:{value:function s(){const s="`headers().values()`";pI(i,s);return o.values.apply(o,arguments)}},entries:{value:function s(){const s="`headers().entries()`";pI(i,s);return o.entries.apply(o,arguments)}},[Symbol.iterator]:{value:function(){const s="`...headers()` or similar iteration";pI(i,s);return o[Symbol.iterator].apply(o,arguments)}}});return a}function pC(o){return typeof o==="string"?`'${o}'`:"..."}function pI(o,i){const s=workUnitAsyncStorage.getStore();if(s&&s.type==="request"&&s.prerenderPhase===true){const o=s;trackSynchronousRequestDataAccessInDev(o)}pj(o,i)}const pj=pp(pN);function pN(o,i){const s=o?`Route "${o}" `:"This route ";return Object.defineProperty(new Error(`${s}used ${i}. `+`\`headers()\` should be awaited before using its value. `+`Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:false,configurable:true})}var pD=s(16);function pU(){const o="draftMode";const i=workAsyncStorage.getStore();const s=workUnitAsyncStorage.getStore();if(!i||!s){throwForMissingRequestStore(o)}switch(s.type){case"request":return p$(s.draftMode,i);case"cache":case"unstable-cache":const a=getDraftModeProviderForCacheScope(i,s);if(a){return p$(a,i)}case"prerender":case"prerender-ppr":case"prerender-legacy":if(false){}else{return pM(null)}default:const c=s;return c}}function p$(o,i){const s=pL.get(pU);if(s){return s}let a;if(false){}else{a=pM(o)}pL.set(o,a);return a}const pL=new WeakMap;function pM(o){const i=new pW(o);const s=Promise.resolve(i);Object.defineProperty(s,"isEnabled",{get(){return i.isEnabled},set(o){Object.defineProperty(s,"isEnabled",{value:o,writable:true,enumerable:true})},enumerable:true,configurable:true});s.enable=i.enable.bind(i);s.disable=i.disable.bind(i);return s}function pH(o,i){const s=new pW(o);const a=Promise.resolve(s);Object.defineProperty(a,"isEnabled",{get(){const o="`draftMode().isEnabled`";pK(i,o);return s.isEnabled},set(o){Object.defineProperty(a,"isEnabled",{value:o,writable:true,enumerable:true})},enumerable:true,configurable:true});Object.defineProperty(a,"enable",{value:function o(){const o="`draftMode().enable()`";pK(i,o);return s.enable.apply(s,arguments)}});Object.defineProperty(a,"disable",{value:function o(){const o="`draftMode().disable()`";pK(i,o);return s.disable.apply(s,arguments)}});return a}class pW{constructor(o){this._provider=o}get isEnabled(){if(this._provider!==null){return this._provider.isEnabled}return false}enable(){pJ("draftMode().enable()");if(this._provider!==null){this._provider.enable()}}disable(){pJ("draftMode().disable()");if(this._provider!==null){this._provider.disable()}}}function pK(o,i){const s=workUnitAsyncStorage.getStore();if(s&&s.type==="request"&&s.prerenderPhase===true){const o=s;trackSynchronousRequestDataAccessInDev(o)}pq(o,i)}const pq=pp(pB);function pB(o,i){const s=o?`Route "${o}" `:"This route ";return Object.defineProperty(new Error(`${s}used ${i}. `+`\`draftMode()\` should be awaited before using its value. `+`Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:false,configurable:true})}function pJ(o){const i=workAsyncStorage.getStore();const s=workUnitAsyncStorage.getStore();if(i){if(s){if(s.type==="cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "${o}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:false,configurable:true})}else if(s.type==="unstable-cache"){throw Object.defineProperty(new Error(`Route ${i.route} used "${o}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:false,configurable:true})}else if(s.phase==="after"){throw Object.defineProperty(new Error(`Route ${i.route} used "${o}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:false,configurable:true})}}if(i.dynamicShouldError){throw Object.defineProperty(new StaticGenBailoutError(`Route ${i.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${o}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:false,configurable:true})}if(s){if(s.type==="prerender"){const a=Object.defineProperty(new Error(`Route ${i.route} used ${o} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:false,configurable:true});abortAndThrowOnSynchronousRequestDataAccess(i.route,o,a,s)}else if(s.type==="prerender-ppr"){postponeWithTracking(i.route,o,s.dynamicTracking)}else if(s.type==="prerender-legacy"){s.revalidate=0;const a=Object.defineProperty(new DynamicServerError(`Route ${i.route} couldn't be rendered statically because it used \`${o}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:false,configurable:true});i.dynamicUsageDescription=o;i.dynamicUsageStack=a.stack;throw a}else if(false){}}}};;async function pz(o,i){const s=fz("session",o.get("x-forwarded-proto"),o,process.env,i);const a=new Request(s,{headers:{cookie:o.get("cookie")??""}});return fV(a,{...i,callbacks:{...i.callbacks,async session(...o){const s=await i.callbacks?.session?.(...o)??{...o[0].session,expires:o[0].session.expires?.toISOString?.()??o[0].session.expires};const a=o[0].user??o[0].token;return{user:a,...s}}}})}function pV(o){return typeof o==="function"}function pG(o,i){if(typeof o==="function"){return async(...s)=>{if(!s.length){const s=await pR();const a=await o(undefined);i?.(a);return pz(s,a).then(o=>o.json())}if(s[0]instanceof Request){const a=s[0];const c=s[1];const u=await o(a);i?.(u);return pF([a,c],u)}if(pV(s[0])){const a=s[0];return async(...s)=>{const c=await o(s[0]);i?.(c);return pF(s,c,a)}}const a="req"in s[0]?s[0].req:s[0];const c="res"in s[0]?s[0].res:s[1];const u=await o(a);i?.(u);return pz(new Headers(a.headers),u).then(async o=>{const i=await o.json();for(const i of o.headers.getSetCookie())if("headers"in c)c.headers.append("set-cookie",i);else c.appendHeader("set-cookie",i);return i})}}return(...i)=>{if(!i.length){return Promise.resolve(pR()).then(i=>pz(i,o).then(o=>o.json()))}if(i[0]instanceof Request){const s=i[0];const a=i[1];return pF([s,a],o)}if(pV(i[0])){const s=i[0];return async(...i)=>{return pF(i,o,s).then(o=>{return o})}}const s="req"in i[0]?i[0].req:i[0];const a="res"in i[0]?i[0].res:i[1];return pz(new Headers(s.headers),o).then(async o=>{const i=await o.json();for(const i of o.headers.getSetCookie())if("headers"in a)a.headers.append("set-cookie",i);else a.appendHeader("set-cookie",i);return i})}}async function pF(o,i,s){const a=ps(o[0]);const c=await pz(a.headers,i);const u=await c.json();let l=true;if(i.callbacks?.authorized){l=await i.callbacks.authorized({request:a,auth:u})}let d=e1.next?.();if(l instanceof Response){d=l;const o=l.headers.get("Location");const{pathname:s}=a.nextUrl;if(o&&pX(s,new URL(o).pathname,i)){l=true}}else if(s){const i=a;i.auth=u;d=await s(i,o[1])??e1.next()}else if(!l){const o=i.pages?.signIn??`${i.basePath}/signin`;if(a.nextUrl.pathname!==o){const i=a.nextUrl.clone();i.pathname=o;i.searchParams.set("callbackUrl",a.nextUrl.href);d=e1.redirect(i)}}const f=new Response(d?.body,d);for(const o of c.headers.getSetCookie())f.headers.append("set-cookie",o);return f}function pX(o,i,s){const a=i.replace(`${o}/`,"");const c=Object.values(s.pages??{});return(pY.has(a)||c.includes(i))&&i===o}const pY=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var pZ=s(821);var pQ=s(167);const p0=true?s(830).s:0;function p1(o,i,s){if(s===void 0)s=pZ.Q.TemporaryRedirect;const a=Object.defineProperty(new Error(pQ.oJ),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});a.digest=pQ.oJ+";"+i+";"+o+";"+s+";";return a}function p2(o,i){var s;i!=null?i:i=(p0==null?void 0:(s=p0.getStore())==null?void 0:s.isAction)?pQ.zB.push:pQ.zB.replace;throw p1(o,i,pZ.Q.TemporaryRedirect)}function p5(o,i){if(i===void 0)i=RedirectType.replace;throw p1(o,i,RedirectStatusCode.PermanentRedirect)}function p3(o){if(!isRedirectError(o))return null;return o.digest.split(";").slice(2,-2).join(";")}function p6(o){if(!isRedirectError(o)){throw Object.defineProperty(new Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:false,configurable:true})}return o.digest.split(";",2)[1]}function p4(o){if(!isRedirectError(o)){throw Object.defineProperty(new Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:false,configurable:true})}return Number(o.digest.split(";").at(-2))}var p8=s(159);const p9=""+p8.s8+";404";function p7(){const o=Object.defineProperty(new Error(p9),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});o.digest=p9;throw o};const he=""+p8.s8+";403";function ht(){if(true){throw Object.defineProperty(new Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:false,configurable:true})}const o=Object.defineProperty(new Error(he),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});o.digest=he;throw o};const hn=""+p8.s8+";401";function hr(){if(true){throw Object.defineProperty(new Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:false,configurable:true})}const o=Object.defineProperty(new Error(hn),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});o.digest=hn;throw o};const ho=true?s(792).X:0;class hi extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class hs extends(null&&URLSearchParams){append(){throw new hi}delete(){throw new hi}set(){throw new hi}sort(){throw new hi}};;async function ha(o,i={},s,a){const c=new Headers(await pR());const{redirect:u=true,redirectTo:l,...d}=i instanceof FormData?Object.fromEntries(i):i;const f=l?.toString()??c.get("Referer")??"/";const p=fz("signin",c.get("x-forwarded-proto"),c,process.env,a);if(!o){p.searchParams.append("callbackUrl",f);if(u)p2(p.toString());return p.toString()}let h=`${p}/${o}?${new URLSearchParams(s)}`;let g={};for(const i of a.providers){const{options:s,...a}=typeof i==="function"?i():i;const c=s?.id??a.id;if(c===o){g={id:c,type:s?.type??a.type};break}}if(!g.id){const o=`${p}?${new URLSearchParams({callbackUrl:f})}`;if(u)p2(o);return o}if(g.type==="credentials"){h=h.replace("signin","callback")}c.set("Content-Type","application/x-www-form-urlencoded");const m=new URLSearchParams({...d,callbackUrl:f});const y=new Request(h,{method:"POST",headers:c,body:m});const w=await fV(y,{...a,raw:ae,skipCSRFCheck:s7});const b=await ph();for(const o of w?.cookies??[])b.set(o.name,o.value,o.options);const _=w instanceof Response?w.headers.get("Location"):w.redirect;const v=_??h;if(u)return p2(v);return v}async function hc(o,i){const s=new Headers(await pR());s.set("Content-Type","application/x-www-form-urlencoded");const a=fz("signout",s.get("x-forwarded-proto"),s,process.env,i);const c=o?.redirectTo??s.get("Referer")??"/";const u=new URLSearchParams({callbackUrl:c});const l=new Request(a,{method:"POST",headers:s,body:u});const d=await fV(l,{...i,raw:ae,skipCSRFCheck:s7});const f=await ph();for(const o of d?.cookies??[])f.set(o.name,o.value,o.options);if(o?.redirect??true)return p2(d.redirect);return d}async function hu(o,i){const s=new Headers(await pR());s.set("Content-Type","application/json");const a=fz("session",s.get("x-forwarded-proto"),s,process.env,i);const c=JSON.stringify({data:o});const u=new Request(a,{method:"POST",headers:s,body:c});const l=await fV(u,{...i,raw:ae,skipCSRFCheck:s7});const d=await ph();for(const o of l?.cookies??[])d.set(o.name,o.value,o.options);return l.body};function hl(o){if(typeof o==="function"){const i=async i=>{const s=await o(i);pa(s);return fV(ps(i),s)};return{handlers:{GET:i,POST:i},auth:pG(o,o=>pa(o)),signIn:async(i,s,a)=>{const c=await o(undefined);pa(c);return ha(i,s,a,c)},signOut:async i=>{const s=await o(undefined);pa(s);return hc(i,s)},unstable_update:async i=>{const s=await o(undefined);pa(s);return hu(i,s)}}}pa(o);const i=i=>fV(ps(i),o);return{handlers:{GET:i,POST:i},auth:pG(o),signIn:(i,s,a)=>{return ha(i,s,a,o)},signOut:i=>{return hc(i,o)},unstable_update:i=>{return hu(i,o)}}};function hd(o){const i=o?.enterprise?.baseUrl??"https://github.com";const s=o?.enterprise?.baseUrl?`${o?.enterprise?.baseUrl}/api/v3`:"https://api.github.com";return{id:"github",name:"GitHub",type:"oauth",authorization:{url:`${i}/login/oauth/authorize`,params:{scope:"read:user user:email"}},token:`${i}/login/oauth/access_token`,userinfo:{url:`${s}/user`,async request({tokens:o,provider:i}){const a=await fetch(i.userinfo?.url,{headers:{Authorization:`Bearer ${o.access_token}`,"User-Agent":"authjs"}}).then(async o=>await o.json());if(!a.email){const i=await fetch(`${s}/user/emails`,{headers:{Authorization:`Bearer ${o.access_token}`,"User-Agent":"authjs"}});if(i.ok){const o=await i.json();a.email=(o.find(o=>o.primary)??o[0]).email}}return a}},profile(o){return{id:o.id.toString(),name:o.name??o.login,email:o.email,image:o.avatar_url}},style:{bg:"#24292f",text:"#fff"},options:o}};function hf(o){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:o}};const{handlers:hp,signIn:hh,signOut:hg,auth:hm}=hl({providers:[hd,hf],secret:process.env.AUTH_SECRET});var hy=s(199);const hw={...c};const hb=hw.middleware||hw.default;const h_="/middleware";if(typeof hb!=="function"){throw Object.defineProperty(new Error(`The Middleware "${h_}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:false,configurable:true})}function hv(o){return async(...i)=>{try{return await o(...i)}catch(c){if(false){}const o=i[0];const s=new URL(o.url);const a=s.pathname+s.search;await f(c,{path:a,method:o.method,headers:Object.fromEntries(o.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:undefined});throw c}}}function hE(o){return rm({...o,page:h_,handler:hv(hb)})}},159:(o,i,s)=>{"use strict";s.d(i,{RM:()=>l,s8:()=>u});const a={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401};const c=new Set(Object.values(a));const u="NEXT_HTTP_ERROR_FALLBACK";function l(o){if(typeof o!=="object"||o===null||!("digest"in o)||typeof o.digest!=="string"){return false}const[i,s]=o.digest.split(";");return i===u&&c.has(Number(s))}function d(o){const i=o.digest.split(";")[1];return Number(i)}function f(o){switch(o){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}},167:(o,i,s)=>{"use strict";s.d(i,{nJ:()=>l,oJ:()=>c,zB:()=>u});var a=s(821);const c="NEXT_REDIRECT";var u=function(o){o["push"]="push";o["replace"]="replace";return o}({});function l(o){if(typeof o!=="object"||o===null||!("digest"in o)||typeof o.digest!=="string"){return false}const i=o.digest.split(";");const[s,u]=i;const l=i.slice(2,-2).join(";");const d=i.at(-2);const f=Number(d);return s===c&&(u==="replace"||u==="push")&&typeof l==="string"&&!isNaN(f)&&f in a.Q}},199:(o,i,s)=>{"use strict";s.d(i,{p:()=>u});var a=s(159);var c=s(167);function u(o){return(0,c.nJ)(o)||(0,a.RM)(o)}},201:(o,i,s)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var s in i)Object.defineProperty(o,s,{enumerable:true,get:i[s]})}a(i,{getTestReqInfo:function(){return f},withRequest:function(){return d}});const c=s(521);const u=new c.AsyncLocalStorage;function l(o,i){const s=i.header(o,"next-test-proxy-port");if(!s){return undefined}const a=i.url(o);const c=Number(s);const u=i.header(o,"next-test-data")||"";return{url:a,proxyPort:c,testData:u}}function d(o,i,s){const a=l(o,i);if(!a){return s()}return u.run(a,s)}function f(o,i){const s=u.getStore();if(s){return s}if(o&&i){return l(o,i)}return undefined}},280:(o,i,s)=>{var a="/";var c;(()=>{var u={226:function(a,u){(function(l,d){"use strict";var f="1.0.35",p="",h="?",g="function",m="undefined",y="object",w="string",b="major",_="model",v="name",E="type",S="vendor",k="version",x="architecture",R="console",P="mobile",A="tablet",T="smarttv",O="wearable",C="embedded",I=350;var j="Amazon",N="Apple",D="ASUS",U="BlackBerry",$="Browser",L="Chrome",M="Edge",H="Firefox",W="Google",K="Huawei",q="LG",B="Microsoft",J="Motorola",z="Opera",V="Samsung",G="Sharp",F="Sony",X="Viera",Y="Xiaomi",Z="Zebra",Q="Facebook",ee="Chromium OS",et="Mac OS";var en=function(o,i){var s={};for(var a in o){if(i[a]&&i[a].length%2===0){s[a]=i[a].concat(o[a])}else{s[a]=o[a]}}return s},er=function(o){var i={};for(var s=0;s<o.length;s++){i[o[s].toUpperCase()]=o[s]}return i},eo=function(o,i){return typeof o===w?ei(i).indexOf(ei(o))!==-1:false},ei=function(o){return o.toLowerCase()},es=function(o){return typeof o===w?o.replace(/[^\d\.]/g,p).split(".")[0]:d},ea=function(o,i){if(typeof o===w){o=o.replace(/^\s\s*/,p);return typeof i===m?o:o.substring(0,I)}};var ec=function(o,i){var s=0,a,c,u,l,f,p;while(s<i.length&&!f){var h=i[s],m=i[s+1];a=c=0;while(a<h.length&&!f){if(!h[a]){break}f=h[a++].exec(o);if(!!f){for(u=0;u<m.length;u++){p=f[++c];l=m[u];if(typeof l===y&&l.length>0){if(l.length===2){if(typeof l[1]==g){this[l[0]]=l[1].call(this,p)}else{this[l[0]]=l[1]}}else if(l.length===3){if(typeof l[1]===g&&!(l[1].exec&&l[1].test)){this[l[0]]=p?l[1].call(this,p,l[2]):d}else{this[l[0]]=p?p.replace(l[1],l[2]):d}}else if(l.length===4){this[l[0]]=p?l[3].call(this,p.replace(l[1],l[2])):d}}else{this[l]=p?p:d}}}}s+=2}},eu=function(o,i){for(var s in i){if(typeof i[s]===y&&i[s].length>0){for(var a=0;a<i[s].length;a++){if(eo(i[s][a],o)){return s===h?d:s}}}else if(eo(i[s],o)){return s===h?d:s}}return o};var el={"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},ed={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"};var ef={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[k,[v,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[k,[v,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[v,k],[/opios[\/ ]+([\w\.]+)/i],[k,[v,z+" Mini"]],[/\bopr\/([\w\.]+)/i],[k,[v,z]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[v,k],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[k,[v,"UC"+$]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[k,[v,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[k,[v,"WeChat"]],[/konqueror\/([\w\.]+)/i],[k,[v,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[k,[v,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[k,[v,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[v,/(.+)/,"$1 Secure "+$],k],[/\bfocus\/([\w\.]+)/i],[k,[v,H+" Focus"]],[/\bopt\/([\w\.]+)/i],[k,[v,z+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[k,[v,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[k,[v,"Dolphin"]],[/coast\/([\w\.]+)/i],[k,[v,z+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[k,[v,"MIUI "+$]],[/fxios\/([-\w\.]+)/i],[k,[v,H]],[/\bqihu|(qi?ho?o?|360)browser/i],[[v,"360 "+$]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[v,/(.+)/,"$1 "+$],k],[/(comodo_dragon)\/([\w\.]+)/i],[[v,/_/g," "],k],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[v,k],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[v],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[v,Q],k],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[v,k],[/\bgsa\/([\w\.]+) .*safari\//i],[k,[v,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[k,[v,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[k,[v,L+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[v,L+" WebView"],k],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[k,[v,"Android "+$]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[v,k],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[k,[v,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[k,v],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[v,[k,eu,el]],[/(webkit|khtml)\/([\w\.]+)/i],[v,k],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[v,"Netscape"],k],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[k,[v,H+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[v,k],[/(cobalt)\/([\w\.]+)/i],[v,[k,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[x,"amd64"]],[/(ia32(?=;))/i],[[x,ei]],[/((?:i[346]|x)86)[;\)]/i],[[x,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[x,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[x,"armhf"]],[/windows (ce|mobile); ppc;/i],[[x,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[x,/ower/,p,ei]],[/(sun4\w)[;\)]/i],[[x,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[x,ei]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[_,[S,V],[E,A]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[_,[S,V],[E,P]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[_,[S,N],[E,P]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[_,[S,N],[E,A]],[/(macintosh);/i],[_,[S,N]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[_,[S,G],[E,P]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[_,[S,K],[E,A]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[_,[S,K],[E,P]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[_,/_/g," "],[S,Y],[E,P]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[_,/_/g," "],[S,Y],[E,A]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[_,[S,"OPPO"],[E,P]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[_,[S,"Vivo"],[E,P]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[_,[S,"Realme"],[E,P]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[_,[S,J],[E,P]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[_,[S,J],[E,A]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[_,[S,q],[E,A]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[_,[S,q],[E,P]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[_,[S,"Lenovo"],[E,A]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[_,/_/g," "],[S,"Nokia"],[E,P]],[/(pixel c)\b/i],[_,[S,W],[E,A]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[_,[S,W],[E,P]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[_,[S,F],[E,P]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[_,"Xperia Tablet"],[S,F],[E,A]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[_,[S,"OnePlus"],[E,P]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[_,[S,j],[E,A]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[_,/(.+)/g,"Fire Phone $1"],[S,j],[E,P]],[/(playbook);[-\w\),; ]+(rim)/i],[_,S,[E,A]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[_,[S,U],[E,P]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[_,[S,D],[E,A]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[_,[S,D],[E,P]],[/(nexus 9)/i],[_,[S,"HTC"],[E,A]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[S,[_,/_/g," "],[E,P]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[_,[S,"Acer"],[E,A]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[_,[S,"Meizu"],[E,P]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[S,_,[E,P]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[S,_,[E,A]],[/(surface duo)/i],[_,[S,B],[E,A]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[_,[S,"Fairphone"],[E,P]],[/(u304aa)/i],[_,[S,"AT&T"],[E,P]],[/\bsie-(\w*)/i],[_,[S,"Siemens"],[E,P]],[/\b(rct\w+) b/i],[_,[S,"RCA"],[E,A]],[/\b(venue[\d ]{2,7}) b/i],[_,[S,"Dell"],[E,A]],[/\b(q(?:mv|ta)\w+) b/i],[_,[S,"Verizon"],[E,A]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[_,[S,"Barnes & Noble"],[E,A]],[/\b(tm\d{3}\w+) b/i],[_,[S,"NuVision"],[E,A]],[/\b(k88) b/i],[_,[S,"ZTE"],[E,A]],[/\b(nx\d{3}j) b/i],[_,[S,"ZTE"],[E,P]],[/\b(gen\d{3}) b.+49h/i],[_,[S,"Swiss"],[E,P]],[/\b(zur\d{3}) b/i],[_,[S,"Swiss"],[E,A]],[/\b((zeki)?tb.*\b) b/i],[_,[S,"Zeki"],[E,A]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[S,"Dragon Touch"],_,[E,A]],[/\b(ns-?\w{0,9}) b/i],[_,[S,"Insignia"],[E,A]],[/\b((nxa|next)-?\w{0,9}) b/i],[_,[S,"NextBook"],[E,A]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[S,"Voice"],_,[E,P]],[/\b(lvtel\-)?(v1[12]) b/i],[[S,"LvTel"],_,[E,P]],[/\b(ph-1) /i],[_,[S,"Essential"],[E,P]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[_,[S,"Envizen"],[E,A]],[/\b(trio[-\w\. ]+) b/i],[_,[S,"MachSpeed"],[E,A]],[/\btu_(1491) b/i],[_,[S,"Rotor"],[E,A]],[/(shield[\w ]+) b/i],[_,[S,"Nvidia"],[E,A]],[/(sprint) (\w+)/i],[S,_,[E,P]],[/(kin\.[onetw]{3})/i],[[_,/\./g," "],[S,B],[E,P]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[_,[S,Z],[E,A]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[_,[S,Z],[E,P]],[/smart-tv.+(samsung)/i],[S,[E,T]],[/hbbtv.+maple;(\d+)/i],[[_,/^/,"SmartTV"],[S,V],[E,T]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[S,q],[E,T]],[/(apple) ?tv/i],[S,[_,N+" TV"],[E,T]],[/crkey/i],[[_,L+"cast"],[S,W],[E,T]],[/droid.+aft(\w)( bui|\))/i],[_,[S,j],[E,T]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[_,[S,G],[E,T]],[/(bravia[\w ]+)( bui|\))/i],[_,[S,F],[E,T]],[/(mitv-\w{5}) bui/i],[_,[S,Y],[E,T]],[/Hbbtv.*(technisat) (.*);/i],[S,_,[E,T]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[S,ea],[_,ea],[E,T]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[E,T]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[S,_,[E,R]],[/droid.+; (shield) bui/i],[_,[S,"Nvidia"],[E,R]],[/(playstation [345portablevi]+)/i],[_,[S,F],[E,R]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[_,[S,B],[E,R]],[/((pebble))app/i],[S,_,[E,O]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[_,[S,N],[E,O]],[/droid.+; (glass) \d/i],[_,[S,W],[E,O]],[/droid.+; (wt63?0{2,3})\)/i],[_,[S,Z],[E,O]],[/(quest( 2| pro)?)/i],[_,[S,Q],[E,O]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[S,[E,C]],[/(aeobc)\b/i],[_,[S,j],[E,C]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[_,[E,P]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[_,[E,A]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[E,A]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[E,P]],[/(android[-\w\. ]{0,9});.+buil/i],[_,[S,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[k,[v,M+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[k,[v,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[v,k],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[k,v]],os:[[/microsoft (windows) (vista|xp)/i],[v,k],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[v,[k,eu,ed]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[v,"Windows"],[k,eu,ed]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[k,/_/g,"."],[v,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[v,et],[k,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[k,v],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[v,k],[/\(bb(10);/i],[k,[v,U]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[k,[v,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[k,[v,H+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[k,[v,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[k,[v,"watchOS"]],[/crkey\/([\d\.]+)/i],[k,[v,L+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[v,ee],k],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[v,k],[/(sunos) ?([\w\.\d]*)/i],[[v,"Solaris"],k],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[v,k]]};var ep=function(o,i){if(typeof o===y){i=o;o=d}if(!(this instanceof ep)){return new ep(o,i).getResult()}var s=typeof l!==m&&l.navigator?l.navigator:d;var a=o||(s&&s.userAgent?s.userAgent:p);var c=s&&s.userAgentData?s.userAgentData:d;var u=i?en(ef,i):ef;var f=s&&s.userAgent==a;this.getBrowser=function(){var o={};o[v]=d;o[k]=d;ec.call(o,a,u.browser);o[b]=es(o[k]);if(f&&s&&s.brave&&typeof s.brave.isBrave==g){o[v]="Brave"}return o};this.getCPU=function(){var o={};o[x]=d;ec.call(o,a,u.cpu);return o};this.getDevice=function(){var o={};o[S]=d;o[_]=d;o[E]=d;ec.call(o,a,u.device);if(f&&!o[E]&&c&&c.mobile){o[E]=P}if(f&&o[_]=="Macintosh"&&s&&typeof s.standalone!==m&&s.maxTouchPoints&&s.maxTouchPoints>2){o[_]="iPad";o[E]=A}return o};this.getEngine=function(){var o={};o[v]=d;o[k]=d;ec.call(o,a,u.engine);return o};this.getOS=function(){var o={};o[v]=d;o[k]=d;ec.call(o,a,u.os);if(f&&!o[v]&&c&&c.platform!="Unknown"){o[v]=c.platform.replace(/chrome os/i,ee).replace(/macos/i,et)}return o};this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}};this.getUA=function(){return a};this.setUA=function(o){a=typeof o===w&&o.length>I?ea(o,I):o;return this};this.setUA(a);return this};ep.VERSION=f;ep.BROWSER=er([v,k,b]);ep.CPU=er([x]);ep.DEVICE=er([_,S,E,R,P,T,A,O,C]);ep.ENGINE=ep.OS=er([v,k]);if(typeof u!==m){if("object"!==m&&a.exports){u=a.exports=ep}u.UAParser=ep}else{if("function"===g&&s.amdO){!(c=(function(){return ep}).call(i,s,i,o),c!==undefined&&(o.exports=c))}else if(typeof l!==m){l.UAParser=ep}}var eh=typeof l!==m&&(l.jQuery||l.Zepto);if(eh&&!eh.ua){var eg=new ep;eh.ua=eg.getResult();eh.ua.get=function(){return eg.getUA()};eh.ua.set=function(o){eg.setUA(o);var i=eg.getResult();for(var s in i){eh.ua[s]=i[s]}}}})(typeof window==="object"?window:this)}};var l={};function d(o){var i=l[o];if(i!==undefined){return i.exports}var s=l[o]={exports:{}};var a=true;try{u[o].call(s.exports,s,s.exports,d);a=false}finally{if(a)delete l[o]}return s.exports}if(typeof d!=="undefined")d.ab=a+"/";var f=d(226);o.exports=f})()},356:o=>{"use strict";o.exports=require("node:buffer")},521:o=>{"use strict";o.exports=require("node:async_hooks")},535:(o,i,s)=>{"use strict";s.d(i,{J:()=>c});var a=s(58);const c=(0,a.xl)();},552:(o,i,s)=>{"use strict";var a=s(356)["Buffer"];Object.defineProperty(i,"__esModule",{value:true});0&&0;function c(o,i){for(var s in i)Object.defineProperty(o,s,{enumerable:true,get:i[s]})}c(i,{handleFetch:function(){return h},interceptFetch:function(){return g},reader:function(){return l}});const u=s(201);const l={url(o){return o.url},header(o,i){return o.headers.get(i)}};function d(){let o=(new Error().stack??"").split("\n");for(let i=1;i<o.length;i++){if(o[i].length>0){o=o.slice(i);break}}o=o.filter(o=>!o.includes("/next/dist/"));o=o.slice(0,5);o=o.map(o=>o.replace("webpack-internal:///(rsc)/","").trim());return o.join("    ")}async function f(o,i){const{url:s,method:c,headers:u,body:l,cache:f,credentials:p,integrity:h,mode:g,redirect:m,referrer:y,referrerPolicy:w}=i;return{testData:o,api:"fetch",request:{url:s,method:c,headers:[...Array.from(u),["next-test-stack",d()]],body:l?a.from(await i.arrayBuffer()).toString("base64"):null,cache:f,credentials:p,integrity:h,mode:g,redirect:m,referrer:y,referrerPolicy:w}}}function p(o){const{status:i,headers:s,body:c}=o.response;return new Response(c?a.from(c,"base64"):null,{status:i,headers:new Headers(s)})}async function h(o,i){const s=(0,u.getTestReqInfo)(i,l);if(!s){return o(i)}const{testData:a,proxyPort:c}=s;const d=await f(a,i);const h=await o(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(d),next:{internal:true}});if(!h.ok){throw Object.defineProperty(new Error(`Proxy request failed: ${h.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:false,configurable:true})}const g=await h.json();const{api:m}=g;switch(m){case"continue":return o(i);case"abort":case"unhandled":throw Object.defineProperty(new Error(`Proxy request aborted [${i.method} ${i.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:false,configurable:true});default:break}return p(g)}function g(o){s.g.fetch=function i(i,s){var a;if(s==null?void 0:(a=s.next)==null?void 0:a.internal){return o(i,s)}return h(o,new Request(i,s))};return()=>{s.g.fetch=o}}},557:(o,i,s)=>{"use strict";s.d(i,{t3:()=>P,I3:()=>I,Ui:()=>O,xI:()=>E,Pk:()=>S});var a=s(815);var c=s(16);var u=s(602);var l=s(115);var d=s(535);var f=s(801);const p="__next_metadata_boundary__";const h="__next_viewport_boundary__";const g="__next_outlet_boundary__";const m=typeof a.unstable_postpone==="function";function y(o){return{isDebugDynamicAccesses:o,dynamicAccesses:[],syncDynamicExpression:undefined,syncDynamicErrorWithStack:null}}function w(){return{hasSuspendedDynamic:false,hasDynamicMetadata:false,hasDynamicViewport:false,hasSyncDynamicErrors:false,dynamicErrors:[]}}function b(o){var i;return(i=o.dynamicAccesses[0])==null?void 0:i.expression}function _(o,i,s){if(i){if(i.type==="cache"||i.type==="unstable-cache"){return}}if(o.forceDynamic||o.forceStatic)return;if(o.dynamicShouldError){throw Object.defineProperty(new StaticGenBailoutError(`Route ${o.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${s}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:false,configurable:true})}if(i){if(i.type==="prerender-ppr"){O(o.route,s,i.dynamicTracking)}else if(i.type==="prerender-legacy"){i.revalidate=0;const a=Object.defineProperty(new DynamicServerError(`Route ${o.route} couldn't be rendered statically because it used ${s}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:false,configurable:true});o.dynamicUsageDescription=s;o.dynamicUsageStack=a.stack;throw a}else if(false){}}}function v(o,i){const s=workUnitAsyncStorage.getStore();if(!s||s.type!=="prerender-ppr")return;O(o.route,i,s.dynamicTracking)}function E(o,i,s){const a=Object.defineProperty(new c.F(`Route ${i.route} couldn't be rendered statically because it used \`${o}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:false,configurable:true});s.revalidate=0;i.dynamicUsageDescription=o;i.dynamicUsageStack=a.stack;throw a}function S(o,i){if(i){if(i.type==="cache"||i.type==="unstable-cache"){return}if(i.type==="prerender"||i.type==="prerender-legacy"){i.revalidate=0}if(false){}}}function k(o,i,s){const a=`Route ${o} needs to bail out of prerendering at this point because it used ${i}.`;const c=D(a);s.controller.abort(c);const u=s.dynamicTracking;if(u){u.dynamicAccesses.push({stack:u.isDebugDynamicAccesses?new Error().stack:undefined,expression:i})}}function x(o,i,s,a){const c=a.dynamicTracking;if(c){if(c.syncDynamicErrorWithStack===null){c.syncDynamicExpression=i;c.syncDynamicErrorWithStack=s}}k(o,i,a)}function R(o){o.prerenderPhase=false}function P(o,i,s,a){const c=a.controller.signal;if(c.aborted===false){const c=a.dynamicTracking;if(c){if(c.syncDynamicErrorWithStack===null){c.syncDynamicExpression=i;c.syncDynamicErrorWithStack=s;if(a.validating===true){c.syncDynamicLogged=true}}}k(o,i,a)}throw D(`Route ${o} needs to bail out of prerendering at this point because it used ${i}.`)}const A=null&&R;function T({reason:o,route:i}){const s=workUnitAsyncStorage.getStore();const a=s&&s.type==="prerender-ppr"?s.dynamicTracking:null;O(i,o,a)}function O(o,i,s){H();if(s){s.dynamicAccesses.push({stack:s.isDebugDynamicAccesses?new Error().stack:undefined,expression:i})}a.unstable_postpone(C(o,i))}function C(o,i){return`Route ${o} needs to bail out of prerendering at this point because it used ${i}. `+`React throws this special object to indicate where. It should not be caught by `+`your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function I(o){if(typeof o==="object"&&o!==null&&typeof o.message==="string"){return j(o.message)}return false}function j(o){return o.includes("needs to bail out of prerendering at this point because it used")&&o.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(j(C("%%%","^^^"))===false){throw Object.defineProperty(new Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:false,configurable:true})}const N="NEXT_PRERENDER_INTERRUPTED";function D(o){const i=Object.defineProperty(new Error(o),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});i.digest=N;return i}function U(o){return typeof o==="object"&&o!==null&&o.digest===N&&"name"in o&&"message"in o&&o instanceof Error}function $(o){return o.length>0}function L(o,i){o.dynamicAccesses.push(...i.dynamicAccesses);return o.dynamicAccesses}function M(o){return o.filter(o=>typeof o.stack==="string"&&o.stack.length>0).map(({expression:o,stack:i})=>{i=i.split("\n").slice(4).filter(o=>{if(o.includes("node_modules/next/")){return false}if(o.includes(" (<anonymous>)")){return false}if(o.includes(" (node:")){return false}return true}).join("\n");return`Dynamic API Usage Debug - ${o}:
${i}`})}function H(){if(!m){throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`),"__NEXT_ERROR_CODE",{value:"E224",enumerable:false,configurable:true})}}function W(o){H();const i=new AbortController;try{React.unstable_postpone(o)}catch(o){i.abort(o)}return i.signal}function K(o){const i=new AbortController;if(o.cacheSignal){o.cacheSignal.inputReady().then(()=>{i.abort()})}else{scheduleOnNextTick(()=>i.abort())}return i.signal}function q(o,i){const s=i.dynamicTracking;if(s){s.dynamicAccesses.push({stack:s.isDebugDynamicAccesses?new Error().stack:undefined,expression:o})}}function B(o){const i=workAsyncStorage.getStore();if(i&&i.isStaticGeneration&&i.fallbackRouteParams&&i.fallbackRouteParams.size>0){const s=workUnitAsyncStorage.getStore();if(s){if(s.type==="prerender"){React.use(makeHangingPromise(s.renderSignal,o))}else if(s.type==="prerender-ppr"){O(i.route,o,s.dynamicTracking)}else if(s.type==="prerender-legacy"){E(o,i,s)}}}}const J=/\n\s+at Suspense \(<anonymous>\)/;const z=new RegExp(`\\n\\s+at ${p}[\\n\\s]`);const V=new RegExp(`\\n\\s+at ${h}[\\n\\s]`);const G=new RegExp(`\\n\\s+at ${g}[\\n\\s]`);function F(o,i,s,a,c){if(G.test(i)){return}else if(z.test(i)){s.hasDynamicMetadata=true;return}else if(V.test(i)){s.hasDynamicViewport=true;return}else if(J.test(i)){s.hasSuspendedDynamic=true;return}else if(a.syncDynamicErrorWithStack||c.syncDynamicErrorWithStack){s.hasSyncDynamicErrors=true;return}else{const a=`Route "${o}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;const c=X(a,i);s.dynamicErrors.push(c);return}}function X(o,i){const s=Object.defineProperty(new Error(o),"__NEXT_ERROR_CODE",{value:"E394",enumerable:false,configurable:true});s.stack="Error: "+o+i;return s}function Y(o,i,s,a){let c;let u;let l;if(s.syncDynamicErrorWithStack){c=s.syncDynamicErrorWithStack;u=s.syncDynamicExpression;l=s.syncDynamicLogged===true}else if(a.syncDynamicErrorWithStack){c=a.syncDynamicErrorWithStack;u=a.syncDynamicExpression;l=a.syncDynamicLogged===true}else{c=null;u=undefined;l=false}if(i.hasSyncDynamicErrors&&c){if(!l){console.error(c)}throw new StaticGenBailoutError}const d=i.dynamicErrors;if(d.length){for(let o=0;o<d.length;o++){console.error(d[o])}throw new StaticGenBailoutError}if(!i.hasSuspendedDynamic){if(i.hasDynamicMetadata){if(c){console.error(c);throw Object.defineProperty(new StaticGenBailoutError(`Route "${o}" has a \`generateMetadata\` that could not finish rendering before ${u} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:false,configurable:true})}throw Object.defineProperty(new StaticGenBailoutError(`Route "${o}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:false,configurable:true})}else if(i.hasDynamicViewport){if(c){console.error(c);throw Object.defineProperty(new StaticGenBailoutError(`Route "${o}" has a \`generateViewport\` that could not finish rendering before ${u} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:false,configurable:true})}throw Object.defineProperty(new StaticGenBailoutError(`Route "${o}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:false,configurable:true})}}}},602:(o,i,s)=>{"use strict";s.d(i,{f:()=>c});const a="NEXT_STATIC_GEN_BAILOUT";class c extends Error{constructor(...o){super(...o),this.code=a}}function u(o){if(typeof o!=="object"||o===null||!("code"in o)){return false}return o.code===a}},724:o=>{"use strict";var i=Object.defineProperty;var s=Object.getOwnPropertyDescriptor;var a=Object.getOwnPropertyNames;var c=Object.prototype.hasOwnProperty;var u=(o,s)=>{for(var a in s)i(o,a,{get:s[a],enumerable:true})};var l=(o,u,l,d)=>{if(u&&typeof u==="object"||typeof u==="function"){for(let f of a(u))if(!c.call(o,f)&&f!==l)i(o,f,{get:()=>u[f],enumerable:!(d=s(u,f))||d.enumerable})}return o};var d=o=>l(i({},"__esModule",{value:true}),o);var f={};u(f,{RequestCookies:()=>E,ResponseCookies:()=>S,parseCookie:()=>h,parseSetCookie:()=>g,stringifyCookie:()=>p});o.exports=d(f);function p(o){var i;const s=["path"in o&&o.path&&`Path=${o.path}`,"expires"in o&&(o.expires||o.expires===0)&&`Expires=${(typeof o.expires==="number"?new Date(o.expires):o.expires).toUTCString()}`,"maxAge"in o&&typeof o.maxAge==="number"&&`Max-Age=${o.maxAge}`,"domain"in o&&o.domain&&`Domain=${o.domain}`,"secure"in o&&o.secure&&"Secure","httpOnly"in o&&o.httpOnly&&"HttpOnly","sameSite"in o&&o.sameSite&&`SameSite=${o.sameSite}`,"partitioned"in o&&o.partitioned&&"Partitioned","priority"in o&&o.priority&&`Priority=${o.priority}`].filter(Boolean);const a=`${o.name}=${encodeURIComponent((i=o.value)!=null?i:"")}`;return s.length===0?a:`${a}; ${s.join("; ")}`}function h(o){const i=new Map;for(const s of o.split(/; */)){if(!s)continue;const o=s.indexOf("=");if(o===-1){i.set(s,"true");continue}const[a,c]=[s.slice(0,o),s.slice(o+1)];try{i.set(a,decodeURIComponent(c!=null?c:"true"))}catch{}}return i}function g(o){if(!o){return void 0}const[[i,s],...a]=h(o);const{domain:c,expires:u,httponly:l,maxage:d,path:f,samesite:p,secure:g,partitioned:y,priority:b}=Object.fromEntries(a.map(([o,i])=>[o.toLowerCase().replace(/-/g,""),i]));const v={name:i,value:decodeURIComponent(s),domain:c,...u&&{expires:new Date(u)},...l&&{httpOnly:true},...typeof d==="string"&&{maxAge:Number(d)},path:f,...p&&{sameSite:w(p)},...g&&{secure:true},...b&&{priority:_(b)},...y&&{partitioned:true}};return m(v)}function m(o){const i={};for(const s in o){if(o[s]){i[s]=o[s]}}return i}var y=["strict","lax","none"];function w(o){o=o.toLowerCase();return y.includes(o)?o:void 0}var b=["low","medium","high"];function _(o){o=o.toLowerCase();return b.includes(o)?o:void 0}function v(o){if(!o)return[];var i=[];var s=0;var a;var c;var u;var l;var d;function f(){while(s<o.length&&/\s/.test(o.charAt(s))){s+=1}return s<o.length}function p(){c=o.charAt(s);return c!=="="&&c!==";"&&c!==","}while(s<o.length){a=s;d=false;while(f()){c=o.charAt(s);if(c===","){u=s;s+=1;f();l=s;while(s<o.length&&p()){s+=1}if(s<o.length&&o.charAt(s)==="="){d=true;s=l;i.push(o.substring(a,u));a=s}else{s=u+1}}else{s+=1}}if(!d||s>=o.length){i.push(o.substring(a,o.length))}}return i}var E=class{constructor(o){this._parsed=new Map;this._headers=o;const i=o.get("cookie");if(i){const o=h(i);for(const[i,s]of o){this._parsed.set(i,{name:i,value:s})}}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...o){const i=typeof o[0]==="string"?o[0]:o[0].name;return this._parsed.get(i)}getAll(...o){var i;const s=Array.from(this._parsed);if(!o.length){return s.map(([o,i])=>i)}const a=typeof o[0]==="string"?o[0]:(i=o[0])==null?void 0:i.name;return s.filter(([o])=>o===a).map(([o,i])=>i)}has(o){return this._parsed.has(o)}set(...o){const[i,s]=o.length===1?[o[0].name,o[0].value]:o;const a=this._parsed;a.set(i,{name:i,value:s});this._headers.set("cookie",Array.from(a).map(([o,i])=>p(i)).join("; "));return this}delete(o){const i=this._parsed;const s=!Array.isArray(o)?i.delete(o):o.map(o=>i.delete(o));this._headers.set("cookie",Array.from(i).map(([o,i])=>p(i)).join("; "));return s}clear(){this.delete(Array.from(this._parsed.keys()));return this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o=>`${o.name}=${encodeURIComponent(o.value)}`).join("; ")}};var S=class{constructor(o){this._parsed=new Map;var i,s,a;this._headers=o;const c=(a=(s=(i=o.getSetCookie)==null?void 0:i.call(o))!=null?s:o.get("set-cookie"))!=null?a:[];const u=Array.isArray(c)?c:v(c);for(const o of u){const i=g(o);if(i)this._parsed.set(i.name,i)}}get(...o){const i=typeof o[0]==="string"?o[0]:o[0].name;return this._parsed.get(i)}getAll(...o){var i;const s=Array.from(this._parsed.values());if(!o.length){return s}const a=typeof o[0]==="string"?o[0]:(i=o[0])==null?void 0:i.name;return s.filter(o=>o.name===a)}has(o){return this._parsed.has(o)}set(...o){const[i,s,a]=o.length===1?[o[0].name,o[0].value,o[0]]:o;const c=this._parsed;c.set(i,x({name:i,value:s,...a}));k(c,this._headers);return this}delete(...o){const[i,s]=typeof o[0]==="string"?[o[0]]:[o[0].name,o[0]];return this.set({...s,name:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(p).join("; ")}};function k(o,i){i.delete("set-cookie");for(const[,s]of o){const o=p(s);i.append("set-cookie",o)}}function x(o={name:"",value:""}){if(typeof o.expires==="number"){o.expires=new Date(o.expires)}if(o.maxAge){o.expires=new Date(Date.now()+o.maxAge*1e3)}if(o.path===null||o.path===void 0){o.path="/"}return o}0&&0},792:(o,i,s)=>{"use strict";s.d(i,{X:()=>m});var a=s(801);const c=Symbol.for("react.postpone");function u(o){return typeof o==="object"&&o!==null&&o.$$typeof===c};const l="BAILOUT_TO_CLIENT_SIDE_RENDERING";class d extends Error{constructor(o){super("Bail out to client-side rendering: "+o),this.reason=o,this.digest=l}}function f(o){if(typeof o!=="object"||o===null||!("digest"in o)){return false}return o.digest===l}var p=s(199);var h=s(557);var g=s(16);function m(o){if((0,p.p)(o)||f(o)||(0,g.h)(o)||(0,h.I3)(o)||u(o)||(0,a.T)(o)){throw o}if(o instanceof Error&&"cause"in o){m(o.cause)}}},801:(o,i,s)=>{"use strict";s.d(i,{T:()=>a,W:()=>d});function a(o){if(typeof o!=="object"||o===null||!("digest"in o)){return false}return o.digest===c}const c="HANGING_PROMISE_REJECTION";class u extends Error{constructor(o){super(`During prerendering, ${o} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${o} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=o,this.digest=c}}const l=new WeakMap;function d(o,i){if(o.aborted){return Promise.reject(new u(i))}else{const s=new Promise((s,a)=>{const c=a.bind(null,new u(i));let d=l.get(o);if(d){d.push(c)}else{const i=[c];l.set(o,i);o.addEventListener("abort",()=>{for(let o=0;o<i.length;o++){i[o]()}},{once:true})}});s.catch(f);return s}}function f(){}},802:o=>{var i="/";(()=>{"use strict";var s={993:o=>{var i=Object.prototype.hasOwnProperty,s="~";function a(){}if(Object.create){a.prototype=Object.create(null);if(!(new a).__proto__)s=false}function c(o,i,s){this.fn=o;this.context=i;this.once=s||false}function u(o,i,a,u,l){if(typeof a!=="function"){throw new TypeError("The listener must be a function")}var d=new c(a,u||o,l),f=s?s+i:i;if(!o._events[f])o._events[f]=d,o._eventsCount++;else if(!o._events[f].fn)o._events[f].push(d);else o._events[f]=[o._events[f],d];return o}function l(o,i){if(--o._eventsCount===0)o._events=new a;else delete o._events[i]}function d(){this._events=new a;this._eventsCount=0}d.prototype.eventNames=function o(){var o=[],a,c;if(this._eventsCount===0)return o;for(c in a=this._events){if(i.call(a,c))o.push(s?c.slice(1):c)}if(Object.getOwnPropertySymbols){return o.concat(Object.getOwnPropertySymbols(a))}return o};d.prototype.listeners=function o(o){var i=s?s+o:o,a=this._events[i];if(!a)return[];if(a.fn)return[a.fn];for(var c=0,u=a.length,l=new Array(u);c<u;c++){l[c]=a[c].fn}return l};d.prototype.listenerCount=function o(o){var i=s?s+o:o,a=this._events[i];if(!a)return 0;if(a.fn)return 1;return a.length};d.prototype.emit=function o(o,i,a,c,u,l){var d=s?s+o:o;if(!this._events[d])return false;var f=this._events[d],p=arguments.length,h,g;if(f.fn){if(f.once)this.removeListener(o,f.fn,undefined,true);switch(p){case 1:return f.fn.call(f.context),true;case 2:return f.fn.call(f.context,i),true;case 3:return f.fn.call(f.context,i,a),true;case 4:return f.fn.call(f.context,i,a,c),true;case 5:return f.fn.call(f.context,i,a,c,u),true;case 6:return f.fn.call(f.context,i,a,c,u,l),true}for(g=1,h=new Array(p-1);g<p;g++){h[g-1]=arguments[g]}f.fn.apply(f.context,h)}else{var m=f.length,y;for(g=0;g<m;g++){if(f[g].once)this.removeListener(o,f[g].fn,undefined,true);switch(p){case 1:f[g].fn.call(f[g].context);break;case 2:f[g].fn.call(f[g].context,i);break;case 3:f[g].fn.call(f[g].context,i,a);break;case 4:f[g].fn.call(f[g].context,i,a,c);break;default:if(!h)for(y=1,h=new Array(p-1);y<p;y++){h[y-1]=arguments[y]}f[g].fn.apply(f[g].context,h)}}}return true};d.prototype.on=function o(o,i,s){return u(this,o,i,s,false)};d.prototype.once=function o(o,i,s){return u(this,o,i,s,true)};d.prototype.removeListener=function o(o,i,a,c){var u=s?s+o:o;if(!this._events[u])return this;if(!i){l(this,u);return this}var d=this._events[u];if(d.fn){if(d.fn===i&&(!c||d.once)&&(!a||d.context===a)){l(this,u)}}else{for(var f=0,p=[],h=d.length;f<h;f++){if(d[f].fn!==i||c&&!d[f].once||a&&d[f].context!==a){p.push(d[f])}}if(p.length)this._events[u]=p.length===1?p[0]:p;else l(this,u)}return this};d.prototype.removeAllListeners=function o(o){var i;if(o){i=s?s+o:o;if(this._events[i])l(this,i)}else{this._events=new a;this._eventsCount=0}return this};d.prototype.off=d.prototype.removeListener;d.prototype.addListener=d.prototype.on;d.prefixed=s;d.EventEmitter=d;if(true){o.exports=d}},213:o=>{o.exports=(o,i)=>{i=i||(()=>{});return o.then(o=>new Promise(o=>{o(i())}).then(()=>o),o=>new Promise(o=>{o(i())}).then(()=>{throw o}))}},574:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});function s(o,i,s){let a=0;let c=o.length;while(c>0){const u=c/2|0;let l=a+u;if(s(o[l],i)<=0){a=++l;c-=u+1}else{c=u}}return a}i["default"]=s},821:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});const a=s(574);class c{constructor(){this._queue=[]}enqueue(o,i){i=Object.assign({priority:0},i);const s={priority:i.priority,run:o};if(this.size&&this._queue[this.size-1].priority>=i.priority){this._queue.push(s);return}const c=a.default(this._queue,s,(o,i)=>i.priority-o.priority);this._queue.splice(c,0,s)}dequeue(){const o=this._queue.shift();return o===null||o===void 0?void 0:o.run}filter(o){return this._queue.filter(i=>i.priority===o.priority).map(o=>o.run)}get size(){return this._queue.length}}i["default"]=c},816:(o,i,s)=>{const a=s(213);class c extends Error{constructor(o){super(o);this.name="TimeoutError"}}const u=(o,i,s)=>new Promise((u,l)=>{if(typeof i!=="number"||i<0){throw new TypeError("Expected `milliseconds` to be a positive number")}if(i===Infinity){u(o);return}const d=setTimeout(()=>{if(typeof s==="function"){try{u(s())}catch(o){l(o)}return}const a=typeof s==="string"?s:`Promise timed out after ${i} milliseconds`;const d=s instanceof Error?s:new c(a);if(typeof o.cancel==="function"){o.cancel()}l(d)},i);a(o.then(u,l),()=>{clearTimeout(d)})});o.exports=u;o.exports["default"]=u;o.exports.TimeoutError=c}};var a={};function c(o){var i=a[o];if(i!==undefined){return i.exports}var u=a[o]={exports:{}};var l=true;try{s[o](u,u.exports,c);l=false}finally{if(l)delete a[o]}return u.exports}if(typeof c!=="undefined")c.ab=i+"/";var u={};(()=>{var o=u;Object.defineProperty(o,"__esModule",{value:true});const i=c(993);const s=c(816);const a=c(821);const l=()=>{};const d=new s.TimeoutError;class f extends i{constructor(o){var i,s,c,u;super();this._intervalCount=0;this._intervalEnd=0;this._pendingCount=0;this._resolveEmpty=l;this._resolveIdle=l;o=Object.assign({carryoverConcurrencyCount:false,intervalCap:Infinity,interval:0,concurrency:Infinity,autoStart:true,queueClass:a.default},o);if(!(typeof o.intervalCap==="number"&&o.intervalCap>=1)){throw new TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${(s=(i=o.intervalCap)===null||i===void 0?void 0:i.toString())!==null&&s!==void 0?s:""}\` (${typeof o.intervalCap})`)}if(o.interval===undefined||!(Number.isFinite(o.interval)&&o.interval>=0)){throw new TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${(u=(c=o.interval)===null||c===void 0?void 0:c.toString())!==null&&u!==void 0?u:""}\` (${typeof o.interval})`)}this._carryoverConcurrencyCount=o.carryoverConcurrencyCount;this._isIntervalIgnored=o.intervalCap===Infinity||o.interval===0;this._intervalCap=o.intervalCap;this._interval=o.interval;this._queue=new o.queueClass;this._queueClass=o.queueClass;this.concurrency=o.concurrency;this._timeout=o.timeout;this._throwOnTimeout=o.throwOnTimeout===true;this._isPaused=o.autoStart===false}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--;this._tryToStartAnother();this.emit("next")}_resolvePromises(){this._resolveEmpty();this._resolveEmpty=l;if(this._pendingCount===0){this._resolveIdle();this._resolveIdle=l;this.emit("idle")}}_onResumeInterval(){this._onInterval();this._initializeIntervalIfNeeded();this._timeoutId=undefined}_isIntervalPaused(){const o=Date.now();if(this._intervalId===undefined){const i=this._intervalEnd-o;if(i<0){this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}else{if(this._timeoutId===undefined){this._timeoutId=setTimeout(()=>{this._onResumeInterval()},i)}return true}}return false}_tryToStartAnother(){if(this._queue.size===0){if(this._intervalId){clearInterval(this._intervalId)}this._intervalId=undefined;this._resolvePromises();return false}if(!this._isPaused){const o=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){const i=this._queue.dequeue();if(!i){return false}this.emit("active");i();if(o){this._initializeIntervalIfNeeded()}return true}}return false}_initializeIntervalIfNeeded(){if(this._isIntervalIgnored||this._intervalId!==undefined){return}this._intervalId=setInterval(()=>{this._onInterval()},this._interval);this._intervalEnd=Date.now()+this._interval}_onInterval(){if(this._intervalCount===0&&this._pendingCount===0&&this._intervalId){clearInterval(this._intervalId);this._intervalId=undefined}this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0;this._processQueue()}_processQueue(){while(this._tryToStartAnother()){}}get concurrency(){return this._concurrency}set concurrency(o){if(!(typeof o==="number"&&o>=1)){throw new TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${o}\` (${typeof o})`)}this._concurrency=o;this._processQueue()}async add(o,i={}){return new Promise((a,c)=>{const u=async()=>{this._pendingCount++;this._intervalCount++;try{const u=this._timeout===undefined&&i.timeout===undefined?o():s.default(Promise.resolve(o()),i.timeout===undefined?this._timeout:i.timeout,()=>{if(i.throwOnTimeout===undefined?this._throwOnTimeout:i.throwOnTimeout){c(d)}return undefined});a(await u)}catch(o){c(o)}this._next()};this._queue.enqueue(u,i);this._tryToStartAnother();this.emit("add")})}async addAll(o,i){return Promise.all(o.map(async o=>this.add(o,i)))}start(){if(!this._isPaused){return this}this._isPaused=false;this._processQueue();return this}pause(){this._isPaused=true}clear(){this._queue=new this._queueClass}async onEmpty(){if(this._queue.size===0){return}return new Promise(o=>{const i=this._resolveEmpty;this._resolveEmpty=()=>{i();o()}})}async onIdle(){if(this._pendingCount===0&&this._queue.size===0){return}return new Promise(o=>{const i=this._resolveIdle;this._resolveIdle=()=>{i();o()}})}get size(){return this._queue.size}sizeBy(o){return this._queue.filter(o).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(o){this._timeout=o}}o["default"]=f})();o.exports=u})()},815:(o,i,s)=>{"use strict";if(true){o.exports=s(35)}else{}},821:(o,i,s)=>{"use strict";s.d(i,{Q:()=>a});var a=function(o){o[o["SeeOther"]=303]="SeeOther";o[o["TemporaryRedirect"]=307]="TemporaryRedirect";o[o["PermanentRedirect"]=308]="PermanentRedirect";return o}({})},830:(o,i,s)=>{"use strict";s.d(i,{s:()=>c});var a=s(58);const c=(0,a.xl)();},890:o=>{var i="/";(()=>{"use strict";if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=i+"/";var s={};(()=>{var o=s;o.parse=l;o.serialize=d;var i=decodeURIComponent;var a=encodeURIComponent;var c=/; */;var u=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function l(o,s){if(typeof o!=="string"){throw new TypeError("argument str must be a string")}var a={};var u=s||{};var l=o.split(c);var d=u.decode||i;for(var p=0;p<l.length;p++){var h=l[p];var g=h.indexOf("=");if(g<0){continue}var m=h.substr(0,g).trim();var y=h.substr(++g,h.length).trim();if('"'==y[0]){y=y.slice(1,-1)}if(undefined==a[m]){a[m]=f(y,d)}}return a}function d(o,i,s){var c=s||{};var l=c.encode||a;if(typeof l!=="function"){throw new TypeError("option encode is invalid")}if(!u.test(o)){throw new TypeError("argument name is invalid")}var d=l(i);if(d&&!u.test(d)){throw new TypeError("argument val is invalid")}var f=o+"="+d;if(null!=c.maxAge){var p=c.maxAge-0;if(isNaN(p)||!isFinite(p)){throw new TypeError("option maxAge is invalid")}f+="; Max-Age="+Math.floor(p)}if(c.domain){if(!u.test(c.domain)){throw new TypeError("option domain is invalid")}f+="; Domain="+c.domain}if(c.path){if(!u.test(c.path)){throw new TypeError("option path is invalid")}f+="; Path="+c.path}if(c.expires){if(typeof c.expires.toUTCString!=="function"){throw new TypeError("option expires is invalid")}f+="; Expires="+c.expires.toUTCString()}if(c.httpOnly){f+="; HttpOnly"}if(c.secure){f+="; Secure"}if(c.sameSite){var h=typeof c.sameSite==="string"?c.sameSite.toLowerCase():c.sameSite;switch(h){case true:f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"strict":f+="; SameSite=Strict";break;case"none":f+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return f}function f(o,i){try{return i(o)}catch(i){return o}}})();o.exports=s})()},905:(o,i,s)=>{"use strict";Object.defineProperty(i,"__esModule",{value:true});0&&0;function a(o,i){for(var s in i)Object.defineProperty(o,s,{enumerable:true,get:i[s]})}a(i,{interceptTestApis:function(){return l},wrapRequestHandler:function(){return d}});const c=s(201);const u=s(552);function l(){return(0,u.interceptFetch)(s.g.fetch)}function d(o){return(i,s)=>(0,c.withRequest)(i,u.reader,()=>o(i,s))}},956:(o,i,s)=>{var a="/";(()=>{"use strict";var i={491:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.ContextAPI=void 0;const a=s(223);const c=s(172);const u=s(930);const l="context";const d=new a.NoopContextManager;class f{constructor(){}static getInstance(){if(!this._instance){this._instance=new f}return this._instance}setGlobalContextManager(o){return(0,c.registerGlobal)(l,o,u.DiagAPI.instance())}active(){return this._getContextManager().active()}with(o,i,s,...a){return this._getContextManager().with(o,i,s,...a)}bind(o,i){return this._getContextManager().bind(o,i)}_getContextManager(){return(0,c.getGlobal)(l)||d}disable(){this._getContextManager().disable();(0,c.unregisterGlobal)(l,u.DiagAPI.instance())}}i.ContextAPI=f},930:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagAPI=void 0;const a=s(56);const c=s(912);const u=s(957);const l=s(172);const d="diag";class f{constructor(){function o(o){return function(...i){const s=(0,l.getGlobal)("diag");if(!s)return;return s[o](...i)}}const i=this;const s=(o,s={logLevel:u.DiagLogLevel.INFO})=>{var a,d,f;if(o===i){const o=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");i.error((a=o.stack)!==null&&a!==void 0?a:o.message);return false}if(typeof s==="number"){s={logLevel:s}}const p=(0,l.getGlobal)("diag");const h=(0,c.createLogLevelDiagLogger)((d=s.logLevel)!==null&&d!==void 0?d:u.DiagLogLevel.INFO,o);if(p&&!s.suppressOverrideMessage){const o=(f=(new Error).stack)!==null&&f!==void 0?f:"<failed to generate stacktrace>";p.warn(`Current logger will be overwritten from ${o}`);h.warn(`Current logger will overwrite one already registered from ${o}`)}return(0,l.registerGlobal)("diag",h,i,true)};i.setLogger=s;i.disable=()=>{(0,l.unregisterGlobal)(d,i)};i.createComponentLogger=o=>new a.DiagComponentLogger(o);i.verbose=o("verbose");i.debug=o("debug");i.info=o("info");i.warn=o("warn");i.error=o("error")}static instance(){if(!this._instance){this._instance=new f}return this._instance}}i.DiagAPI=f},653:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.MetricsAPI=void 0;const a=s(660);const c=s(172);const u=s(930);const l="metrics";class d{constructor(){}static getInstance(){if(!this._instance){this._instance=new d}return this._instance}setGlobalMeterProvider(o){return(0,c.registerGlobal)(l,o,u.DiagAPI.instance())}getMeterProvider(){return(0,c.getGlobal)(l)||a.NOOP_METER_PROVIDER}getMeter(o,i,s){return this.getMeterProvider().getMeter(o,i,s)}disable(){(0,c.unregisterGlobal)(l,u.DiagAPI.instance())}}i.MetricsAPI=d},181:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.PropagationAPI=void 0;const a=s(172);const c=s(874);const u=s(194);const l=s(277);const d=s(369);const f=s(930);const p="propagation";const h=new c.NoopTextMapPropagator;class g{constructor(){this.createBaggage=d.createBaggage;this.getBaggage=l.getBaggage;this.getActiveBaggage=l.getActiveBaggage;this.setBaggage=l.setBaggage;this.deleteBaggage=l.deleteBaggage}static getInstance(){if(!this._instance){this._instance=new g}return this._instance}setGlobalPropagator(o){return(0,a.registerGlobal)(p,o,f.DiagAPI.instance())}inject(o,i,s=u.defaultTextMapSetter){return this._getGlobalPropagator().inject(o,i,s)}extract(o,i,s=u.defaultTextMapGetter){return this._getGlobalPropagator().extract(o,i,s)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,a.unregisterGlobal)(p,f.DiagAPI.instance())}_getGlobalPropagator(){return(0,a.getGlobal)(p)||h}}i.PropagationAPI=g},997:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.TraceAPI=void 0;const a=s(172);const c=s(846);const u=s(139);const l=s(607);const d=s(930);const f="trace";class p{constructor(){this._proxyTracerProvider=new c.ProxyTracerProvider;this.wrapSpanContext=u.wrapSpanContext;this.isSpanContextValid=u.isSpanContextValid;this.deleteSpan=l.deleteSpan;this.getSpan=l.getSpan;this.getActiveSpan=l.getActiveSpan;this.getSpanContext=l.getSpanContext;this.setSpan=l.setSpan;this.setSpanContext=l.setSpanContext}static getInstance(){if(!this._instance){this._instance=new p}return this._instance}setGlobalTracerProvider(o){const i=(0,a.registerGlobal)(f,this._proxyTracerProvider,d.DiagAPI.instance());if(i){this._proxyTracerProvider.setDelegate(o)}return i}getTracerProvider(){return(0,a.getGlobal)(f)||this._proxyTracerProvider}getTracer(o,i){return this.getTracerProvider().getTracer(o,i)}disable(){(0,a.unregisterGlobal)(f,d.DiagAPI.instance());this._proxyTracerProvider=new c.ProxyTracerProvider}}i.TraceAPI=p},277:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.deleteBaggage=i.setBaggage=i.getActiveBaggage=i.getBaggage=void 0;const a=s(491);const c=s(780);const u=(0,c.createContextKey)("OpenTelemetry Baggage Key");function l(o){return o.getValue(u)||undefined}i.getBaggage=l;function d(){return l(a.ContextAPI.getInstance().active())}i.getActiveBaggage=d;function f(o,i){return o.setValue(u,i)}i.setBaggage=f;function p(o){return o.deleteValue(u)}i.deleteBaggage=p},993:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.BaggageImpl=void 0;class s{constructor(o){this._entries=o?new Map(o):new Map}getEntry(o){const i=this._entries.get(o);if(!i){return undefined}return Object.assign({},i)}getAllEntries(){return Array.from(this._entries.entries()).map(([o,i])=>[o,i])}setEntry(o,i){const a=new s(this._entries);a._entries.set(o,i);return a}removeEntry(o){const i=new s(this._entries);i._entries.delete(o);return i}removeEntries(...o){const i=new s(this._entries);for(const s of o){i._entries.delete(s)}return i}clear(){return new s}}i.BaggageImpl=s},830:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.baggageEntryMetadataSymbol=void 0;i.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.baggageEntryMetadataFromString=i.createBaggage=void 0;const a=s(930);const c=s(993);const u=s(830);const l=a.DiagAPI.instance();function d(o={}){return new c.BaggageImpl(new Map(Object.entries(o)))}i.createBaggage=d;function f(o){if(typeof o!=="string"){l.error(`Cannot create baggage metadata from unknown type: ${typeof o}`);o=""}return{__TYPE__:u.baggageEntryMetadataSymbol,toString(){return o}}}i.baggageEntryMetadataFromString=f},67:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.context=void 0;const a=s(491);i.context=a.ContextAPI.getInstance()},223:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopContextManager=void 0;const a=s(780);class c{active(){return a.ROOT_CONTEXT}with(o,i,s,...a){return i.call(s,...a)}bind(o,i){return i}enable(){return this}disable(){return this}}i.NoopContextManager=c},780:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.ROOT_CONTEXT=i.createContextKey=void 0;function s(o){return Symbol.for(o)}i.createContextKey=s;class a{constructor(o){const i=this;i._currentContext=o?new Map(o):new Map;i.getValue=o=>i._currentContext.get(o);i.setValue=(o,s)=>{const c=new a(i._currentContext);c._currentContext.set(o,s);return c};i.deleteValue=o=>{const s=new a(i._currentContext);s._currentContext.delete(o);return s}}}i.ROOT_CONTEXT=new a},506:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.diag=void 0;const a=s(930);i.diag=a.DiagAPI.instance()},56:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagComponentLogger=void 0;const a=s(172);class c{constructor(o){this._namespace=o.namespace||"DiagComponentLogger"}debug(...o){return u("debug",this._namespace,o)}error(...o){return u("error",this._namespace,o)}info(...o){return u("info",this._namespace,o)}warn(...o){return u("warn",this._namespace,o)}verbose(...o){return u("verbose",this._namespace,o)}}i.DiagComponentLogger=c;function u(o,i,s){const c=(0,a.getGlobal)("diag");if(!c){return}s.unshift(i);return c[o](...s)}},972:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagConsoleLogger=void 0;const s=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class a{constructor(){function o(o){return function(...i){if(console){let s=console[o];if(typeof s!=="function"){s=console.log}if(typeof s==="function"){return s.apply(console,i)}}}}for(let i=0;i<s.length;i++){this[s[i].n]=o(s[i].c)}}}i.DiagConsoleLogger=a},912:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.createLogLevelDiagLogger=void 0;const a=s(957);function c(o,i){if(o<a.DiagLogLevel.NONE){o=a.DiagLogLevel.NONE}else if(o>a.DiagLogLevel.ALL){o=a.DiagLogLevel.ALL}i=i||{};function s(s,a){const c=i[s];if(typeof c==="function"&&o>=a){return c.bind(i)}return function(){}}return{error:s("error",a.DiagLogLevel.ERROR),warn:s("warn",a.DiagLogLevel.WARN),info:s("info",a.DiagLogLevel.INFO),debug:s("debug",a.DiagLogLevel.DEBUG),verbose:s("verbose",a.DiagLogLevel.VERBOSE)}}i.createLogLevelDiagLogger=c},957:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.DiagLogLevel=void 0;var s;(function(o){o[o["NONE"]=0]="NONE";o[o["ERROR"]=30]="ERROR";o[o["WARN"]=50]="WARN";o[o["INFO"]=60]="INFO";o[o["DEBUG"]=70]="DEBUG";o[o["VERBOSE"]=80]="VERBOSE";o[o["ALL"]=9999]="ALL"})(s=i.DiagLogLevel||(i.DiagLogLevel={}))},172:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.unregisterGlobal=i.getGlobal=i.registerGlobal=void 0;const a=s(200);const c=s(521);const u=s(130);const l=c.VERSION.split(".")[0];const d=Symbol.for(`opentelemetry.js.api.${l}`);const f=a._globalThis;function p(o,i,s,a=false){var u;const l=f[d]=(u=f[d])!==null&&u!==void 0?u:{version:c.VERSION};if(!a&&l[o]){const i=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${o}`);s.error(i.stack||i.message);return false}if(l.version!==c.VERSION){const i=new Error(`@opentelemetry/api: Registration of version v${l.version} for ${o} does not match previously registered API v${c.VERSION}`);s.error(i.stack||i.message);return false}l[o]=i;s.debug(`@opentelemetry/api: Registered a global for ${o} v${c.VERSION}.`);return true}i.registerGlobal=p;function h(o){var i,s;const a=(i=f[d])===null||i===void 0?void 0:i.version;if(!a||!(0,u.isCompatible)(a)){return}return(s=f[d])===null||s===void 0?void 0:s[o]}i.getGlobal=h;function g(o,i){i.debug(`@opentelemetry/api: Unregistering a global for ${o} v${c.VERSION}.`);const s=f[d];if(s){delete s[o]}}i.unregisterGlobal=g},130:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.isCompatible=i._makeCompatibilityCheck=void 0;const a=s(521);const c=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function u(o){const i=new Set([o]);const s=new Set;const a=o.match(c);if(!a){return()=>false}const u={major:+a[1],minor:+a[2],patch:+a[3],prerelease:a[4]};if(u.prerelease!=null){return function i(i){return i===o}}function l(o){s.add(o);return false}function d(o){i.add(o);return true}return function o(o){if(i.has(o)){return true}if(s.has(o)){return false}const a=o.match(c);if(!a){return l(o)}const f={major:+a[1],minor:+a[2],patch:+a[3],prerelease:a[4]};if(f.prerelease!=null){return l(o)}if(u.major!==f.major){return l(o)}if(u.major===0){if(u.minor===f.minor&&u.patch<=f.patch){return d(o)}return l(o)}if(u.minor<=f.minor){return d(o)}return l(o)}}i._makeCompatibilityCheck=u;i.isCompatible=u(a.VERSION)},886:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.metrics=void 0;const a=s(653);i.metrics=a.MetricsAPI.getInstance()},901:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.ValueType=void 0;var s;(function(o){o[o["INT"]=0]="INT";o[o["DOUBLE"]=1]="DOUBLE"})(s=i.ValueType||(i.ValueType={}))},102:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.createNoopMeter=i.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=i.NOOP_OBSERVABLE_GAUGE_METRIC=i.NOOP_OBSERVABLE_COUNTER_METRIC=i.NOOP_UP_DOWN_COUNTER_METRIC=i.NOOP_HISTOGRAM_METRIC=i.NOOP_COUNTER_METRIC=i.NOOP_METER=i.NoopObservableUpDownCounterMetric=i.NoopObservableGaugeMetric=i.NoopObservableCounterMetric=i.NoopObservableMetric=i.NoopHistogramMetric=i.NoopUpDownCounterMetric=i.NoopCounterMetric=i.NoopMetric=i.NoopMeter=void 0;class s{constructor(){}createHistogram(o,s){return i.NOOP_HISTOGRAM_METRIC}createCounter(o,s){return i.NOOP_COUNTER_METRIC}createUpDownCounter(o,s){return i.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(o,s){return i.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(o,s){return i.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(o,s){return i.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(o,i){}removeBatchObservableCallback(o){}}i.NoopMeter=s;class a{}i.NoopMetric=a;class c extends a{add(o,i){}}i.NoopCounterMetric=c;class u extends a{add(o,i){}}i.NoopUpDownCounterMetric=u;class l extends a{record(o,i){}}i.NoopHistogramMetric=l;class d{addCallback(o){}removeCallback(o){}}i.NoopObservableMetric=d;class f extends d{}i.NoopObservableCounterMetric=f;class p extends d{}i.NoopObservableGaugeMetric=p;class h extends d{}i.NoopObservableUpDownCounterMetric=h;i.NOOP_METER=new s;i.NOOP_COUNTER_METRIC=new c;i.NOOP_HISTOGRAM_METRIC=new l;i.NOOP_UP_DOWN_COUNTER_METRIC=new u;i.NOOP_OBSERVABLE_COUNTER_METRIC=new f;i.NOOP_OBSERVABLE_GAUGE_METRIC=new p;i.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new h;function g(){return i.NOOP_METER}i.createNoopMeter=g},660:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.NOOP_METER_PROVIDER=i.NoopMeterProvider=void 0;const a=s(102);class c{getMeter(o,i,s){return a.NOOP_METER}}i.NoopMeterProvider=c;i.NOOP_METER_PROVIDER=new c},200:function(o,i,s){var a=this&&this.__createBinding||(Object.create?function(o,i,s,a){if(a===undefined)a=s;Object.defineProperty(o,a,{enumerable:true,get:function(){return i[s]}})}:function(o,i,s,a){if(a===undefined)a=s;o[a]=i[s]});var c=this&&this.__exportStar||function(o,i){for(var s in o)if(s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s))a(i,o,s)};Object.defineProperty(i,"__esModule",{value:true});c(s(46),i)},651:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i._globalThis=void 0;i._globalThis=typeof globalThis==="object"?globalThis:s.g},46:function(o,i,s){var a=this&&this.__createBinding||(Object.create?function(o,i,s,a){if(a===undefined)a=s;Object.defineProperty(o,a,{enumerable:true,get:function(){return i[s]}})}:function(o,i,s,a){if(a===undefined)a=s;o[a]=i[s]});var c=this&&this.__exportStar||function(o,i){for(var s in o)if(s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s))a(i,o,s)};Object.defineProperty(i,"__esModule",{value:true});c(s(651),i)},939:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.propagation=void 0;const a=s(181);i.propagation=a.PropagationAPI.getInstance()},874:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopTextMapPropagator=void 0;class s{inject(o,i){}extract(o,i){return o}fields(){return[]}}i.NoopTextMapPropagator=s},194:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.defaultTextMapSetter=i.defaultTextMapGetter=void 0;i.defaultTextMapGetter={get(o,i){if(o==null){return undefined}return o[i]},keys(o){if(o==null){return[]}return Object.keys(o)}};i.defaultTextMapSetter={set(o,i,s){if(o==null){return}o[i]=s}}},845:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.trace=void 0;const a=s(997);i.trace=a.TraceAPI.getInstance()},403:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.NonRecordingSpan=void 0;const a=s(476);class c{constructor(o=a.INVALID_SPAN_CONTEXT){this._spanContext=o}spanContext(){return this._spanContext}setAttribute(o,i){return this}setAttributes(o){return this}addEvent(o,i){return this}setStatus(o){return this}updateName(o){return this}end(o){}isRecording(){return false}recordException(o,i){}}i.NonRecordingSpan=c},614:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopTracer=void 0;const a=s(491);const c=s(607);const u=s(403);const l=s(139);const d=a.ContextAPI.getInstance();class f{startSpan(o,i,s=d.active()){const a=Boolean(i===null||i===void 0?void 0:i.root);if(a){return new u.NonRecordingSpan}const f=s&&(0,c.getSpanContext)(s);if(p(f)&&(0,l.isSpanContextValid)(f)){return new u.NonRecordingSpan(f)}else{return new u.NonRecordingSpan}}startActiveSpan(o,i,s,a){let u;let l;let f;if(arguments.length<2){return}else if(arguments.length===2){f=i}else if(arguments.length===3){u=i;f=s}else{u=i;l=s;f=a}const p=l!==null&&l!==void 0?l:d.active();const h=this.startSpan(o,u,p);const g=(0,c.setSpan)(p,h);return d.with(g,f,undefined,h)}}i.NoopTracer=f;function p(o){return typeof o==="object"&&typeof o["spanId"]==="string"&&typeof o["traceId"]==="string"&&typeof o["traceFlags"]==="number"}},124:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.NoopTracerProvider=void 0;const a=s(614);class c{getTracer(o,i,s){return new a.NoopTracer}}i.NoopTracerProvider=c},125:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.ProxyTracer=void 0;const a=s(614);const c=new a.NoopTracer;class u{constructor(o,i,s,a){this._provider=o;this.name=i;this.version=s;this.options=a}startSpan(o,i,s){return this._getTracer().startSpan(o,i,s)}startActiveSpan(o,i,s,a){const c=this._getTracer();return Reflect.apply(c.startActiveSpan,c,arguments)}_getTracer(){if(this._delegate){return this._delegate}const o=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!o){return c}this._delegate=o;return this._delegate}}i.ProxyTracer=u},846:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.ProxyTracerProvider=void 0;const a=s(125);const c=s(124);const u=new c.NoopTracerProvider;class l{getTracer(o,i,s){var c;return(c=this.getDelegateTracer(o,i,s))!==null&&c!==void 0?c:new a.ProxyTracer(this,o,i,s)}getDelegate(){var o;return(o=this._delegate)!==null&&o!==void 0?o:u}setDelegate(o){this._delegate=o}getDelegateTracer(o,i,s){var a;return(a=this._delegate)===null||a===void 0?void 0:a.getTracer(o,i,s)}}i.ProxyTracerProvider=l},996:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.SamplingDecision=void 0;var s;(function(o){o[o["NOT_RECORD"]=0]="NOT_RECORD";o[o["RECORD"]=1]="RECORD";o[o["RECORD_AND_SAMPLED"]=2]="RECORD_AND_SAMPLED"})(s=i.SamplingDecision||(i.SamplingDecision={}))},607:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.getSpanContext=i.setSpanContext=i.deleteSpan=i.setSpan=i.getActiveSpan=i.getSpan=void 0;const a=s(780);const c=s(403);const u=s(491);const l=(0,a.createContextKey)("OpenTelemetry Context Key SPAN");function d(o){return o.getValue(l)||undefined}i.getSpan=d;function f(){return d(u.ContextAPI.getInstance().active())}i.getActiveSpan=f;function p(o,i){return o.setValue(l,i)}i.setSpan=p;function h(o){return o.deleteValue(l)}i.deleteSpan=h;function g(o,i){return p(o,new c.NonRecordingSpan(i))}i.setSpanContext=g;function m(o){var i;return(i=d(o))===null||i===void 0?void 0:i.spanContext()}i.getSpanContext=m},325:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.TraceStateImpl=void 0;const a=s(564);const c=32;const u=512;const l=",";const d="=";class f{constructor(o){this._internalState=new Map;if(o)this._parse(o)}set(o,i){const s=this._clone();if(s._internalState.has(o)){s._internalState.delete(o)}s._internalState.set(o,i);return s}unset(o){const i=this._clone();i._internalState.delete(o);return i}get(o){return this._internalState.get(o)}serialize(){return this._keys().reduce((o,i)=>{o.push(i+d+this.get(i));return o},[]).join(l)}_parse(o){if(o.length>u)return;this._internalState=o.split(l).reverse().reduce((o,i)=>{const s=i.trim();const c=s.indexOf(d);if(c!==-1){const u=s.slice(0,c);const l=s.slice(c+1,i.length);if((0,a.validateKey)(u)&&(0,a.validateValue)(l)){o.set(u,l)}else{}}return o},new Map);if(this._internalState.size>c){this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,c))}}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){const o=new f;o._internalState=new Map(this._internalState);return o}}i.TraceStateImpl=f},564:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.validateValue=i.validateKey=void 0;const s="[_0-9a-z-*/]";const a=`[a-z]${s}{0,255}`;const c=`[a-z0-9]${s}{0,240}@[a-z]${s}{0,13}`;const u=new RegExp(`^(?:${a}|${c})$`);const l=/^[ -~]{0,255}[!-~]$/;const d=/,|=/;function f(o){return u.test(o)}i.validateKey=f;function p(o){return l.test(o)&&!d.test(o)}i.validateValue=p},98:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.createTraceState=void 0;const a=s(325);function c(o){return new a.TraceStateImpl(o)}i.createTraceState=c},476:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.INVALID_SPAN_CONTEXT=i.INVALID_TRACEID=i.INVALID_SPANID=void 0;const a=s(475);i.INVALID_SPANID="0000000000000000";i.INVALID_TRACEID="00000000000000000000000000000000";i.INVALID_SPAN_CONTEXT={traceId:i.INVALID_TRACEID,spanId:i.INVALID_SPANID,traceFlags:a.TraceFlags.NONE}},357:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.SpanKind=void 0;var s;(function(o){o[o["INTERNAL"]=0]="INTERNAL";o[o["SERVER"]=1]="SERVER";o[o["CLIENT"]=2]="CLIENT";o[o["PRODUCER"]=3]="PRODUCER";o[o["CONSUMER"]=4]="CONSUMER"})(s=i.SpanKind||(i.SpanKind={}))},139:(o,i,s)=>{Object.defineProperty(i,"__esModule",{value:true});i.wrapSpanContext=i.isSpanContextValid=i.isValidSpanId=i.isValidTraceId=void 0;const a=s(476);const c=s(403);const u=/^([0-9a-f]{32})$/i;const l=/^[0-9a-f]{16}$/i;function d(o){return u.test(o)&&o!==a.INVALID_TRACEID}i.isValidTraceId=d;function f(o){return l.test(o)&&o!==a.INVALID_SPANID}i.isValidSpanId=f;function p(o){return d(o.traceId)&&f(o.spanId)}i.isSpanContextValid=p;function h(o){return new c.NonRecordingSpan(o)}i.wrapSpanContext=h},847:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.SpanStatusCode=void 0;var s;(function(o){o[o["UNSET"]=0]="UNSET";o[o["OK"]=1]="OK";o[o["ERROR"]=2]="ERROR"})(s=i.SpanStatusCode||(i.SpanStatusCode={}))},475:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.TraceFlags=void 0;var s;(function(o){o[o["NONE"]=0]="NONE";o[o["SAMPLED"]=1]="SAMPLED"})(s=i.TraceFlags||(i.TraceFlags={}))},521:(o,i)=>{Object.defineProperty(i,"__esModule",{value:true});i.VERSION=void 0;i.VERSION="1.6.0"}};var c={};function u(o){var s=c[o];if(s!==undefined){return s.exports}var a=c[o]={exports:{}};var l=true;try{i[o].call(a.exports,a,a.exports,u);l=false}finally{if(l)delete c[o]}return a.exports}if(typeof u!=="undefined")u.ab=a+"/";var l={};(()=>{var o=l;Object.defineProperty(o,"__esModule",{value:true});o.trace=o.propagation=o.metrics=o.diag=o.context=o.INVALID_SPAN_CONTEXT=o.INVALID_TRACEID=o.INVALID_SPANID=o.isValidSpanId=o.isValidTraceId=o.isSpanContextValid=o.createTraceState=o.TraceFlags=o.SpanStatusCode=o.SpanKind=o.SamplingDecision=o.ProxyTracerProvider=o.ProxyTracer=o.defaultTextMapSetter=o.defaultTextMapGetter=o.ValueType=o.createNoopMeter=o.DiagLogLevel=o.DiagConsoleLogger=o.ROOT_CONTEXT=o.createContextKey=o.baggageEntryMetadataFromString=void 0;var i=u(369);Object.defineProperty(o,"baggageEntryMetadataFromString",{enumerable:true,get:function(){return i.baggageEntryMetadataFromString}});var s=u(780);Object.defineProperty(o,"createContextKey",{enumerable:true,get:function(){return s.createContextKey}});Object.defineProperty(o,"ROOT_CONTEXT",{enumerable:true,get:function(){return s.ROOT_CONTEXT}});var a=u(972);Object.defineProperty(o,"DiagConsoleLogger",{enumerable:true,get:function(){return a.DiagConsoleLogger}});var c=u(957);Object.defineProperty(o,"DiagLogLevel",{enumerable:true,get:function(){return c.DiagLogLevel}});var d=u(102);Object.defineProperty(o,"createNoopMeter",{enumerable:true,get:function(){return d.createNoopMeter}});var f=u(901);Object.defineProperty(o,"ValueType",{enumerable:true,get:function(){return f.ValueType}});var p=u(194);Object.defineProperty(o,"defaultTextMapGetter",{enumerable:true,get:function(){return p.defaultTextMapGetter}});Object.defineProperty(o,"defaultTextMapSetter",{enumerable:true,get:function(){return p.defaultTextMapSetter}});var h=u(125);Object.defineProperty(o,"ProxyTracer",{enumerable:true,get:function(){return h.ProxyTracer}});var g=u(846);Object.defineProperty(o,"ProxyTracerProvider",{enumerable:true,get:function(){return g.ProxyTracerProvider}});var m=u(996);Object.defineProperty(o,"SamplingDecision",{enumerable:true,get:function(){return m.SamplingDecision}});var y=u(357);Object.defineProperty(o,"SpanKind",{enumerable:true,get:function(){return y.SpanKind}});var w=u(847);Object.defineProperty(o,"SpanStatusCode",{enumerable:true,get:function(){return w.SpanStatusCode}});var b=u(475);Object.defineProperty(o,"TraceFlags",{enumerable:true,get:function(){return b.TraceFlags}});var _=u(98);Object.defineProperty(o,"createTraceState",{enumerable:true,get:function(){return _.createTraceState}});var v=u(139);Object.defineProperty(o,"isSpanContextValid",{enumerable:true,get:function(){return v.isSpanContextValid}});Object.defineProperty(o,"isValidTraceId",{enumerable:true,get:function(){return v.isValidTraceId}});Object.defineProperty(o,"isValidSpanId",{enumerable:true,get:function(){return v.isValidSpanId}});var E=u(476);Object.defineProperty(o,"INVALID_SPANID",{enumerable:true,get:function(){return E.INVALID_SPANID}});Object.defineProperty(o,"INVALID_TRACEID",{enumerable:true,get:function(){return E.INVALID_TRACEID}});Object.defineProperty(o,"INVALID_SPAN_CONTEXT",{enumerable:true,get:function(){return E.INVALID_SPAN_CONTEXT}});const S=u(67);Object.defineProperty(o,"context",{enumerable:true,get:function(){return S.context}});const k=u(506);Object.defineProperty(o,"diag",{enumerable:true,get:function(){return k.diag}});const x=u(886);Object.defineProperty(o,"metrics",{enumerable:true,get:function(){return x.metrics}});const R=u(939);Object.defineProperty(o,"propagation",{enumerable:true,get:function(){return R.propagation}});const P=u(845);Object.defineProperty(o,"trace",{enumerable:true,get:function(){return P.trace}});o["default"]={context:S.context,diag:k.diag,metrics:x.metrics,propagation:R.propagation,trace:P.trace}})();o.exports=l})()}},o=>{var i=i=>o(o.s=i);var s=i(125);(_ENTRIES=typeof _ENTRIES==="undefined"?{}:_ENTRIES).middleware_middleware=s}]);
//# sourceMappingURL=middleware.js.map