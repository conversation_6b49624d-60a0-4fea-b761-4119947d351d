"use strict";(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[341],{303:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"AmpStateContext",{enumerable:true,get:function(){return s}});const r=n(4252);const o=r._(n(4232));const s=o.default.createContext({});if(false){}},3776:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return l}});const r=n(4232);const o="object"==="undefined";const s=o?()=>{}:r.useLayoutEffect;const i=o?()=>{}:r.useEffect;function l(e){const{headManager:t,reduceComponentsToState:n}=e;function l(){if(t&&t.mountedInstances){const o=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(o,e))}}if(o){var a;t==null?void 0:(a=t.mountedInstances)==null?void 0:a.add(e.children);l()}s(()=>{var n;t==null?void 0:(n=t.mountedInstances)==null?void 0:n.add(e.children);return()=>{var n;t==null?void 0:(n=t.mountedInstances)==null?void 0:n.delete(e.children)}});s(()=>{if(t){t._pendingUpdate=l}return()=>{if(t){t._pendingUpdate=l}}});i(()=>{if(t&&t._pendingUpdate){t._pendingUpdate();t._pendingUpdate=null}return()=>{if(t&&t._pendingUpdate){t._pendingUpdate();t._pendingUpdate=null}}});return null}},5679:(e,t,n)=>{var r=n(5364);Object.defineProperty(t,"__esModule",{value:true});0&&0;function o(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}o(t,{default:function(){return x},defaultHead:function(){return h}});const s=n(4252);const i=n(8365);const l=n(7876);const a=i._(n(4232));const d=s._(n(3776));const u=n(303);const c=n(8831);const f=n(6807);const p=n(6079);function h(e){if(e===void 0)e=false;const t=[(0,l.jsx)("meta",{charSet:"utf-8"},"charset")];if(!e){t.push((0,l.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport"))}return t}function y(e,t){if(typeof t==="string"||typeof t==="number"){return e}if(t.type===a.default.Fragment){return e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>{if(typeof t==="string"||typeof t==="number"){return e}return e.concat(t)},[]))}return e.concat(t)}const m=["name","httpEquiv","charSet","itemProp"];function g(){const e=new Set;const t=new Set;const n=new Set;const r={};return o=>{let s=true;let i=false;if(o.key&&typeof o.key!=="number"&&o.key.indexOf("$")>0){i=true;const t=o.key.slice(o.key.indexOf("$")+1);if(e.has(t)){s=false}else{e.add(t)}}switch(o.type){case"title":case"base":if(t.has(o.type)){s=false}else{t.add(o.type)}break;case"meta":for(let e=0,t=m.length;e<t;e++){const t=m[e];if(!o.props.hasOwnProperty(t))continue;if(t==="charSet"){if(n.has(t)){s=false}else{n.add(t)}}else{const e=o.props[t];const n=r[t]||new Set;if((t!=="name"||!i)&&n.has(e)){s=false}else{n.add(e);r[t]=n}}}break}return s}}function b(e,t){const{inAmpMode:n}=t;return e.reduce(y,[]).reverse().concat(h(n).reverse()).filter(g()).reverse().map((e,t)=>{const o=e.key||t;if(true&&r.env.__NEXT_OPTIMIZE_FONTS&&!n){if(e.type==="link"&&e.props["href"]&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props["href"].startsWith(t))){const t={...e.props||{}};t["data-href"]=t["href"];t["href"]=undefined;t["data-optimized-fonts"]=true;return a.default.cloneElement(e,t)}}if(false){}return a.default.cloneElement(e,{key:o})})}function _(e){let{children:t}=e;const n=(0,a.useContext)(u.AmpStateContext);const r=(0,a.useContext)(c.HeadManagerContext);return(0,l.jsx)(d.default,{reduceComponentsToState:b,headManager:r,inAmpMode:(0,f.isInAmpMode)(n),children:t})}const x=_;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},6079:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"warnOnce",{enumerable:true,get:function(){return n}});let n=e=>{};if(false){}},6807:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"isInAmpMode",{enumerable:true,get:function(){return n}});function n(e){let{ampFirst:t=false,hybrid:n=false,hasQuery:r=false}=e===void 0?{}:e;return t||n&&r}},9341:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return u}});const r=n(4252);const o=n(7876);const s=r._(n(4232));const i=r._(n(5679));const l={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function a(e){let{req:t,res:n,err:r}=e;const o=n&&n.statusCode?n.statusCode:r?r.statusCode:404;let s;if(true){s=window.location.hostname}else{}return{statusCode:o,hostname:s}}const d={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class u extends s.default.Component{render(){const{statusCode:e,withDarkMode:t=true}=this.props;const n=this.props.title||l[e]||"An unexpected error has occurred";return(0,o.jsxs)("div",{style:d.error,children:[(0,o.jsx)(i.default,{children:(0,o.jsx)("title",{children:e?e+": "+n:"Application error: a client-side exception has occurred"})}),(0,o.jsxs)("div",{style:d.desc,children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,o.jsx)("h1",{className:"next-error-h1",style:d.h1,children:e}):null,(0,o.jsx)("div",{style:d.wrap,children:(0,o.jsxs)("h2",{style:d.h2,children:[this.props.title||e?n:(0,o.jsxs)(o.Fragment,{children:["Application error: a client-side exception has occurred"," ",Boolean(this.props.hostname)&&(0,o.jsxs)(o.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}u.displayName="ErrorPage";u.getInitialProps=a;u.origGetInitialProps=a;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}}}]);