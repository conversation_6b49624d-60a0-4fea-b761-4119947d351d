(()=>{var e={};e.id=492;e.ids=[492];e.modules={25:(e,r,t)=>{"use strict";t.d(r,{"default":()=>s});var n=t(2907);var o=t.n(n);const s=(0,n.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>s});var n=t(2907);var o=t.n(n);const s=(0,n.registerClientReference)(function(){throw new Error("Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1279:(e,r,t)=>{"use strict";t.d(r,{"default":()=>l});var n=t(687);var o=t.n(n);var s=t(3210);var i=t.n(s);var a=t(218);const d=({children:e,...r})=>{return(0,n.jsx)(a.N,{...r,children:e})};const l=d},2704:()=>{},2910:(e,r,t)=>{Promise.resolve().then(t.bind(t,363));Promise.resolve().then(t.bind(t,25));Promise.resolve().then(t.bind(t,2175))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23));Promise.resolve().then(t.t.bind(t,7924,23));Promise.resolve().then(t.t.bind(t,5656,23));Promise.resolve().then(t.t.bind(t,99,23));Promise.resolve().then(t.t.bind(t,8243,23));Promise.resolve().then(t.t.bind(t,8827,23));Promise.resolve().then(t.t.bind(t,2763,23));Promise.resolve().then(t.t.bind(t,7173,23))},4593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>a});var n=t(687);var o=t.n(n);var s=t(218);var i=t(2581);const a=({...e})=>{const{theme:r="system"}=(0,s.D)();return(0,n.jsx)(i.l$,{theme:r,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},5511:e=>{"use strict";e.exports=require("crypto")},5958:(e,r,t)=>{Promise.resolve().then(t.bind(t,4593));Promise.resolve().then(t.bind(t,1279));Promise.resolve().then(t.bind(t,9208))},6749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23));Promise.resolve().then(t.t.bind(t,6042,23));Promise.resolve().then(t.t.bind(t,8170,23));Promise.resolve().then(t.t.bind(t,9477,23));Promise.resolve().then(t.t.bind(t,9345,23));Promise.resolve().then(t.t.bind(t,2089,23));Promise.resolve().then(t.t.bind(t,6577,23));Promise.resolve().then(t.t.bind(t,1307,23))},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>i,j2:()=>l});var n=t(9859);var o=t(3560);var s=t(6056);const{handlers:i,signIn:a,signOut:d,auth:l}=(0,n.Ay)({providers:[o.A,s.A],secret:process.env.AUTH_SECRET})},8014:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>f,metadata:()=>h});var n=t(7413);var o=t.n(n);var s=t(363);var i=t(6649);var a=t.n(i);var d=t(5843);var l=t.n(d);var v=t(2704);var c=t.n(v);var p=t(25);var u=t(2175);var m=t(6814);const h={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}};const b=async({children:e})=>{const r=await (0,m.j2)();return(0,n.jsx)("html",{lang:"en",suppressHydrationWarning:true,children:(0,n.jsx)(u.SessionProvider,{session:r,children:(0,n.jsxs)("body",{className:`${a().className} ${l().variable} antialiased`,children:[(0,n.jsx)(p["default"],{attribute:"class",defaultTheme:"light",children:e}),(0,n.jsx)(s.Toaster,{})]})})})};const f=b},8549:(e,r,t)=>{"use strict";t.r(r);t.d(r,{GlobalError:()=>a.a,__next_app__:()=>g,pages:()=>f,routeModule:()=>w,tree:()=>b});var n=t(5239);var o=t.n(n);var s=t(8088);var i=t(8170);var a=t.n(i);var d=t(893);var l=t.n(d);var v={};for(const e in d)if(["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)<0)v[e]=()=>d[e];t.d(r,v);const c=()=>Promise.resolve().then(t.t.bind(t,7398,23));const p=()=>Promise.resolve().then(t.bind(t,8014));const u=()=>Promise.resolve().then(t.t.bind(t,7398,23));const m=()=>Promise.resolve().then(t.t.bind(t,9999,23));const h=()=>Promise.resolve().then(t.t.bind(t,5284,23));const b={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[c,"next/dist/client/components/not-found-error"]}]},{}]},{"layout":[p,"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[u,"next/dist/client/components/not-found-error"],"forbidden":[m,"next/dist/client/components/forbidden-error"],"unauthorized":[h,"next/dist/client/components/unauthorized-error"]}]}.children;const f=[];const x=t;const P=()=>Promise.resolve();const g={require:x,loadChunk:P};const w=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e);var n=r.X(0,[97,423],()=>t(8549));module.exports=n})();