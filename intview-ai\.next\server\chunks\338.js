exports.id=338;exports.ids=[338];exports.modules={25:(e,r,t)=>{"use strict";t.d(r,{"default":()=>n});var s=t(2907);var a=t.n(s);const n=(0,s.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>n});var s=t(2907);var a=t.n(s);const n=(0,s.registerClientReference)(function(){throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},1279:(e,r,t)=>{"use strict";t.d(r,{"default":()=>c});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(218);const l=({children:e,...r})=>{return(0,s.jsx)(i.N,{...r,children:e})};const c=l},2704:()=>{},2910:(e,r,t)=>{Promise.resolve().then(t.bind(t,363));Promise.resolve().then(t.bind(t,25));Promise.resolve().then(t.bind(t,2175))},2925:(e,r,t)=>{"use strict";t.d(r,{lV:()=>d,MJ:()=>g,zB:()=>u,eI:()=>p,lR:()=>x,C5:()=>b});var s=t(687);var a=t(3210);var n=t(1391);var o=t(7605);var i=t(6241);var l=t(9467);function c({className:e,...r}){return(0,s.jsx)(l.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})};const d=o.Op;const m=a.createContext({});const u=({...e})=>{return(0,s.jsx)(m.Provider,{value:{name:e.name},children:(0,s.jsx)(o.xI,{...e})})};const v=()=>{const e=a.useContext(m);const r=a.useContext(h);const{getFieldState:t}=(0,o.xW)();const s=(0,o.lN)({name:e.name});const n=t(e.name,s);if(!e){throw new Error("useFormField should be used within <FormField>")}const{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}};const h=a.createContext({});function p({className:e,...r}){const t=a.useId();return(0,s.jsx)(h.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",e),...r})})}function x({className:e,...r}){const{error:t,formItemId:a}=v();return(0,s.jsx)(c,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function g({...e}){const{error:r,formItemId:t,formDescriptionId:a,formMessageId:o}=v();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:t,"aria-describedby":!r?`${a}`:`${a} ${o}`,"aria-invalid":!!r,...e})}function f({className:e,...r}){const{formDescriptionId:t}=v();return _jsx("p",{"data-slot":"form-description",id:t,className:cn("text-muted-foreground text-sm",e),...r})}function b({className:e,...r}){const{error:t,formMessageId:a}=v();const n=t?String(t?.message??""):r.children;if(!n)return null;return(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,i.cn)("text-sm !text-red-600",e),...r,children:n})}},2977:(e,r,t)=>{"use strict";t.d(r,{A:()=>f});var s=t(687);var a=t(3210);var n=t(3442);var o=t(7605);var i=t(4934);var l=t(2925);var c=t(8988);var d=t(5814);var m=t.n(d);var u=t(6189);var v=t(2581);const h={HOME:"/",SIGN_IN:"/sign-in",SIGN_UP:"/sign-up"};const p=h;var x=t(474);const g=({schema:e,defaultValues:r,formType:t,heading:d,placeholderValues:h})=>{const g=(0,u.useRouter)();const f=(0,o.mN)({resolver:(0,n.u)(e),defaultValues:r,mode:"onChange"});const[b,w]=(0,a.useState)(null);const[P,j]=(0,a.useState)(false);const[N,y]=(0,a.useState)(false);const[I,S]=(0,a.useState)(false);const k=async e=>{S(true);try{await new Promise(e=>setTimeout(e,1e3));const e=t==="SIGN_IN"?"Successfully signed in! Welcome back.":"Account created successfully! Welcome to AI Interview.";v.oR.success(e);setTimeout(()=>{g.push(p.HOME)},1e3)}catch(r){console.error("Authentication error:",r);const e=t==="SIGN_IN"?"Sign in failed. Please check your credentials.":"Account creation failed. Please try again.";v.oR.error(e)}finally{S(false)}};let A="Continue";if(t==="SIGN_IN")A="Sign In";else if(t==="SIGN_UP")A="Create an Account";else if(t==="RESET_PASSWORD")A="Reset Password";return(0,s.jsx)(l.lV,{...f,children:(0,s.jsxs)("form",{onSubmit:f.handleSubmit(k),className:"space-y-4 pt-5 px-0 w-full",children:[(0,s.jsx)("h1",{className:"text-[34px] leading-[41px] font-semibold font-poppins text-gray-900 mb-8",children:d}),Object.keys(r).map(e=>(0,s.jsx)(l.zB,{control:f.control,name:e,render:({field:e})=>{const r=f.formState.errors[e.name];const t=f.getFieldState(e.name).isTouched;const a=b===e.name;const n=t&&!r;const o=a?" ":n?"":"";return(0,s.jsxs)(l.eI,{className:"flex w-full flex-col gap-3.5",children:[(0,s.jsx)(l.lR,{className:"paragraph-medium text-dark400_light700",children:e.name==="email"?"Email Address":e.name.charAt(0).toUpperCase()+e.name.slice(1)}),(0,s.jsx)(l.MJ,{children:(0,s.jsxs)("div",{className:"relative w-full",children:[(0,s.jsx)(c.p,{type:e.name.toLowerCase().includes("password")&&!N?"password":"text",...e,onFocus:()=>w(e.name),onBlur:()=>w(null),className:`paragraph-regular background-light900_dark300 text-dark300_light700 min-h-12 rounded-1.5 border focus:outline-none w-full ${o}`,placeholder:h[e.name]||""}),e.name.toLowerCase().includes("password")&&(0,s.jsx)("button",{type:"button",onClick:()=>y(!N),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,s.jsx)(x["default"],{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,s.jsx)(l.C5,{})]})}},e)),t==="SIGN_IN"&&(0,s.jsx)("div",{className:"flex justify-end mt-5",children:(0,s.jsx)(m(),{href:"/sign-in/Forgotpassword",className:"font-medium underline text-[#7B61FF] mb-14",children:"Forgot Password?"})}),t==="SIGN_UP"&&(0,s.jsxs)("div",{className:"flex items-start gap-3 text-sm",children:[(0,s.jsx)("input",{type:"checkbox",id:"terms",className:"mt-1",onChange:e=>j(e.target.checked)}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-gray-700",children:["I agree to all the"," ",(0,s.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Terms"})," of service and"," ",(0,s.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Privacy"})," ",(0,s.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Policies"})]})]}),(0,s.jsx)("div",{className:"flex justify-center w-full",children:(0,s.jsx)(i.$,{className:"primary-button paragraph-medium w-full max-w-[370px] min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900 text-white hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed",disabled:I||f.formState.isSubmitting||!f.formState.isValid||t==="SIGN_UP"&&!P,children:I||f.formState.isSubmitting?t==="SIGN_IN"?"Signing In...":t==="SIGN_UP"?"Creating Account...":"Processing...":A})}),t==="SIGN_IN"?(0,s.jsxs)("p",{className:"text-center mb-14",children:["Don't have an account? ",(0,s.jsx)(m(),{href:p.SIGN_UP,className:" font-semibold underline text-[#7B61FF]",children:"Sign Up"})]}):(0,s.jsxs)("p",{className:"text-center",children:["Already have an account?"," ",(0,s.jsx)(m(),{href:p.SIGN_IN,className:"underline text-[#6938EF] font-medium ",children:"Login"})]}),(0,s.jsxs)("div",{className:`flex items-center justify-around flex-col gap-2 sm:flex-row sm:gap-0${t==="SIGN_IN"?"mt-16":"mt-16"}`,children:[(0,s.jsx)("span",{className:"text-md text-[#000000]",children:"Or continue with"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Google",children:(0,s.jsx)(x["default"],{src:"/icons/google.svg",alt:"Google",width:32,height:32})}),(0,s.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Facebook",children:(0,s.jsx)(x["default"],{src:"/icons/facebook.svg",alt:"Facebook",width:32,height:32})}),(0,s.jsx)("button",{type:"button",className:"border rounded-full  transition hover:bg-gray-700 hover:cursor-pointer","aria-label":"Continue with Apple",children:(0,s.jsx)(x["default"],{src:"/icons/apple.svg",alt:"Apple",width:32,height:32})})]})]})]})})};const f=g},4013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23));Promise.resolve().then(t.t.bind(t,7924,23));Promise.resolve().then(t.t.bind(t,5656,23));Promise.resolve().then(t.t.bind(t,99,23));Promise.resolve().then(t.t.bind(t,8243,23));Promise.resolve().then(t.t.bind(t,8827,23));Promise.resolve().then(t.t.bind(t,2763,23));Promise.resolve().then(t.t.bind(t,7173,23))},4593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>i});var s=t(687);var a=t.n(s);var n=t(218);var o=t(2581);const i=({...e})=>{const{theme:r="system"}=(0,n.D)();return(0,s.jsx)(o.l$,{theme:r,className:"toaster group",position:"top-right",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},4934:(e,r,t)=>{"use strict";t.d(r,{$:()=>m});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(1391);var l=t(4224);var c=t(6241);const d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function m({className:e,variant:r,size:t,asChild:a=false,...n}){const o=a?i.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,c.cn)(d({variant:r,size:t,className:e})),...n})}},5958:(e,r,t)=>{Promise.resolve().then(t.bind(t,4593));Promise.resolve().then(t.bind(t,1279));Promise.resolve().then(t.bind(t,9208))},6241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(9384);var a=t(2348);function n(...e){return(0,a.QP)((0,s.$)(e))}},6487:()=>{},6749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23));Promise.resolve().then(t.t.bind(t,6042,23));Promise.resolve().then(t.t.bind(t,8170,23));Promise.resolve().then(t.t.bind(t,9477,23));Promise.resolve().then(t.t.bind(t,9345,23));Promise.resolve().then(t.t.bind(t,2089,23));Promise.resolve().then(t.t.bind(t,6577,23));Promise.resolve().then(t.t.bind(t,1307,23))},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>o,j2:()=>c});var s=t(9859);var a=t(3560);var n=t(6056);const{handlers:o,signIn:i,signOut:l,auth:c}=(0,s.Ay)({providers:[a.A,n.A],secret:process.env.AUTH_SECRET})},7470:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>o});var s=t(7413);var a=t.n(s);const n=({children:e})=>{return(0,s.jsx)("main",{children:e})};const o=n},8014:(e,r,t)=>{"use strict";t.r(r);t.d(r,{"default":()=>g,metadata:()=>p});var s=t(7413);var a=t.n(s);var n=t(363);var o=t(6649);var i=t.n(o);var l=t(5843);var c=t.n(l);var d=t(2704);var m=t.n(d);var u=t(25);var v=t(2175);var h=t(6814);const p={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}};const x=async({children:e})=>{const r=await (0,h.j2)();return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:true,children:(0,s.jsx)(v.SessionProvider,{session:r,children:(0,s.jsxs)("body",{className:`${i().className} ${c().variable} antialiased`,children:[(0,s.jsx)(u["default"],{attribute:"class",defaultTheme:"light",children:e}),(0,s.jsx)(n.Toaster,{})]})})})};const g=x},8335:()=>{},8988:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(687);var a=t.n(s);var n=t(3210);var o=t.n(n);var i=t(6241);function l({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},9360:(e,r,t)=>{"use strict";t.d(r,{Ih:()=>o,Il:()=>a,mJ:()=>n,pi:()=>l,zL:()=>i});var s=t(7566);const a=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const n=s.Ik({confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:s.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const o=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})});const i=s.Ik({newPassword:s.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:s.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]});const l=s.Ik({otp1:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:s.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})}};