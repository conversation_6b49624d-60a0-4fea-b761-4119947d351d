(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[6],{1867:(e,l,s)=>{"use strict";s.r(l);s.d(l,{"default":()=>h});var a=s(5155);var r=s(2115);var i=s(6766);var d=s(9858);var c=s(3582);const t=()=>{const e={email:"Enter your email address",password:"Enter your password"};return(0,a.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)(i["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,a.jsx)(i["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,a.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,a.jsx)(d.A,{formType:"SIGN_IN",schema:c.Il,defaultValues:{email:"",password:""},onSubmit:e=>Promise.resolve({success:true,data:e}),heading:"Sign In - For Applicants",placeholderValues:e})})})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,a.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsx)(d.A,{formType:"SIGN_IN",schema:c.Il,defaultValues:{email:"",password:""},onSubmit:e=>Promise.resolve({success:true,data:e}),heading:"Sign In - For Applicants",placeholderValues:e})})})})})]})};const h=t},9559:(e,l,s)=>{Promise.resolve().then(s.bind(s,1867))}},e=>{var l=l=>e(e.s=l);e.O(0,[766,831,903,671,874,421,441,684,358],()=>l(9559));var s=e.O();_N_E=s}]);