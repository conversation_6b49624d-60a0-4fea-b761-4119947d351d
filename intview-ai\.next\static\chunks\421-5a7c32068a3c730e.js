"use strict";(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[421],{3582:(e,r,a)=>{a.d(r,{Ih:()=>o,Il:()=>s,mJ:()=>n,pi:()=>c,zL:()=>i});var t=a(8309);const s=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const n=t.Ik({confirmPassword:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),name:t.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character")});const o=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."})});const i=t.Ik({newPassword:t.Yj().min(6,{message:"Password must be at least 6 characters long"}).regex(/[A-Z]/,"Password must contain at least one uppercase character").regex(/[a-z]/,"Password must contain at least one lowercase character").regex(/[0-9]/,"Password must contain at least one number").regex(/[^a-zA-Z0-9]/,"Password must contain at least one special character"),confirmPassword:t.Yj().min(6,{error:"Password must be at least 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Password do not match",path:["confirmPassword"]});const c=t.Ik({otp1:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp2:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp3:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed"),otp4:t.Yj().min(1,"Required").regex(/^\d$/,"Only digits allowed")})},3999:(e,r,a)=>{a.d(r,{cn:()=>n});var t=a(2596);var s=a(9688);function n(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++){r[a]=arguments[a]}return(0,s.QP)((0,t.$)(r))}},7168:(e,r,a)=>{a.d(r,{$:()=>l});var t=a(5155);var s=a(2115);var n=a(4624);var o=a(2085);var i=a(3999);const c=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:a,size:s,asChild:o=false,...l}=e;const d=o?n.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,i.cn)(c({variant:a,size:s,className:r})),...l})}},9540:(e,r,a)=>{a.d(r,{lV:()=>d,MJ:()=>f,zB:()=>m,eI:()=>h,lR:()=>p,C5:()=>b});var t=a(5155);var s=a(2115);var n=a(4624);var o=a(2177);var i=a(3999);var c=a(7073);function l(e){let{className:r,...a}=e;return(0,t.jsx)(c.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...a})};const d=o.Op;const u=s.createContext({});const m=e=>{let{...r}=e;return(0,t.jsx)(u.Provider,{value:{name:r.name},children:(0,t.jsx)(o.xI,{...r})})};const g=()=>{const e=s.useContext(u);const r=s.useContext(x);const{getFieldState:a}=(0,o.xW)();const t=(0,o.lN)({name:e.name});const n=a(e.name,t);if(!e){throw new Error("useFormField should be used within <FormField>")}const{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}};const x=s.createContext({});function h(e){let{className:r,...a}=e;const n=s.useId();return(0,t.jsx)(x.Provider,{value:{id:n},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-3 ",r),...a})})}function p(e){let{className:r,...a}=e;const{error:s,formItemId:n}=g();return(0,t.jsx)(l,{"data-slot":"form-label","data-error":!!s,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...a})}function f(e){let{...r}=e;const{error:a,formItemId:s,formDescriptionId:o,formMessageId:i}=g();return(0,t.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":!a?"".concat(o):"".concat(o," ").concat(i),"aria-invalid":!!a,...r})}function v(e){let{className:r,...a}=e;const{formDescriptionId:t}=g();return _jsx("p",{"data-slot":"form-description",id:t,className:cn("text-muted-foreground text-sm",r),...a})}function b(e){let{className:r,...a}=e;const{error:s,formMessageId:n}=g();var o;const c=s?String((o=s===null||s===void 0?void 0:s.message)!==null&&o!==void 0?o:""):a.children;if(!c)return null;return(0,t.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-sm !text-red-600",r),...a,children:c})}},9852:(e,r,a)=>{a.d(r,{p:()=>o});var t=a(5155);var s=a(2115);var n=a(3999);function o(e){let{className:r,type:a,...s}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...s})}},9858:(e,r,a)=>{a.d(r,{A:()=>v});var t=a(5155);var s=a(2115);var n=a(221);var o=a(2177);var i=a(7168);var c=a(9540);var l=a(9852);var d=a(6874);var u=a.n(d);var m=a(5695);var g=a(6671);const x={HOME:"/",SIGN_IN:"/sign-in",SIGN_UP:"/sign-up"};const h=x;var p=a(6766);const f=e=>{let{schema:r,defaultValues:a,formType:d,heading:x,placeholderValues:f}=e;const v=(0,m.useRouter)();const b=(0,o.mN)({resolver:(0,n.u)(r),defaultValues:a,mode:"onChange"});const[w,N]=(0,s.useState)(null);const[j,y]=(0,s.useState)(false);const[P,I]=(0,s.useState)(false);const[S,k]=(0,s.useState)(false);const _=async e=>{k(true);try{await new Promise(e=>setTimeout(e,1e3));const e=d==="SIGN_IN"?"Successfully signed in! Welcome back.":"Account created successfully! Welcome to AI Interview.";g.oR.success(e);setTimeout(()=>{v.push(h.HOME)},1e3)}catch(r){console.error("Authentication error:",r);const e=d==="SIGN_IN"?"Sign in failed. Please check your credentials.":"Account creation failed. Please try again.";g.oR.error(e)}finally{k(false)}};let A="Continue";if(d==="SIGN_IN")A="Sign In";else if(d==="SIGN_UP")A="Create an Account";else if(d==="RESET_PASSWORD")A="Reset Password";return(0,t.jsx)(c.lV,{...b,children:(0,t.jsxs)("form",{onSubmit:b.handleSubmit(_),className:"space-y-4 pt-5 px-0 w-full",children:[(0,t.jsx)("h1",{className:"text-[34px] leading-[41px] font-semibold font-poppins text-gray-900 mb-8",children:x}),Object.keys(a).map(e=>(0,t.jsx)(c.zB,{control:b.control,name:e,render:e=>{let{field:r}=e;const a=b.formState.errors[r.name];const s=b.getFieldState(r.name).isTouched;const n=w===r.name;const o=s&&!a;const i=n?" ":o?"":"";return(0,t.jsxs)(c.eI,{className:"flex w-full flex-col gap-3.5",children:[(0,t.jsx)(c.lR,{className:"paragraph-medium text-dark400_light700",children:r.name==="email"?"Email Address":r.name.charAt(0).toUpperCase()+r.name.slice(1)}),(0,t.jsx)(c.MJ,{children:(0,t.jsxs)("div",{className:"relative w-full",children:[(0,t.jsx)(l.p,{type:r.name.toLowerCase().includes("password")&&!P?"password":"text",...r,onFocus:()=>N(r.name),onBlur:()=>N(null),className:"paragraph-regular background-light900_dark300 text-dark300_light700 min-h-12 rounded-1.5 border focus:outline-none w-full ".concat(i),placeholder:f[r.name]||""}),r.name.toLowerCase().includes("password")&&(0,t.jsx)("button",{type:"button",onClick:()=>I(!P),className:"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer",children:(0,t.jsx)(p["default"],{src:"/images/eye.png",alt:"Toggle Password Visibility",width:20,height:20})})]})}),(0,t.jsx)(c.C5,{})]})}},e)),d==="SIGN_IN"&&(0,t.jsx)("div",{className:"flex justify-end mt-5",children:(0,t.jsx)(u(),{href:"/sign-in/Forgotpassword",className:"font-medium underline text-[#7B61FF] mb-14",children:"Forgot Password?"})}),d==="SIGN_UP"&&(0,t.jsxs)("div",{className:"flex items-start gap-3 text-sm",children:[(0,t.jsx)("input",{type:"checkbox",id:"terms",className:"mt-1",onChange:e=>y(e.target.checked)}),(0,t.jsxs)("label",{htmlFor:"terms",className:"text-gray-700",children:["I agree to all the"," ",(0,t.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Terms"})," of service and"," ",(0,t.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Privacy"})," ",(0,t.jsx)("span",{className:"text-[#6938EF] cursor-pointer",children:"Policies"})]})]}),(0,t.jsx)("div",{className:"flex justify-center w-full",children:(0,t.jsx)(i.$,{className:"primary-button paragraph-medium w-full max-w-[370px] min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900 text-white hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed",disabled:S||b.formState.isSubmitting||!b.formState.isValid||d==="SIGN_UP"&&!j,children:S||b.formState.isSubmitting?d==="SIGN_IN"?"Signing In...":d==="SIGN_UP"?"Creating Account...":"Processing...":A})}),d==="SIGN_IN"?(0,t.jsxs)("p",{className:"text-center mb-14",children:["Don't have an account? ",(0,t.jsx)(u(),{href:h.SIGN_UP,className:" font-semibold underline text-[#7B61FF]",children:"Sign Up"})]}):(0,t.jsxs)("p",{className:"text-center",children:["Already have an account?"," ",(0,t.jsx)(u(),{href:h.SIGN_IN,className:"underline text-[#6938EF] font-medium ",children:"Login"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-around flex-col gap-2 sm:flex-row sm:gap-0".concat(d==="SIGN_IN"?"mt-16":"mt-16"),children:[(0,t.jsx)("span",{className:"text-md text-[#000000]",children:"Or continue with"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Google",children:(0,t.jsx)(p["default"],{src:"/icons/google.svg",alt:"Google",width:32,height:32})}),(0,t.jsx)("button",{type:"button",className:"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition","aria-label":"Continue with Facebook",children:(0,t.jsx)(p["default"],{src:"/icons/facebook.svg",alt:"Facebook",width:32,height:32})}),(0,t.jsx)("button",{type:"button",className:"border rounded-full  transition hover:bg-gray-700 hover:cursor-pointer","aria-label":"Continue with Apple",children:(0,t.jsx)(p["default"],{src:"/icons/apple.svg",alt:"Apple",width:32,height:32})})]})]})]})})};const v=f}}]);