{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "DtJYS3Vl-qLqvrGesMhQR", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oiPCILC9Y9ye88tkN4f+XFo3xxlWQOZ7iXBzQ8fSiPw=", "__NEXT_PREVIEW_MODE_ID": "d1c1d682daa9c38fb1516a74b0807fe9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b79068ca5a9b61b5dadb20fe1bf7b92d822ec09186f90918992f8c19520b0c5b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e4ded313a1bbbae46e4ecac2bd490fa6b8da9374d723bc0c77c90f7a9635e480"}}}, "functions": {}, "sortedMiddleware": ["/"]}