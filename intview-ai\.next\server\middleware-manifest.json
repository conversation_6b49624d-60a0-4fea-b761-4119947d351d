{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "CThxngnRZhmvLEGoaRRVb", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oiPCILC9Y9ye88tkN4f+XFo3xxlWQOZ7iXBzQ8fSiPw=", "__NEXT_PREVIEW_MODE_ID": "ac33a7720d0b9cf1363462c636c343f5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e5e615363104432f6bcd85be45bc10ac8dc94620f5513ee55b7cb455a5ca042", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4385bea258b61f0df909f27f31b124573e2a240ec51018c14f7c592c945814da"}}}, "functions": {}, "sortedMiddleware": ["/"]}