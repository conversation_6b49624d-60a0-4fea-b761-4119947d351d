"use strict";(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[766],{901:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"RouterContext",{enumerable:true,get:function(){return i}});const r=n(8229);const o=r._(n(2115));const i=o.default.createContext(null);if(false){}},1193:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return o}});const n=75;function r(e){let{config:t,src:r,width:o,quality:i}=e;var s;if(false){}const u=i||((s=t.qualities)==null?void 0:s.reduce((e,t)=>Math.abs(t-n)<Math.abs(e-n)?t:e))||n;return t.path+"?url="+encodeURIComponent(r)+"&w="+o+"&q="+u+(r.startsWith("/_next/static/media/")&&false?0:"")}r.__next_img_default=true;const o=r},1469:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function r(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}r(t,{default:function(){return l},getImageProps:function(){return a}});const o=n(8229);const i=n(8883);const s=n(3063);const u=o._(n(1193));function a(e){const{props:t}=(0,i.getImgProps)(e,{defaultLoader:u.default,imgConf:{"deviceSizes":[640,750,828,1080,1200,1920,2048,3840],"imageSizes":[16,32,48,64,96,128,256,384],"path":"/_next/image","loader":"default","dangerouslyAllowSVG":false,"unoptimized":false}});for(const[e,n]of Object.entries(t)){if(n===undefined){delete t[e]}}return{props:t}}const l=s.Image},2464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"AmpStateContext",{enumerable:true,get:function(){return i}});const r=n(8229);const o=r._(n(2115));const i=o.default.createContext({});if(false){}},3063:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"Image",{enumerable:true,get:function(){return w}});const r=n(8229);const o=n(6966);const i=n(5155);const s=o._(n(2115));const u=r._(n(7650));const a=r._(n(5564));const l=n(8883);const c=n(5840);const f=n(6752);const d=n(3230);const p=n(901);const g=r._(n(1193));const m=n(6654);const h={"deviceSizes":[640,750,828,1080,1200,1920,2048,3840],"imageSizes":[16,32,48,64,96,128,256,384],"path":"/_next/image","loader":"default","dangerouslyAllowSVG":false,"unoptimized":false};if(false){}function b(e,t,n,r,o,i,s){const u=e==null?void 0:e.src;if(!e||e["data-loaded-src"]===u){return}e["data-loaded-src"]=u;const a="decode"in e?e.decode():Promise.resolve();a.catch(()=>{}).then(()=>{if(!e.parentElement||!e.isConnected){return}if(t!=="empty"){o(true)}if(n==null?void 0:n.current){const t=new Event("load");Object.defineProperty(t,"target",{writable:false,value:e});let r=false;let o=false;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{r=true;t.preventDefault()},stopPropagation:()=>{o=true;t.stopPropagation()}})}if(r==null?void 0:r.current){r.current(e)}if(false){}})}function y(e){if(Boolean(s.use)){return{fetchPriority:e}}return{fetchpriority:e}}const _=(0,s.forwardRef)((e,t)=>{let{src:n,srcSet:r,sizes:o,height:u,width:a,decoding:l,className:c,style:f,fetchPriority:d,placeholder:p,loading:g,unoptimized:h,fill:_,onLoadRef:v,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:O,sizesInput:P,onLoad:S,onError:x,...C}=e;const E=(0,s.useCallback)(e=>{if(!e){return}if(x){e.src=e.src}if(false){}if(e.complete){b(e,p,v,w,j,h,P)}},[n,p,v,w,j,x,h,P]);const M=(0,m.useMergedRef)(t,E);return(0,i.jsx)("img",{...C,...y(d),loading:g,width:a,height:u,decoding:l,"data-nimg":_?"fill":"1",className:c,style:f,sizes:o,srcSet:r,src:n,ref:M,onLoad:e=>{const t=e.currentTarget;b(t,p,v,w,j,h,P)},onError:e=>{O(true);if(p!=="empty"){j(true)}if(x){x(e)}}})});function v(e){let{isAppRouter:t,imgAttributes:n}=e;const r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...y(n.fetchPriority)};if(t&&u.default.preload){u.default.preload(n.src,r);return null}return(0,i.jsx)(a.default,{children:(0,i.jsx)("link",{rel:"preload",href:n.srcSet?undefined:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}const w=(0,s.forwardRef)((e,t)=>{const n=(0,s.useContext)(p.RouterContext);const r=!n;const o=(0,s.useContext)(f.ImageConfigContext);const u=(0,s.useMemo)(()=>{var e;const t=h||o||c.imageConfigDefault;const n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t);const r=t.deviceSizes.sort((e,t)=>e-t);const i=(e=t.qualities)==null?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:r,qualities:i}},[o]);const{onLoad:a,onLoadingComplete:d}=e;const m=(0,s.useRef)(a);(0,s.useEffect)(()=>{m.current=a},[a]);const b=(0,s.useRef)(d);(0,s.useEffect)(()=>{b.current=d},[d]);const[y,w]=(0,s.useState)(false);const[j,O]=(0,s.useState)(false);const{props:P,meta:S}=(0,l.getImgProps)(e,{defaultLoader:g.default,imgConf:u,blurComplete:y,showAltText:j});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(_,{...P,unoptimized:S.unoptimized,placeholder:S.placeholder,fill:S.fill,onLoadRef:m,onLoadingCompleteRef:b,setBlurComplete:w,setShowAltText:O,sizesInput:e.sizes,ref:t}),S.priority?(0,i.jsx)(v,{isAppRouter:r,imgAttributes:P}):null]})});if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},5029:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"default",{enumerable:true,get:function(){return u}});const r=n(2115);const o="object"==="undefined";const i=o?()=>{}:r.useLayoutEffect;const s=o?()=>{}:r.useEffect;function u(e){const{headManager:t,reduceComponentsToState:n}=e;function u(){if(t&&t.mountedInstances){const o=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(o,e))}}if(o){var a;t==null?void 0:(a=t.mountedInstances)==null?void 0:a.add(e.children);u()}i(()=>{var n;t==null?void 0:(n=t.mountedInstances)==null?void 0:n.add(e.children);return()=>{var n;t==null?void 0:(n=t.mountedInstances)==null?void 0:n.delete(e.children)}});i(()=>{if(t){t._pendingUpdate=u}return()=>{if(t){t._pendingUpdate=u}}});s(()=>{if(t&&t._pendingUpdate){t._pendingUpdate();t._pendingUpdate=null}return()=>{if(t&&t._pendingUpdate){t._pendingUpdate();t._pendingUpdate=null}}});return null}},5100:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"getImageBlurSvg",{enumerable:true,get:function(){return n}});function n(e){let{widthInt:t,heightInt:n,blurWidth:r,blurHeight:o,blurDataURL:i,objectFit:s}=e;const u=20;const a=r?r*40:t;const l=o?o*40:n;const c=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";const f=c?"none":s==="contain"?"xMidYMid":s==="cover"?"xMidYMid slice":"none";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='"+u+"'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='"+u+"'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+f+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}},5564:(e,t,n)=>{var r=n(9509);Object.defineProperty(t,"__esModule",{value:true});0&&0;function o(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}o(t,{default:function(){return v},defaultHead:function(){return g}});const i=n(8229);const s=n(6966);const u=n(5155);const a=s._(n(2115));const l=i._(n(5029));const c=n(2464);const f=n(2830);const d=n(7544);const p=n(3230);function g(e){if(e===void 0)e=false;const t=[(0,u.jsx)("meta",{charSet:"utf-8"},"charset")];if(!e){t.push((0,u.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport"))}return t}function m(e,t){if(typeof t==="string"||typeof t==="number"){return e}if(t.type===a.default.Fragment){return e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>{if(typeof t==="string"||typeof t==="number"){return e}return e.concat(t)},[]))}return e.concat(t)}const h=["name","httpEquiv","charSet","itemProp"];function b(){const e=new Set;const t=new Set;const n=new Set;const r={};return o=>{let i=true;let s=false;if(o.key&&typeof o.key!=="number"&&o.key.indexOf("$")>0){s=true;const t=o.key.slice(o.key.indexOf("$")+1);if(e.has(t)){i=false}else{e.add(t)}}switch(o.type){case"title":case"base":if(t.has(o.type)){i=false}else{t.add(o.type)}break;case"meta":for(let e=0,t=h.length;e<t;e++){const t=h[e];if(!o.props.hasOwnProperty(t))continue;if(t==="charSet"){if(n.has(t)){i=false}else{n.add(t)}}else{const e=o.props[t];const n=r[t]||new Set;if((t!=="name"||!s)&&n.has(e)){i=false}else{n.add(e);r[t]=n}}}break}return i}}function y(e,t){const{inAmpMode:n}=t;return e.reduce(m,[]).reverse().concat(g(n).reverse()).filter(b()).reverse().map((e,t)=>{const o=e.key||t;if(true&&r.env.__NEXT_OPTIMIZE_FONTS&&!n){if(e.type==="link"&&e.props["href"]&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props["href"].startsWith(t))){const t={...e.props||{}};t["data-href"]=t["href"];t["href"]=undefined;t["data-optimized-fonts"]=true;return a.default.cloneElement(e,t)}}if(false){}return a.default.cloneElement(e,{key:o})})}function _(e){let{children:t}=e;const n=(0,a.useContext)(c.AmpStateContext);const r=(0,a.useContext)(f.HeadManagerContext);return(0,u.jsx)(l.default,{reduceComponentsToState:y,headManager:r,inAmpMode:(0,d.isInAmpMode)(n),children:t})}const v=_;if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});0&&0;function n(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:true,get:t[n]})}n(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return o}});const r=["default","imgix","cloudinary","akamai","custom"];const o={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:false,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:false,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:undefined,remotePatterns:[],qualities:undefined,unoptimized:false}},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"useMergedRef",{enumerable:true,get:function(){return o}});const r=n(2115);function o(e,t){const n=(0,r.useRef)(null);const o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(r===null){const e=n.current;if(e){n.current=null;e()}const t=o.current;if(t){o.current=null;t()}}else{if(e){n.current=i(e,r)}if(t){o.current=i(t,r)}}},[e,t])}function i(e,t){if(typeof e==="function"){const n=e(t);if(typeof n==="function"){return n}else{return()=>e(null)}}else{e.current=t;return()=>{e.current=null}}}if((typeof t.default==="function"||typeof t.default==="object"&&t.default!==null)&&typeof t.default.__esModule==="undefined"){Object.defineProperty(t.default,"__esModule",{value:true});Object.assign(t.default,t);e.exports=t.default}},6752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"ImageConfigContext",{enumerable:true,get:function(){return s}});const r=n(8229);const o=r._(n(2115));const i=n(5840);const s=o.default.createContext(i.imageConfigDefault);if(false){}},6766:(e,t,n)=>{n.d(t,{"default":()=>o.a});var r=n(1469);var o=n.n(r)},7544:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"isInAmpMode",{enumerable:true,get:function(){return n}});function n(e){let{ampFirst:t=false,hybrid:n=false,hasQuery:r=false}=e===void 0?{}:e;return t||n&&r}},8883:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:true});Object.defineProperty(t,"getImgProps",{enumerable:true,get:function(){return h}});const r=n(3230);const o=n(5100);const i=n(5840);const s=null&&["lazy","eager",undefined];const u=["-moz-initial","fill","none","scale-down",undefined];function a(e){return e.default!==undefined}function l(e){return e.src!==undefined}function c(e){return!!e&&typeof e==="object"&&(a(e)||l(e))}const f=new Map;let d;function p(e){if(typeof e==="undefined"){return e}if(typeof e==="number"){return Number.isFinite(e)?e:NaN}if(typeof e==="string"&&/^[0-9]+$/.test(e)){return parseInt(e,10)}return NaN}function g(e,t,n){let{deviceSizes:r,allSizes:o}=e;if(n){const e=/(^|\s)(1?\d?\d)vw/g;const t=[];for(let r;r=e.exec(n);r){t.push(parseInt(r[2]))}if(t.length){const e=Math.min(...t)*.01;return{widths:o.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:o,kind:"w"}}if(typeof t!=="number"){return{widths:r,kind:"w"}}const i=[...new Set([t,t*2].map(e=>o.find(t=>t>=e)||o[o.length-1]))];return{widths:i,kind:"x"}}function m(e){let{config:t,src:n,unoptimized:r,width:o,quality:i,sizes:s,loader:u}=e;if(r){return{src:n,srcSet:undefined,sizes:undefined}}const{widths:a,kind:l}=g(t,o,s);const c=a.length-1;return{sizes:!s&&l==="w"?"100vw":s,srcSet:a.map((e,r)=>u({config:t,src:n,quality:i,width:e})+" "+(l==="w"?e:r+1)+l).join(", "),src:u({config:t,src:n,quality:i,width:a[c]})}}function h(e,t){let{src:n,sizes:r,unoptimized:s=false,priority:l=false,loading:f,className:d,quality:g,width:h,height:b,fill:y=false,style:_,overrideSrc:v,onLoad:w,onLoadingComplete:j,placeholder:O="empty",blurDataURL:P,fetchPriority:S,decoding:x="async",layout:C,objectFit:E,objectPosition:M,lazyBoundary:R,lazyRoot:z,...I}=e;const{imgConf:k,showAltText:A,blurComplete:D,defaultLoader:N}=t;let T;let F=k||i.imageConfigDefault;if("allSizes"in F){T=F}else{var L;const e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t);const t=F.deviceSizes.sort((e,t)=>e-t);const n=(L=F.qualities)==null?void 0:L.sort((e,t)=>e-t);T={...F,allSizes:e,deviceSizes:t,qualities:n}}if(typeof N==="undefined"){throw Object.defineProperty(new Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:false,configurable:true})}let U=I.loader||N;delete I.loader;delete I.srcSet;const B="__next_img_default"in U;if(B){if(T.loader==="custom"){throw Object.defineProperty(new Error('Image with src "'+n+'" is missing "loader" prop.'+"\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader"),"__NEXT_ERROR_CODE",{value:"E252",enumerable:false,configurable:true})}}else{const e=U;U=t=>{const{config:n,...r}=t;return e(r)}}if(C){if(C==="fill"){y=true}const e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}};const t={responsive:"100vw",fill:"100vw"};const n=e[C];if(n){_={..._,...n}}const o=t[C];if(o&&!r){r=o}}let G="";let q=p(h);let W=p(b);let V;let X;if(c(n)){const e=a(n)?n.default:n;if(!e.src){throw Object.defineProperty(new Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:false,configurable:true})}if(!e.height||!e.width){throw Object.defineProperty(new Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:false,configurable:true})}V=e.blurWidth;X=e.blurHeight;P=P||e.blurDataURL;G=e.src;if(!y){if(!q&&!W){q=e.width;W=e.height}else if(q&&!W){const t=q/e.width;W=Math.round(e.height*t)}else if(!q&&W){const t=W/e.height;q=Math.round(e.width*t)}}}n=typeof n==="string"?n:G;let H=!l&&(f==="lazy"||typeof f==="undefined");if(!n||n.startsWith("data:")||n.startsWith("blob:")){s=true;H=false}if(T.unoptimized){s=true}if(B&&!T.dangerouslyAllowSVG&&n.split("?",1)[0].endsWith(".svg")){s=true}const $=p(g);if(false){}const J=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:M}:{},A?{}:{color:"transparent"},_);const Y=!D&&O!=="empty"?O==="blur"?'url("data:image/svg+xml;charset=utf-8,'+(0,o.getImageBlurSvg)({widthInt:q,heightInt:W,blurWidth:V,blurHeight:X,blurDataURL:P||"",objectFit:J.objectFit})+'")':'url("'+O+'")':null;const Z=!u.includes(J.objectFit)?J.objectFit:J.objectFit==="fill"?"100% 100%":"cover";let K=Y?{backgroundSize:Z,backgroundPosition:J.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{};if(false){}const Q=m({config:T,src:n,unoptimized:s,width:q,quality:$,sizes:r,loader:U});if(false){}const ee={...I,loading:H?"lazy":f,fetchPriority:S,width:q,height:W,decoding:x,className:d,style:{...J,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:v||Q.src};const et={unoptimized:s,priority:l,placeholder:O,fill:y};return{props:ee,meta:et}}}}]);