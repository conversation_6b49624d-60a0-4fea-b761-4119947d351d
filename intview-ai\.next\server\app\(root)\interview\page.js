(()=>{var e={};e.id=812;e.ids=[812];e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1079:(e,t,r)=>{"use strict";r.r(t);r.d(t,{"default":()=>a});var s=r(2907);var n=r.n(s);const a=(0,s.registerClientReference)(function(){throw new Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(root)\\\\interview\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx","default")},2379:(e,t,r)=>{Promise.resolve().then(r.bind(r,7206))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3105:e=>{var t=function(e){"use strict";var t=Object.prototype;var r=t.hasOwnProperty;var s=Object.defineProperty||function(e,t,r){e[t]=r.value};var n;var a=typeof Symbol==="function"?Symbol:{};var i=a.iterator||"@@iterator";var o=a.asyncIterator||"@@asyncIterator";var l=a.toStringTag||"@@toStringTag";function c(e,t,r){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true});return e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var a=t&&t.prototype instanceof g?t:g;var i=Object.create(a.prototype);var o=new P(n||[]);s(i,"_invoke",{value:S(e,r,o)});return i}e.wrap=d;function u(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}var h="suspendedStart";var m="suspendedYield";var x="executing";var p="completed";var f={};function g(){}function v(){}function y(){}var b={};c(b,i,function(){return this});var w=Object.getPrototypeOf;var j=w&&w(w(R([])));if(j&&j!==t&&r.call(j,i)){b=j}var N=y.prototype=g.prototype=Object.create(b);v.prototype=y;s(N,"constructor",{value:y,configurable:true});s(y,"constructor",{value:v,configurable:true});v.displayName=c(y,l,"GeneratorFunction");function k(e){["next","throw","return"].forEach(function(t){c(e,t,function(e){return this._invoke(t,e)})})}e.isGeneratorFunction=function(e){var t=typeof e==="function"&&e.constructor;return t?t===v||(t.displayName||t.name)==="GeneratorFunction":false};e.mark=function(e){if(Object.setPrototypeOf){Object.setPrototypeOf(e,y)}else{e.__proto__=y;c(e,l,"GeneratorFunction")}e.prototype=Object.create(N);return e};e.awrap=function(e){return{__await:e}};function A(e,t){function n(s,a,i,o){var l=u(e[s],e,a);if(l.type==="throw"){o(l.arg)}else{var c=l.arg;var d=c.value;if(d&&typeof d==="object"&&r.call(d,"__await")){return t.resolve(d.__await).then(function(e){n("next",e,i,o)},function(e){n("throw",e,i,o)})}return t.resolve(d).then(function(e){c.value=e;i(c)},function(e){return n("throw",e,i,o)})}}var a;function i(e,r){function s(){return new t(function(t,s){n(e,r,t,s)})}return a=a?a.then(s,s):s()}s(this,"_invoke",{value:i})}k(A.prototype);c(A.prototype,o,function(){return this});e.AsyncIterator=A;e.async=function(t,r,s,n,a){if(a===void 0)a=Promise;var i=new A(d(t,r,s,n),a);return e.isGeneratorFunction(r)?i:i.next().then(function(e){return e.done?e.value:i.next()})};function S(e,t,r){var s=h;return function n(n,a){if(s===x){throw new Error("Generator is already running")}if(s===p){if(n==="throw"){throw a}return L()}r.method=n;r.arg=a;while(true){var i=r.delegate;if(i){var o=E(i,r);if(o){if(o===f)continue;return o}}if(r.method==="next"){r.sent=r._sent=r.arg}else if(r.method==="throw"){if(s===h){s=p;throw r.arg}r.dispatchException(r.arg)}else if(r.method==="return"){r.abrupt("return",r.arg)}s=x;var l=u(e,t,r);if(l.type==="normal"){s=r.done?p:m;if(l.arg===f){continue}return{value:l.arg,done:r.done}}else if(l.type==="throw"){s=p;r.method="throw";r.arg=l.arg}}}}function E(e,t){var r=t.method;var s=e.iterator[r];if(s===n){t.delegate=null;if(r==="throw"&&e.iterator["return"]){t.method="return";t.arg=n;E(e,t);if(t.method==="throw"){return f}}if(r!=="return"){t.method="throw";t.arg=new TypeError("The iterator does not provide a '"+r+"' method")}return f}var a=u(s,e.iterator,t.arg);if(a.type==="throw"){t.method="throw";t.arg=a.arg;t.delegate=null;return f}var i=a.arg;if(!i){t.method="throw";t.arg=new TypeError("iterator result is not an object");t.delegate=null;return f}if(i.done){t[e.resultName]=i.value;t.next=e.nextLoc;if(t.method!=="return"){t.method="next";t.arg=n}}else{return i}t.delegate=null;return f}k(N);c(N,l,"Generator");c(N,i,function(){return this});c(N,"toString",function(){return"[object Generator]"});function C(e){var t={tryLoc:e[0]};if(1 in e){t.catchLoc=e[1]}if(2 in e){t.finallyLoc=e[2];t.afterLoc=e[3]}this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal";delete t.arg;e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}];e.forEach(C,this);this.reset(true)}e.keys=function(e){var t=Object(e);var r=[];for(var s in t){r.push(s)}r.reverse();return function e(){while(r.length){var s=r.pop();if(s in t){e.value=s;e.done=false;return e}}e.done=true;return e}};function R(e){if(e!=null){var t=e[i];if(t){return t.call(e)}if(typeof e.next==="function"){return e}if(!isNaN(e.length)){var s=-1,a=function t(){while(++s<e.length){if(r.call(e,s)){t.value=e[s];t.done=false;return t}}t.value=n;t.done=true;return t};return a.next=a}}throw new TypeError(typeof e+" is not iterable")}e.values=R;function L(){return{value:n,done:true}}P.prototype={constructor:P,reset:function(e){this.prev=0;this.next=0;this.sent=this._sent=n;this.done=false;this.delegate=null;this.method="next";this.arg=n;this.tryEntries.forEach(I);if(!e){for(var t in this){if(t.charAt(0)==="t"&&r.call(this,t)&&!isNaN(+t.slice(1))){this[t]=n}}}},stop:function(){this.done=true;var e=this.tryEntries[0];var t=e.completion;if(t.type==="throw"){throw t.arg}return this.rval},dispatchException:function(e){if(this.done){throw e}var t=this;function s(r,s){o.type="throw";o.arg=e;t.next=r;if(s){t.method="next";t.arg=n}return!!s}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];var o=i.completion;if(i.tryLoc==="root"){return s("end")}if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc");var c=r.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc){return s(i.catchLoc,true)}else if(this.prev<i.finallyLoc){return s(i.finallyLoc)}}else if(l){if(this.prev<i.catchLoc){return s(i.catchLoc,true)}}else if(c){if(this.prev<i.finallyLoc){return s(i.finallyLoc)}}else{throw new Error("try statement without catch or finally")}}}},abrupt:function(e,t){for(var s=this.tryEntries.length-1;s>=0;--s){var n=this.tryEntries[s];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}if(a&&(e==="break"||e==="continue")&&a.tryLoc<=t&&t<=a.finallyLoc){a=null}var i=a?a.completion:{};i.type=e;i.arg=t;if(a){this.method="next";this.next=a.finallyLoc;return f}return this.complete(i)},complete:function(e,t){if(e.type==="throw"){throw e.arg}if(e.type==="break"||e.type==="continue"){this.next=e.arg}else if(e.type==="return"){this.rval=this.arg=e.arg;this.method="return";this.next="end"}else if(e.type==="normal"&&t){this.next=t}return f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e){this.complete(r.completion,r.afterLoc);I(r);return f}}},"catch":function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var s=r.completion;if(s.type==="throw"){var n=s.arg;I(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){this.delegate={iterator:R(e),resultName:t,nextLoc:r};if(this.method==="next"){this.arg=n}return f}};return e}(true?e.exports:0);try{regeneratorRuntime=t}catch(e){if(typeof globalThis==="object"){globalThis.regeneratorRuntime=t}else{Function("r","regeneratorRuntime = r")(t)}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4934:(e,t,r)=>{"use strict";r.d(t,{$:()=>u});var s=r(687);var n=r.n(s);var a=r(3210);var i=r.n(a);var o=r(1391);var l=r(4224);var c=r(6241);const d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function u({className:e,variant:t,size:r,asChild:n=false,...a}){const i=n?o.DX:"button";return(0,s.jsx)(i,{"data-slot":"button",className:(0,c.cn)(d({variant:t,size:r,className:e})),...a})}},5511:e=>{"use strict";e.exports=require("crypto")},6241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384);var n=r(2348);function a(...e){return(0,n.QP)((0,s.$)(e))}},6723:(e,t,r)=>{Promise.resolve().then(r.bind(r,1079))},7089:(e,t,r)=>{"use strict";r.r(t);r.d(t,{GlobalError:()=>o.a,__next_app__:()=>k,pages:()=>w,routeModule:()=>A,tree:()=>b});var s=r(5239);var n=r.n(s);var a=r(8088);var i=r(8170);var o=r.n(i);var l=r(893);var c=r.n(l);var d={};for(const e in l)if(["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)<0)d[e]=()=>l[e];r.d(t,d);const u=()=>Promise.resolve().then(r.bind(r,8014));const h=()=>Promise.resolve().then(r.t.bind(r,7398,23));const m=()=>Promise.resolve().then(r.t.bind(r,9999,23));const x=()=>Promise.resolve().then(r.t.bind(r,5284,23));const p=()=>Promise.resolve().then(r.bind(r,2528));const f=()=>Promise.resolve().then(r.t.bind(r,7398,23));const g=()=>Promise.resolve().then(r.t.bind(r,9999,23));const v=()=>Promise.resolve().then(r.t.bind(r,5284,23));const y=()=>Promise.resolve().then(r.bind(r,1079));const b={children:["",{children:["(root)",{children:["interview",{children:["__PAGE__",{},{page:[y,"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx"]}]},{}]},{"layout":[p,"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx"],"not-found":[f,"next/dist/client/components/not-found-error"],"forbidden":[g,"next/dist/client/components/forbidden-error"],"unauthorized":[v,"next/dist/client/components/unauthorized-error"]}]},{"layout":[u,"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[h,"next/dist/client/components/not-found-error"],"forbidden":[m,"next/dist/client/components/forbidden-error"],"unauthorized":[x,"next/dist/client/components/unauthorized-error"]}]}.children;const w=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx"];const j=r;const N=()=>Promise.resolve();const k={require:j,loadChunk:N};const A=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(root)/interview/page",pathname:"/interview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:b}})},7206:(e,t,r)=>{"use strict";r.r(t);r.d(t,{"default":()=>eN});var s=r(687);var n=r(3210);var a=r(4934);var i=r(2688);const o=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]];const l=(0,i.A)("arrow-right",o);const c=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."];const d=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."];const u=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."];const h=({candidateName:e="Jonathan",jobTitle:t="Insurance Agent",languages:r=["English","Chinese"],instructions:i=c,environmentChecklist:o=d,disclaimers:h=u,onNext:m})=>{const[x,p]=(0,n.useState)(false);return(0,s.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,s.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,s.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",e,"!"]}),(0,s.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",t,"."]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,s.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:r.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,s.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:i.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,s.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:o.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,s.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:h.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,s.jsx)("input",{type:"checkbox",id:"terms",checked:x,onChange:e=>p(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,s.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)(a.$,{disabled:!x,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>m&&m(),children:["Proceed",(0,s.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})};const m=h;const x=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]];const p=(0,i.A)("circle-check-big",x);const f=[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]];const g=(0,i.A)("mic",f);const v=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]];const y=(0,i.A)("map-pin",v);var b=r(659);const w=()=>{return(0,s.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,s.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,s.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,s.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,s.jsx)(y,{className:"w-4 h-5"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,s.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,s.jsx)(b.A,{className:"w-4 h-5"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,s.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})})};const j=w;class N{constructor(){this.baseUrl="https://interview-server-delta.vercel.app"}async sendInterviewRequest(e){try{const t=await fetch(`${this.baseUrl}/interview`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const e=await t.text();throw new Error(`Interview API Error: ${t.status} ${t.statusText} - ${e}`)}const r=await t.json();return r}catch(e){console.error("Failed to send interview request:",e);throw e}}async startInterview(e,t,r){const s={position:e,name:t,experience:r,history:[]};const n=await this.sendInterviewRequest(s);return n}async continueInterview(e,t,r,s){const n={position:e,name:t,experience:r,history:s};const a=await this.sendInterviewRequest(n);return a}}const k=new N;const A=(0,n.createContext)(undefined);const S=({children:e})=>{const[t,r]=(0,n.useState)("");const[a,i]=(0,n.useState)(false);const[o,l]=(0,n.useState)(false);const[c,d]=(0,n.useState)("Anas ALi");const[u,h]=(0,n.useState)("Software Engineer");const[m,x]=(0,n.useState)(3);const[p,f]=(0,n.useState)(0);const[g,v]=(0,n.useState)(0);const[y,b]=(0,n.useState)(0);const[w,j]=(0,n.useState)([]);const[N,S]=(0,n.useState)(false);const[E,C]=(0,n.useState)(null);const[I,P]=(0,n.useState)([]);const[R,L]=(0,n.useState)(null);const _=(0,n.useCallback)(e=>{P(t=>[...t,e])},[]);const q=(0,n.useCallback)(async()=>{l(true);L(null);try{const e=await k.startInterview(u,c,m);r(e.nextQuestion);f(e.currentQuestionScore);S(e.isInterviewCompleted);if(e.Summary){C(e.Summary)}_({role:"interviewer",content:e.nextQuestion});i(true)}catch(t){const e=t instanceof Error?t.message:"Failed to start interview";L(e);console.error("Failed to start interview:",t)}finally{l(false)}},[u,c,m,_]);const O=(0,n.useCallback)(async e=>{l(true);L(null);try{const t={role:"candidate",content:e};const s=[...I,t];_(t);const n=await k.continueInterview(u,c,m,s);r(n.nextQuestion);f(n.currentQuestionScore);S(n.isInterviewCompleted);if(n.currentQuestionScore>0){v(e=>e+n.currentQuestionScore);b(e=>e+1);j(e=>[...e,n.currentQuestionScore])}if(n.Summary){C(n.Summary)}if(!n.isInterviewCompleted&&n.nextQuestion){_({role:"interviewer",content:n.nextQuestion})}}catch(t){const e=t instanceof Error?t.message:"Failed to submit answer";L(e);console.error("Failed to submit answer:",t)}finally{l(false)}},[u,c,m,I,_]);const T={currentQuestion:t,setCurrentQuestion:r,isInterviewStarted:a,setIsInterviewStarted:i,isLoading:o,setIsLoading:l,candidateName:c,setCandidateName:d,jobTitle:u,setJobTitle:h,experience:m,setExperience:x,currentQuestionScore:p,totalScore:g,questionCount:y,questionScores:w,isInterviewCompleted:N,interviewSummary:E,conversationHistory:I,addToHistory:_,startInterview:q,submitAnswer:O,error:R,setError:L};return(0,s.jsx)(A.Provider,{value:T,children:e})};const E=()=>{const e=(0,n.useContext)(A);if(e===undefined){throw new Error("useInterview must be used within an InterviewProvider")}return e};const C=({className:e})=>{const{conversationHistory:t,isInterviewStarted:r}=E();const n=t.filter(e=>e.role==="interviewer");return(0,s.jsxs)("div",{className:`rounded-2xl bg-white p-4 w-full shadow-sm overflow-y-auto scrollbar-hidden ${e||""}`,children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Video Transcript"}),!r?(0,s.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Interview not started yet"}):n.length===0?(0,s.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Loading questions..."}):(0,s.jsx)("ul",{className:"space-y-4",children:n.map((e,t)=>(0,s.jsxs)("li",{className:"relative flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium bg-[#6938EF] text-white flex-shrink-0",children:t+1}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsx)("p",{className:"text-sm text-gray-800 leading-relaxed",children:e.content})})]},t))})]})};const I=C;const P=({children:e})=>{return(0,s.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:e})};const R=P;const L=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]];const _=(0,i.A)("square",L);var q=r(3105);let O=null;let T=null;if(false){}const F=({onTranscriptChange:e,onFinalTranscript:t,isDisabled:r=false,className:i=""})=>{const[o,l]=(0,n.useState)(false);const[c,d]=(0,n.useState)(false);const u=T();const{transcript:h,listening:m,resetTranscript:x,browserSupportsSpeechRecognition:p,isMicrophoneAvailable:f}=u;(0,n.useEffect)(()=>{if(h){e(h)}},[h,e]);(0,n.useEffect)(()=>{if(c&&!m&&h){t(h);l(false);d(false)}},[m,h,c,t]);(0,n.useEffect)(()=>{if(!m){}},[m,o]);const v=()=>{if(!O||!p||!f||r){return}x();l(true);d(true);O.startListening({continuous:true,language:"en-US"})};const y=()=>{if(!O){return}O.stopListening();l(false);if(h){t(h)}d(false)};const b=()=>{x();e("")};if(!p){return(0,s.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${i}`,children:(0,s.jsx)("p",{className:"text-red-700 text-sm",children:"Your browser doesn't support speech recognition. Please use a modern browser like Chrome, Edge, or Safari."})})}if(!f){return(0,s.jsx)("div",{className:`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${i}`,children:(0,s.jsx)("p",{className:"text-yellow-700 text-sm",children:"Microphone access is required for voice input. Please allow microphone permissions and refresh the page."})})}return(0,s.jsxs)("div",{className:`space-y-4 ${i}`,children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[!o&&(0,s.jsxs)(a.$,{onClick:v,disabled:r,variant:"default",size:"sm",className:"flex items-center gap-2 bg-primary  text-white",children:[(0,s.jsx)(g,{className:"w-4 h-4"}),"Start Recording"]}),o&&(0,s.jsxs)(a.$,{onClick:y,variant:"destructive",size:"sm",className:"flex items-center gap-2 text-red-400",children:[(0,s.jsx)(_,{className:"w-4 h-4 "}),"Stop Recording"]}),o&&(0,s.jsxs)(a.$,{size:"sm",className:"flex items-center gap-2 text-white",children:[(0,s.jsx)(g,{className:"w-4 h-4"}),"Listening"]}),h&&!o&&(0,s.jsx)(a.$,{onClick:b,variant:"outline",size:"sm",className:"flex items-center gap-2",children:"Clear"})]}),h&&(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-gray-800 text-sm leading-relaxed",children:h})})]})};const z=({className:e=""})=>{return(0,s.jsx)("div",{className:`bg-gray-50 border border-gray-200 rounded-lg p-4 ${e}`,children:(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Speech recognition is not available in this environment."})})};const $=e=>{const[t,r]=(0,n.useState)(false);(0,n.useEffect)(()=>{r(true)},[]);if(!t){return(0,s.jsx)("div",{className:`bg-gray-50 border border-gray-200 rounded-lg p-4 ${e.className||""}`,children:(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Initializing voice recognition..."})})}if(T){return(0,s.jsx)(F,{...e})}else{return(0,s.jsx)(z,{...e})}};const M=$;const D=({onNext:e})=>{const{currentQuestion:t,isInterviewStarted:r,isLoading:i,startInterview:o,submitAnswer:c,error:d,isInterviewCompleted:u,interviewSummary:h}=E();const[m,x]=(0,n.useState)(false);const[f,v]=(0,n.useState)("");const[y,b]=(0,n.useState)("voice");const w=async()=>{try{await o();x(true)}catch(e){console.error("Failed to start interview:",e)}};const N=e=>{v(e)};const k=e=>{v(e)};const A=()=>{b(y==="voice"?"text":"voice");v("")};const S=async()=>{if(!f.trim()){alert("Please provide an answer before continuing.");return}try{x(false);await c(f);v("");x(true)}catch(e){console.error("Failed to submit answer:",e);x(true)}};if(!r){return(0,s.jsxs)("div",{className:"h-screen",children:[(0,s.jsx)(j,{}),(0,s.jsxs)(R,{children:[(0,s.jsx)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:(0,s.jsx)(I,{className:"h-[550px]"})}),(0,s.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,s.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:w,children:["Start Interview",(0,s.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,s.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"Ready to begin"})]})]})}if(u){return(0,s.jsxs)("div",{className:"h-screen",children:[(0,s.jsx)(j,{}),(0,s.jsx)(R,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(p,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Interview Completed!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Thank you for completing the interview. Your responses have been recorded."}),h&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Detailed Interview Summary"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Technical Skills:"}),(0,s.jsxs)("span",{className:"font-medium",children:[h.ScoreCard.technicalSkills,"/20"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Problem Solving:"}),(0,s.jsxs)("span",{className:"font-medium",children:[h.ScoreCard.problemSolving,"/20"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Communication:"}),(0,s.jsxs)("span",{className:"font-medium",children:[h.ScoreCard.communication,"/20"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Experience:"}),(0,s.jsxs)("span",{className:"font-medium",children:[h.ScoreCard.experience,"/20"]})]}),(0,s.jsx)("hr",{className:"my-2"}),(0,s.jsxs)("div",{className:"flex justify-between font-semibold text-base",children:[(0,s.jsx)("span",{children:"Total Score:"}),(0,s.jsxs)("span",{children:[h.ScoreCard.overall,"/100"]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${h.recommendation==="HIRE"?"bg-green-100 text-green-800":h.recommendation==="REJECT"?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"}`,children:h.recommendation})}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-2",children:h.reason})]})]})]})]}),(0,s.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>e?.(),children:["View Results",(0,s.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})]})})]})}return(0,s.jsxs)("div",{className:"h-screen",children:[(0,s.jsx)(j,{}),(0,s.jsxs)(R,{children:[(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-10 justify-center items-center lg:items-start",children:[(0,s.jsxs)("div",{className:"flex-1 max-w-2xl",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Question:"}),(0,s.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed",children:t||"Loading question..."}),d&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:["Error: ",d]})]}),m&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Your Answer:"}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)(a.$,{variant:y==="voice"?"default":"outline",size:"sm",onClick:A,className:"flex items-center gap-2 text-white",children:[(0,s.jsx)(g,{className:"w-4 h-4"}),"Voice"]})})]}),y==="voice"?(0,s.jsx)(M,{onTranscriptChange:N,onFinalTranscript:k,isDisabled:i,className:"mb-4"}):(0,s.jsx)("textarea",{value:f,onChange:e=>v(e.target.value),placeholder:"Type your answer here...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4})]})]}),(0,s.jsx)(I,{className:"h-[400px] lg:w-80"})]}),(0,s.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:m&&!i?(0,s.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:S,children:[u?"Finish Interview":"Submit Answer",(0,s.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]}):(0,s.jsx)("div",{className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500",children:i?"Loading question...":"Listen to the question"})})]})]})};const V=D;const Q=()=>{const{conversationHistory:e,currentQuestionScore:t,totalScore:r}=E();return(0,s.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-5",children:[(0,s.jsx)("p",{className:"text-lg font-semibold text-black",children:"Interview Transcript"}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Score: ",r,"/100"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[e.map((r,n)=>(0,s.jsx)("div",{className:"mb-4",children:r.role==="interviewer"?(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm font-semibold text-blue-600 mb-1",children:["Question ",Math.floor(n/2)+1,":"]}),(0,s.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:r.content})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-semibold text-green-600 mb-1",children:"Answer:"}),(0,s.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:r.content}),n===e.length-1&&t>0&&(0,s.jsxs)("p",{className:"text-xs text-blue-500 mt-1",children:["Score: ",t,"/20"]})]})},n)),e.length===0&&(0,s.jsx)("p",{className:"text-sm text-gray-500 italic",children:"Interview transcript will appear here as you progress..."})]})]})};const W=Q;const G=({onNext:e})=>{return(0,s.jsxs)("div",{className:"h-screen",children:[(0,s.jsx)(j,{}),(0,s.jsxs)(R,{children:[(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,s.jsx)(I,{}),(0,s.jsx)(W,{})]}),(0,s.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,s.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>e&&e(),children:["Finish Interview",(0,s.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})};const U=G;var B=r(474);const Y={"src":"/_next/static/media/trophy.73528452.png","height":28,"width":28,"blurDataURL":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC","blurWidth":8,"blurHeight":8};const X=()=>{return(0,s.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,s.jsx)("div",{className:"flex justify-center mb-2",children:(0,s.jsx)(B["default"],{src:Y,alt:"Trophy"})}),(0,s.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,s.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,s.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,s.jsx)("div",{className:"top-0",children:(0,s.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]})};const H=X;const J=({label:e,value:t,color:r="bg-orange-500"})=>{return(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,s.jsx)("span",{className:"mb-1",children:e}),(0,s.jsxs)("span",{children:[t,"/100"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,s.jsx)("div",{className:`h-2.5 rounded-full ${r}`,style:{width:`${t}%`}})})]})};const Z=J;var K=function(e,t){K=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)if(t.hasOwnProperty(r))e[r]=t[r]};return K(e,t)};function ee(e,t){K(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var et=function(){et=Object.assign||function e(e){for(var t,r=1,s=arguments.length;r<s;r++){t=arguments[r];for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n))e[n]=t[n]}return e};return et.apply(this,arguments)};function er(e,t){var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0)r[s]=e[s];if(e!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)if(t.indexOf(s[n])<0)r[s[n]]=e[s[n]]}return r}var es=100;var en=100;var ea=50;var ei=50;var eo=50;function el(e){var t=e.className,r=e.counterClockwise,s=e.dashRatio,a=e.pathRadius,i=e.strokeWidth,o=e.style;return(0,n.createElement)("path",{className:t,style:Object.assign({},o,ed({pathRadius:a,dashRatio:s,counterClockwise:r})),d:ec({pathRadius:a,counterClockwise:r}),strokeWidth:i,fillOpacity:0})}function ec(e){var t=e.pathRadius,r=e.counterClockwise;var s=t;var n=r?1:0;return"\n      M "+ei+","+eo+"\n      m 0,-"+s+"\n      a "+s+","+s+" "+n+" 1 1 0,"+2*s+"\n      a "+s+","+s+" "+n+" 1 1 0,-"+2*s+"\n    "}function ed(e){var t=e.counterClockwise,r=e.dashRatio,s=e.pathRadius;var n=Math.PI*2*s;var a=(1-r)*n;return{strokeDasharray:n+"px "+n+"px",strokeDashoffset:(t?-a:a)+"px"}}var eu=function(e){ee(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}t.prototype.getBackgroundPadding=function(){if(!this.props.background){return 0}return this.props.backgroundPadding};t.prototype.getPathRadius=function(){return ea-this.props.strokeWidth/2-this.getBackgroundPadding()};t.prototype.getPathRatio=function(){var e=this.props,t=e.value,r=e.minValue,s=e.maxValue;var n=Math.min(Math.max(t,r),s);return(n-r)/(s-r)};t.prototype.render=function(){var e=this.props,t=e.circleRatio,r=e.className,s=e.classes,a=e.counterClockwise,i=e.styles,o=e.strokeWidth,l=e.text;var c=this.getPathRadius();var d=this.getPathRatio();return(0,n.createElement)("svg",{className:s.root+" "+r,style:i.root,viewBox:"0 0 "+es+" "+en,"data-test-id":"CircularProgressbar"},this.props.background?(0,n.createElement)("circle",{className:s.background,style:i.background,cx:ei,cy:eo,r:ea}):null,(0,n.createElement)(el,{className:s.trail,counterClockwise:a,dashRatio:t,pathRadius:c,strokeWidth:o,style:i.trail}),(0,n.createElement)(el,{className:s.path,counterClockwise:a,dashRatio:d*t,pathRadius:c,strokeWidth:o,style:i.path}),l?(0,n.createElement)("text",{className:s.text,style:i.text,x:ei,y:eo},l):null)};t.defaultProps={background:false,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:false,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""};return t}(n.Component);function eh(e){var t=e.children,r=er(e,["children"]);return createElement("div",{"data-test-id":"CircularProgressbarWithChildren"},createElement("div",{style:{position:"relative",width:"100%",height:"100%"}},createElement(eu,et({},r)),e.children?createElement("div",{"data-test-id":"CircularProgressbarWithChildren__children",style:{position:"absolute",width:"100%",height:"100%",marginTop:"-100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}},e.children):null))}function em(e){var t=e.rotation,r=e.strokeLinecap,s=e.textColor,n=e.textSize,a=e.pathColor,i=e.pathTransition,o=e.pathTransitionDuration,l=e.trailColor,c=e.backgroundColor;var d=t==null?undefined:"rotate("+t+"turn)";var u=t==null?undefined:"center center";return{root:{},path:ex({stroke:a,strokeLinecap:r,transform:d,transformOrigin:u,transition:i,transitionDuration:o==null?undefined:o+"s"}),trail:ex({stroke:l,strokeLinecap:r,transform:d,transformOrigin:u}),text:ex({fill:s,fontSize:n}),background:ex({fill:c})}}function ex(e){Object.keys(e).forEach(function(t){if(e[t]==null){delete e[t]}});return e}var ep=r(9587);const ef=({label:e,percent:t,color:r,trailColor:n})=>{return(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,s.jsx)("p",{className:"text-sm font-semibold mb-3",children:e}),(0,s.jsx)("div",{className:"w-32 h-28",children:(0,s.jsx)(eu,{value:t,text:`${t}%`,strokeWidth:10,styles:em({textSize:"12px",pathColor:r,textColor:"#5a5a5a",trailColor:n})})})]})};const eg=ef;const ev=()=>{return(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,s.jsx)("span",{children:"Resume Score"}),(0,s.jsx)("span",{children:"65%"})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(Z,{label:"Company Fit",value:66}),(0,s.jsx)(Z,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,s.jsx)(Z,{label:"Job Knowledge",value:66}),(0,s.jsx)(Z,{label:"Education",value:66}),(0,s.jsx)(Z,{label:"Hard Skills",value:66})]}),(0,s.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,s.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,s.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(Z,{label:"Professionalism",value:64}),(0,s.jsx)(Z,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,s.jsx)(Z,{label:"Communication",value:58}),(0,s.jsx)(Z,{label:"Sociability",value:70})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,s.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,s.jsx)(eg,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,s.jsx)(eg,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]})};const ey=ev;const eb=()=>{return(0,s.jsxs)("div",{className:"h-screen",children:[(0,s.jsx)(H,{}),(0,s.jsx)(R,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,s.jsx)(I,{}),(0,s.jsx)(W,{})]})}),(0,s.jsx)(ey,{})]})};const ew=eb;const ej=()=>{const[e,t]=(0,n.useState)("instructions");const r=()=>{switch(e){case"instructions":return(0,s.jsx)(m,{onNext:()=>t("questions")});case"questions":return(0,s.jsx)(V,{onNext:()=>t("finishInterview")});case"finishInterview":return(0,s.jsx)(U,{onNext:()=>t("analysis")});case"analysis":return(0,s.jsx)(ew,{});default:return(0,s.jsx)(m,{onNext:()=>t("questions")})}};return(0,s.jsx)(S,{children:(0,s.jsx)("div",{children:r()})})};const eN=ej},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9587:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e);var s=t.X(0,[97,423,598,23,814,762],()=>r(7089));module.exports=s})();