(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[220],{2257:(e,s,l)=>{"use strict";l.r(s);l.d(s,{"default":()=>n});var a=l(5155);var r=l(2115);var i=l(6766);var d=l(9858);var m=l(3582);const c=()=>{const e={email:"Enter your email address",name:"Enter your full name",password:"Enter your password",confirmPassword:"Confirm your password"};return(0,a.jsxs)("div",{className:"min-h-screen lg:h-[150vh] lg:relative",children:[(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)(i["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"relative w-full h-60 overflow-hidden rounded-b-4xl",children:(0,a.jsx)(i["default"],{src:"/images/bgauthimg.png",alt:"auth",fill:true,className:"object-cover",priority:true})}),(0,a.jsx)("div",{className:"bg-white px-4 sm:px-6 py-8",children:(0,a.jsx)("div",{className:"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl",children:(0,a.jsx)(d.A,{formType:"SIGN_UP",schema:m.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:true,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]",children:(0,a.jsx)("div",{className:"bg-white lg:rounded-l-4xl h-full",children:(0,a.jsx)("div",{className:"flex justify-center px-4 sm:px-6 py-13 h-full",children:(0,a.jsx)("div",{className:"w-full max-w-md",children:(0,a.jsx)(d.A,{formType:"SIGN_UP",schema:m.mJ,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:true,data:e}),heading:"Sign Up - For Applicants",placeholderValues:e})})})})})]})};const n=c},5217:(e,s,l)=>{Promise.resolve().then(l.bind(l,2257))}},e=>{var s=s=>e(e.s=s);e.O(0,[766,831,903,671,874,421,441,684,358],()=>s(5217));var l=e.O();_N_E=l}]);