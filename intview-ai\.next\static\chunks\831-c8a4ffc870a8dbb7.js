"use strict";(self["webpackChunk_N_E"]=self["webpackChunk_N_E"]||[]).push([[831],{2085:(e,o,r)=>{r.d(o,{F:()=>a});var t=r(2596);const n=e=>typeof e==="boolean"?`${e}`:e===0?"0":e;const s=t.$;const a=(e,o)=>r=>{var t;if((o===null||o===void 0?void 0:o.variants)==null)return s(e,r===null||r===void 0?void 0:r.class,r===null||r===void 0?void 0:r.className);const{variants:a,defaultVariants:l}=o;const i=Object.keys(a).map(e=>{const o=r===null||r===void 0?void 0:r[e];const t=l===null||l===void 0?void 0:l[e];if(o===null)return null;const s=n(o)||n(t);return a[e][s]});const c=r&&Object.entries(r).reduce((e,o)=>{let[r,t]=o;if(t===undefined){return e}e[r]=t;return e},{});const d=o===null||o===void 0?void 0:(t=o.compoundVariants)===null||t===void 0?void 0:t.reduce((e,o)=>{let{class:r,className:t,...n}=o;return Object.entries(n).every(e=>{let[o,r]=e;return Array.isArray(r)?r.includes({...l,...c}[o]):({...l,...c})[o]===r})?[...e,r,t]:e},[]);return s(e,i,d,r===null||r===void 0?void 0:r.class,r===null||r===void 0?void 0:r.className)}},2596:(e,o,r)=>{r.d(o,{$:()=>n});function t(e){var o,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(o=0;o<s;o++)e[o]&&(r=t(e[o]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function n(){for(var e,o,r=0,n="",s=arguments.length;r<s;r++)(e=arguments[r])&&(o=t(e))&&(n&&(n+=" "),n+=o);return n}var s=null&&n},4624:(e,o,r)=>{r.d(o,{DX:()=>c,TL:()=>i});var t=r(2115);function n(e,o){if(typeof e==="function"){return e(o)}else if(e!==null&&e!==void 0){e.current=o}}function s(...e){return o=>{let r=false;const t=e.map(e=>{const t=n(e,o);if(!r&&typeof t=="function"){r=true}return t});if(r){return()=>{for(let o=0;o<t.length;o++){const r=t[o];if(typeof r=="function"){r()}else{n(e[o],null)}}}}}}function a(...e){return React.useCallback(s(...e),e)}var l=r(5155);function i(e){const o=d(e);const r=t.forwardRef((e,r)=>{const{children:n,...s}=e;const a=t.Children.toArray(n);const i=a.find(f);if(i){const e=i.props.children;const n=a.map(o=>{if(o===i){if(t.Children.count(e)>1)return t.Children.only(null);return t.isValidElement(e)?e.props.children:null}else{return o}});return(0,l.jsx)(o,{...s,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,n):null})}return(0,l.jsx)(o,{...s,ref:r,children:n})});r.displayName=`${e}.Slot`;return r}var c=i("Slot");function d(e){const o=t.forwardRef((e,o)=>{const{children:r,...n}=e;if(t.isValidElement(r)){const e=g(r);const a=b(n,r.props);if(r.type!==t.Fragment){a.ref=o?s(o,e):e}return t.cloneElement(r,a)}return t.Children.count(r)>1?t.Children.only(null):null});o.displayName=`${e}.SlotClone`;return o}var m=Symbol("radix.slottable");function u(e){const o=({children:e})=>{return jsx(Fragment2,{children:e})};o.displayName=`${e}.Slottable`;o.__radixId=m;return o}var p=null&&u("Slottable");function f(e){return t.isValidElement(e)&&typeof e.type==="function"&&"__radixId"in e.type&&e.type.__radixId===m}function b(e,o){const r={...o};for(const t in o){const n=e[t];const s=o[t];const a=/^on[A-Z]/.test(t);if(a){if(n&&s){r[t]=(...e)=>{const o=s(...e);n(...e);return o}}else if(n){r[t]=n}}else if(t==="style"){r[t]={...n,...s}}else if(t==="className"){r[t]=[n,s].filter(Boolean).join(" ")}}return{...e,...r}}function g(e){let o=Object.getOwnPropertyDescriptor(e.props,"ref")?.get;let r=o&&"isReactWarning"in o&&o.isReactWarning;if(r){return e.ref}o=Object.getOwnPropertyDescriptor(e,"ref")?.get;r=o&&"isReactWarning"in o&&o.isReactWarning;if(r){return e.props.ref}return e.props.ref||e.ref}},9688:(e,o,r)=>{r.d(o,{QP:()=>ev});const t="-";const n=e=>{const o=i(e);const{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;const a=e=>{const r=e.split(t);if(r[0]===""&&r.length!==1){r.shift()}return s(r,o)||l(e)};const c=(e,o)=>{const t=r[e]||[];if(o&&n[e]){return[...t,...n[e]]}return t};return{getClassGroupId:a,getConflictingClassGroupIds:c}};const s=(e,o)=>{if(e.length===0){return o.classGroupId}const r=e[0];const n=o.nextPart.get(r);const a=n?s(e.slice(1),n):undefined;if(a){return a}if(o.validators.length===0){return undefined}const l=e.join(t);return o.validators.find(({validator:e})=>e(l))?.classGroupId};const a=/^\[(.+)\]$/;const l=e=>{if(a.test(e)){const o=a.exec(e)[1];const r=o?.substring(0,o.indexOf(":"));if(r){return"arbitrary.."+r}}};const i=e=>{const{theme:o,classGroups:r}=e;const t={nextPart:new Map,validators:[]};for(const e in r){c(r[e],t,e,o)}return t};const c=(e,o,r,t)=>{e.forEach(e=>{if(typeof e==="string"){const t=e===""?o:d(o,e);t.classGroupId=r;return}if(typeof e==="function"){if(m(e)){c(e(t),o,r,t);return}o.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{c(n,d(o,e),r,t)})})};const d=(e,o)=>{let r=e;o.split(t).forEach(e=>{if(!r.nextPart.has(e)){r.nextPart.set(e,{nextPart:new Map,validators:[]})}r=r.nextPart.get(e)});return r};const m=e=>e.isThemeGetter;const u=e=>{if(e<1){return{get:()=>undefined,set:()=>{}}}let o=0;let r=new Map;let t=new Map;const n=(n,s)=>{r.set(n,s);o++;if(o>e){o=0;t=r;r=new Map}};return{get(e){let o=r.get(e);if(o!==undefined){return o}if((o=t.get(e))!==undefined){n(e,o);return o}},set(e,o){if(r.has(e)){r.set(e,o)}else{n(e,o)}}}};const p="!";const f=":";const b=f.length;const g=e=>{const{prefix:o,experimentalParseClassName:r}=e;let t=e=>{const o=[];let r=0;let t=0;let n=0;let s;for(let a=0;a<e.length;a++){let l=e[a];if(r===0&&t===0){if(l===f){o.push(e.slice(n,a));n=a+b;continue}if(l==="/"){s=a;continue}}if(l==="["){r++}else if(l==="]"){r--}else if(l==="("){t++}else if(l===")"){t--}}const a=o.length===0?e:e.substring(n);const l=h(a);const i=l!==a;const c=s&&s>n?s-n:undefined;return{modifiers:o,hasImportantModifier:i,baseClassName:l,maybePostfixModifierPosition:c}};if(o){const e=o+f;const r=t;t=o=>o.startsWith(e)?r(o.substring(e.length)):{isExternal:true,modifiers:[],hasImportantModifier:false,baseClassName:o,maybePostfixModifierPosition:undefined}}if(r){const e=t;t=o=>r({className:o,parseClassName:e})}return t};const h=e=>{if(e.endsWith(p)){return e.substring(0,e.length-1)}if(e.startsWith(p)){return e.substring(1)}return e};const k=e=>{const o=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,true]));const r=e=>{if(e.length<=1){return e}const r=[];let t=[];e.forEach(e=>{const n=e[0]==="["||o[e];if(n){r.push(...t.sort(),e);t=[]}else{t.push(e)}});r.push(...t.sort());return r};return r};const x=e=>({cache:u(e.cacheSize),parseClassName:g(e),sortModifiers:k(e),...n(e)});const w=/\s+/;const y=(e,o)=>{const{parseClassName:r,getClassGroupId:t,getConflictingClassGroupIds:n,sortModifiers:s}=o;const a=[];const l=e.trim().split(w);let i="";for(let e=l.length-1;e>=0;e-=1){const o=l[e];const{isExternal:c,modifiers:d,hasImportantModifier:m,baseClassName:u,maybePostfixModifierPosition:f}=r(o);if(c){i=o+(i.length>0?" "+i:i);continue}let b=!!f;let g=t(b?u.substring(0,f):u);if(!g){if(!b){i=o+(i.length>0?" "+i:i);continue}g=t(u);if(!g){i=o+(i.length>0?" "+i:i);continue}b=false}const h=s(d).join(":");const k=m?h+p:h;const x=k+g;if(a.includes(x)){continue}a.push(x);const w=n(g,b);for(let e=0;e<w.length;++e){const o=w[e];a.push(k+o)}i=o+(i.length>0?" "+i:i)}return i};function v(){let e=0;let o;let r;let t="";while(e<arguments.length){if(o=arguments[e++]){if(r=z(o)){t&&(t+=" ");t+=r}}}return t}const z=e=>{if(typeof e==="string"){return e}let o;let r="";for(let t=0;t<e.length;t++){if(e[t]){if(o=z(e[t])){r&&(r+=" ");r+=o}}}return r};function j(e,...o){let r;let t;let n;let s=a;function a(a){const i=o.reduce((e,o)=>o(e),e());r=x(i);t=r.cache.get;n=r.cache.set;s=l;return l(a)}function l(e){const o=t(e);if(o){return o}const s=y(e,r);n(e,s);return s}return function e(){return s(v.apply(null,arguments))}}const C=e=>{const o=o=>o[e]||[];o.isThemeGetter=true;return o};const G=/^\[(?:(\w[\w-]*):)?(.+)\]$/i;const M=/^\((?:(\w[\w-]*):)?(.+)\)$/i;const N=/^\d+\/\d+$/;const S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/;const _=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/;const E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/;const $=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;const P=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;const O=e=>N.test(e);const I=e=>!!e&&!Number.isNaN(Number(e));const W=e=>!!e&&Number.isInteger(Number(e));const R=e=>e.endsWith("%")&&I(e.slice(0,-1));const A=e=>S.test(e);const V=()=>true;const T=e=>_.test(e)&&!E.test(e);const D=()=>false;const F=e=>$.test(e);const q=e=>P.test(e);const B=e=>!Q(e)&&!U(e);const L=e=>es(e,ec,D);const Q=e=>G.test(e);const X=e=>es(e,ed,T);const Z=e=>es(e,em,I);const H=e=>es(e,el,D);const J=e=>es(e,ei,q);const K=e=>es(e,ep,F);const U=e=>M.test(e);const Y=e=>ea(e,ed);const ee=e=>ea(e,eu);const eo=e=>ea(e,el);const er=e=>ea(e,ec);const et=e=>ea(e,ei);const en=e=>ea(e,ep,true);const es=(e,o,r)=>{const t=G.exec(e);if(t){if(t[1]){return o(t[1])}return r(t[2])}return false};const ea=(e,o,r=false)=>{const t=M.exec(e);if(t){if(t[1]){return o(t[1])}return r}return false};const el=e=>e==="position"||e==="percentage";const ei=e=>e==="image"||e==="url";const ec=e=>e==="length"||e==="size"||e==="bg-size";const ed=e=>e==="length";const em=e=>e==="number";const eu=e=>e==="family-name";const ep=e=>e==="shadow";const ef=Object.defineProperty({__proto__:null,isAny:V,isAnyNonArbitrary:B,isArbitraryImage:J,isArbitraryLength:X,isArbitraryNumber:Z,isArbitraryPosition:H,isArbitraryShadow:K,isArbitrarySize:L,isArbitraryValue:Q,isArbitraryVariable:U,isArbitraryVariableFamilyName:ee,isArbitraryVariableImage:et,isArbitraryVariableLength:Y,isArbitraryVariablePosition:eo,isArbitraryVariableShadow:en,isArbitraryVariableSize:er,isFraction:O,isInteger:W,isNumber:I,isPercent:R,isTshirtSize:A},Symbol.toStringTag,{value:"Module"});const eb=()=>{const e=C("color");const o=C("font");const r=C("text");const t=C("font-weight");const n=C("tracking");const s=C("leading");const a=C("breakpoint");const l=C("container");const i=C("spacing");const c=C("radius");const d=C("shadow");const m=C("inset-shadow");const u=C("text-shadow");const p=C("drop-shadow");const f=C("blur");const b=C("perspective");const g=C("aspect");const h=C("ease");const k=C("animate");const x=()=>["auto","avoid","all","avoid-page","page","left","right","column"];const w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"];const y=()=>[...w(),U,Q];const v=()=>["auto","hidden","clip","visible","scroll"];const z=()=>["auto","contain","none"];const j=()=>[U,Q,i];const G=()=>[O,"full","auto",...j()];const M=()=>[W,"none","subgrid",U,Q];const N=()=>["auto",{span:["full",W,U,Q]},W,U,Q];const S=()=>[W,"auto",U,Q];const _=()=>["auto","min","max","fr",U,Q];const E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"];const $=()=>["start","end","center","stretch","center-safe","end-safe"];const P=()=>["auto",...j()];const T=()=>[O,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...j()];const D=()=>[e,U,Q];const F=()=>[...w(),eo,H,{position:[U,Q]}];const q=()=>["no-repeat",{repeat:["","x","y","space","round"]}];const es=()=>["auto","cover","contain",er,L,{size:[U,Q]}];const ea=()=>[R,Y,X];const el=()=>["","none","full",c,U,Q];const ei=()=>["",I,Y,X];const ec=()=>["solid","dashed","dotted","double"];const ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"];const em=()=>[I,R,eo,H];const eu=()=>["","none",f,U,Q];const ep=()=>["none",I,U,Q];const ef=()=>["none",I,U,Q];const eb=()=>[I,U,Q];const eg=()=>[O,"full",...j()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[V],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",I],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",O,Q,U,g]}],container:["container"],columns:[{columns:[I,Q,U,l]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:v()}],"overflow-x":[{"overflow-x":v()}],"overflow-y":[{"overflow-y":v()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:G()}],"inset-x":[{"inset-x":G()}],"inset-y":[{"inset-y":G()}],start:[{start:G()}],end:[{end:G()}],top:[{top:G()}],right:[{right:G()}],bottom:[{bottom:G()}],left:[{left:G()}],visibility:["visible","invisible","collapse"],z:[{z:[W,"auto",U,Q]}],basis:[{basis:[O,"full","auto",l,...j()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[I,O,"auto","initial","none",Q]}],grow:[{grow:["",I,U,Q]}],shrink:[{shrink:["",I,U,Q]}],order:[{order:[W,"first","last","none",U,Q]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:N()}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:N()}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":_()}],"auto-rows":[{"auto-rows":_()}],gap:[{gap:j()}],"gap-x":[{"gap-x":j()}],"gap-y":[{"gap-y":j()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...$(),"normal"]}],"justify-self":[{"justify-self":["auto",...$()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...$(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...$(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...$(),"baseline"]}],"place-self":[{"place-self":["auto",...$()]}],p:[{p:j()}],px:[{px:j()}],py:[{py:j()}],ps:[{ps:j()}],pe:[{pe:j()}],pt:[{pt:j()}],pr:[{pr:j()}],pb:[{pb:j()}],pl:[{pl:j()}],m:[{m:P()}],mx:[{mx:P()}],my:[{my:P()}],ms:[{ms:P()}],me:[{me:P()}],mt:[{mt:P()}],mr:[{mr:P()}],mb:[{mb:P()}],ml:[{ml:P()}],"space-x":[{"space-x":j()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":j()}],"space-y-reverse":["space-y-reverse"],size:[{size:T()}],w:[{w:[l,"screen",...T()]}],"min-w":[{"min-w":[l,"screen","none",...T()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...T()]}],h:[{h:["screen","lh",...T()]}],"min-h":[{"min-h":["screen","lh","none",...T()]}],"max-h":[{"max-h":["screen","lh",...T()]}],"font-size":[{text:["base",r,Y,X]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,U,Z]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,Q]}],"font-family":[{font:[ee,Q,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,U,Q]}],"line-clamp":[{"line-clamp":[I,"none",U,Z]}],leading:[{leading:[s,...j()]}],"list-image":[{"list-image":["none",U,Q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",U,Q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[I,"from-font","auto",U,X]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[I,"auto",U,Q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:j()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",U,Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",U,Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:F()}],"bg-repeat":[{bg:q()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},W,U,Q],radial:["",U,Q],conic:[W,U,Q]},et,J]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[I,U,Q]}],"outline-w":[{outline:["",I,Y,X]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",d,en,K]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",m,en,K]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[I,X]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",u,en,K]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[I,U,Q]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[I]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[U,Q]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[I]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:F()}],"mask-repeat":[{mask:q()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",U,Q]}],filter:[{filter:["","none",U,Q]}],blur:[{blur:eu()}],brightness:[{brightness:[I,U,Q]}],contrast:[{contrast:[I,U,Q]}],"drop-shadow":[{"drop-shadow":["","none",p,en,K]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",I,U,Q]}],"hue-rotate":[{"hue-rotate":[I,U,Q]}],invert:[{invert:["",I,U,Q]}],saturate:[{saturate:[I,U,Q]}],sepia:[{sepia:["",I,U,Q]}],"backdrop-filter":[{"backdrop-filter":["","none",U,Q]}],"backdrop-blur":[{"backdrop-blur":eu()}],"backdrop-brightness":[{"backdrop-brightness":[I,U,Q]}],"backdrop-contrast":[{"backdrop-contrast":[I,U,Q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",I,U,Q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[I,U,Q]}],"backdrop-invert":[{"backdrop-invert":["",I,U,Q]}],"backdrop-opacity":[{"backdrop-opacity":[I,U,Q]}],"backdrop-saturate":[{"backdrop-saturate":[I,U,Q]}],"backdrop-sepia":[{"backdrop-sepia":["",I,U,Q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":j()}],"border-spacing-x":[{"border-spacing-x":j()}],"border-spacing-y":[{"border-spacing-y":j()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",U,Q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[I,"initial",U,Q]}],ease:[{ease:["linear","initial",h,U,Q]}],delay:[{delay:[I,U,Q]}],animate:[{animate:["none",k,U,Q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,U,Q]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[U,Q,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",U,Q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":j()}],"scroll-mx":[{"scroll-mx":j()}],"scroll-my":[{"scroll-my":j()}],"scroll-ms":[{"scroll-ms":j()}],"scroll-me":[{"scroll-me":j()}],"scroll-mt":[{"scroll-mt":j()}],"scroll-mr":[{"scroll-mr":j()}],"scroll-mb":[{"scroll-mb":j()}],"scroll-ml":[{"scroll-ml":j()}],"scroll-p":[{"scroll-p":j()}],"scroll-px":[{"scroll-px":j()}],"scroll-py":[{"scroll-py":j()}],"scroll-ps":[{"scroll-ps":j()}],"scroll-pe":[{"scroll-pe":j()}],"scroll-pt":[{"scroll-pt":j()}],"scroll-pr":[{"scroll-pr":j()}],"scroll-pb":[{"scroll-pb":j()}],"scroll-pl":[{"scroll-pl":j()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",U,Q]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[I,Y,X,Z]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}};const eg=(e,{cacheSize:o,prefix:r,experimentalParseClassName:t,extend:n={},override:s={}})=>{eh(e,"cacheSize",o);eh(e,"prefix",r);eh(e,"experimentalParseClassName",t);ek(e.theme,s.theme);ek(e.classGroups,s.classGroups);ek(e.conflictingClassGroups,s.conflictingClassGroups);ek(e.conflictingClassGroupModifiers,s.conflictingClassGroupModifiers);eh(e,"orderSensitiveModifiers",s.orderSensitiveModifiers);ex(e.theme,n.theme);ex(e.classGroups,n.classGroups);ex(e.conflictingClassGroups,n.conflictingClassGroups);ex(e.conflictingClassGroupModifiers,n.conflictingClassGroupModifiers);ew(e,n,"orderSensitiveModifiers");return e};const eh=(e,o,r)=>{if(r!==undefined){e[o]=r}};const ek=(e,o)=>{if(o){for(const r in o){eh(e,r,o[r])}}};const ex=(e,o)=>{if(o){for(const r in o){ew(e,o,r)}}};const ew=(e,o,r)=>{const t=o[r];if(t!==undefined){e[r]=e[r]?e[r].concat(t):t}};const ey=(e,...o)=>typeof e==="function"?j(eb,e,...o):j(()=>eg(eb(),e),...o);const ev=j(eb)}}]);