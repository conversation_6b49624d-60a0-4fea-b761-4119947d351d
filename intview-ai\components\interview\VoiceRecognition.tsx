"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mic, Square } from "lucide-react";

// Import regenerator-runtime for async/await support
import "regenerator-runtime/runtime";

// TypeScript declarations for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

// Dynamic import to avoid SSR issues
let SpeechRecognition: any = null;
let useSpeechRecognition: any = null;

if (typeof window !== "undefined") {
  const speechRecognitionModule = require("react-speech-recognition");
  SpeechRecognition = speechRecognitionModule.default;
  useSpeechRecognition = speechRecognitionModule.useSpeechRecognition;
}

interface VoiceRecognitionProps {
  onTranscriptChange: (transcript: string) => void;
  onFinalTranscript: (transcript: string) => void;
  isDisabled?: boolean;
  className?: string;
}

// Create a wrapper component that handles client-side rendering
const VoiceRecognitionClient: React.FC<VoiceRecognitionProps> = ({
  onTranscriptChange,
  onFinalTranscript,
  isDisabled = false,
  className = "",
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);

  // Always call the hook, but handle the case where it's not available
  let speechRecognitionData;
  try {
    speechRecognitionData = useSpeechRecognition ? useSpeechRecognition() : {
      transcript: "",
      listening: false,
      resetTranscript: () => {},
      browserSupportsSpeechRecognition: false,
      isMicrophoneAvailable: false,
    };
  } catch (error) {
    speechRecognitionData = {
      transcript: "",
      listening: false,
      resetTranscript: () => {},
      browserSupportsSpeechRecognition: false,
      isMicrophoneAvailable: false,
    };
  }

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = speechRecognitionData;

  // Update parent component with transcript changes
  useEffect(() => {
    if (transcript) {
      onTranscriptChange(transcript);
    }
  }, [transcript, onTranscriptChange]);

  // Handle when speech recognition stops
  useEffect(() => {
    if (hasStarted && !listening && transcript) {
      onFinalTranscript(transcript);
      setIsRecording(false);
      setHasStarted(false);
    }
  }, [listening, transcript, hasStarted, onFinalTranscript]);

  // Simple error handling for speech recognition
  useEffect(() => {
    if (!listening) return;
  }, [listening, isRecording]);

  const startListening = () => {
    if (!SpeechRecognition || !browserSupportsSpeechRecognition || !isMicrophoneAvailable || isDisabled) {
      return;
    }

    resetTranscript();
    setIsRecording(true);
    setHasStarted(true);

    SpeechRecognition.startListening({
      continuous: true,
      language: "en-US",
    });
  };

  const stopListening = () => {
    if (!SpeechRecognition) {
      return;
    }

    SpeechRecognition.stopListening();
    setIsRecording(false);

    // Trigger final transcript if we have content
    if (transcript) {
      onFinalTranscript(transcript);
    }
    setHasStarted(false);
  };

  const clearTranscript = () => {
    resetTranscript();
    onTranscriptChange("");
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <p className="text-red-700 text-sm">
          Your browser doesn't support speech recognition. Please use a modern browser like Chrome, Edge, or Safari.
        </p>
      </div>
    );
  }

  if (!isMicrophoneAvailable) {
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
        <p className="text-yellow-700 text-sm">
          Microphone access is required for voice input. Please allow microphone permissions and refresh the page.
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Voice Controls */}
      <div className="flex items-center gap-3">
        {!isRecording && (
          <Button
            onClick={startListening}
            disabled={isDisabled}
            variant="default"
            size="sm"
            className="flex items-center gap-2 bg-primary  text-white"
          >
            <Mic className="w-4 h-4" />
            Start Recording
          </Button>
        )}
        {isRecording && (
           <Button
             onClick={stopListening}
             variant="destructive"
             size="sm"
             className="flex items-center gap-2 text-red-400"
           >
             <Square className="w-4 h-4 " />
             Stop Recording
           </Button>
           
        )}
        {isRecording && (
           <Button
             size="sm"
             className="flex items-center gap-2 text-white"
           >
           <Mic className="w-4 h-4" />
          Listening
           </Button>
           
        )}

        

        {transcript && !isRecording && (
          <Button
            onClick={clearTranscript}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            Clear
          </Button>
        )}
      </div>

      

      {/* Live Transcript Display */}
      {transcript && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <p className="text-gray-800 text-sm leading-relaxed">
            {transcript}
          </p>
        </div>
      )}
    </div>
  );
};

// Main component that handles client-side rendering
const VoiceRecognition: React.FC<VoiceRecognitionProps> = (props) => {
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Show loading state while initializing on client side
  if (!isClient) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${props.className || ""}`}>
        <p className="text-gray-600 text-sm">Initializing voice recognition...</p>
      </div>
    );
  }

  // Render the actual voice recognition component on client side
  return <VoiceRecognitionClient {...props} />;
};

export default VoiceRecognition;
